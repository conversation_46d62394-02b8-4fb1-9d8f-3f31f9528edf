/*
 Navicat Premium Data Transfer

 Source Server         : *************-3307-test
 Source Server Type    : MySQL
 Source Server Version : 50722
 Source Host           : *************:3307
 Source Schema         : admin_template

 Target Server Type    : MySQL
 Target Server Version : 50722
 File Encoding         : 65001

 Date: 14/02/2022 12:09:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for code_column_config
-- ----------------------------
DROP TABLE IF EXISTS `code_column_config`;
CREATE TABLE `code_column_config` (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名',
  `table_schema` varchar(255) DEFAULT NULL COMMENT '数据库名',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名',
  `column_type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `column_default` longtext COMMENT '字段默认值',
  `extra` varchar(255) DEFAULT NULL,
  `form_show` bit(1) DEFAULT NULL COMMENT '字段是否在表单显示',
  `create_show` bit(1) DEFAULT NULL COMMENT '字段是否在新增表单显示',
  `edit_show` bit(1) DEFAULT NULL COMMENT '字段是否在编辑表单显示',
  `copy_show` bit(1) DEFAULT NULL COMMENT '字段是否在复制表单显示',
  `retrieve_show` bit(1) DEFAULT NULL COMMENT '字段是否在详情表单显示',
  `form_type` varchar(255) DEFAULT NULL COMMENT '字段在表单的输入框类型',
  `filter_type` varchar(255) DEFAULT NULL COMMENT '字段在筛选器的输入框类型',
  `list_show` bit(1) DEFAULT NULL COMMENT '是否在列表显示',
  `not_null` bit(1) DEFAULT NULL COMMENT '是否允许为null',
  `query_type` varchar(255) DEFAULT NULL COMMENT '字段的查询类型，如=、!=、like',
  `key_remark` varchar(255) DEFAULT NULL COMMENT '国际化key',
  `remark` varchar(255) DEFAULT NULL COMMENT '国际化中文value',
  `en_remark` varchar(255) DEFAULT NULL COMMENT '国际化英文value',
  `date_annotation` varchar(255) DEFAULT NULL COMMENT '日期注解：创建时自动更新时间、编辑时自动更新时间',
  `is_date` bit(1) DEFAULT NULL COMMENT '字段是否为日期',
  `is_unique` bit(1) DEFAULT NULL COMMENT '字段是否为unique',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=331 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='代码生成字段信息存储';

SET FOREIGN_KEY_CHECKS = 1;
