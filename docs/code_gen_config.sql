/*
 Navicat Premium Data Transfer

 Source Server         : *************-3307-test
 Source Server Type    : MySQL
 Source Server Version : 50722
 Source Host           : *************:3307
 Source Schema         : admin_template

 Target Server Type    : MySQL
 Target Server Version : 50722
 File Encoding         : 65001

 Date: 14/02/2022 12:09:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for code_gen_config
-- ----------------------------
DROP TABLE IF EXISTS `code_gen_config`;
CREATE TABLE `code_gen_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名',
  `table_schema` varchar(255) DEFAULT NULL,
  `author` varchar(255) DEFAULT NULL COMMENT '作者',
  `is_delete` bit(1) DEFAULT NULL COMMENT '是否开启删除功能',
  `module_name` varchar(255) DEFAULT NULL COMMENT '模块名称',
  `pack` varchar(255) DEFAULT NULL COMMENT '至于哪个包下',
  `path` varchar(255) DEFAULT NULL COMMENT '前端代码生成的路径',
  `api_path` varchar(255) DEFAULT NULL COMMENT '前端Api文件路径',
  `new_table_name` varchar(255) DEFAULT NULL COMMENT '新表名',
  `api_alias` varchar(255) DEFAULT NULL COMMENT '接口名称',
  `authority_id` int(11) DEFAULT NULL COMMENT '权限id',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='代码生成器配置';

SET FOREIGN_KEY_CHECKS = 1;
