image: node:14

before_script:
  - echo -e "************** package.jscssfunny.com" >> /etc/hosts
  - echo -e "@flat:registry=http://package.jscssfunny.com:4873/" >> ~/.npmrc
  - echo -e "@flat-design:registry=http://package.jscssfunny.com:4873/" >> ~/.npmrc
  - echo 'success!'

stages:
  - merge-request

Code Review:
  stage: merge-request
  script:
    - npm i @flat/code-review -g
    - echo "$CI_MERGE_REQUEST_PROJECT_ID" 
    - echo "$CI_MERGE_REQUEST_IID"
    - echo "$GITLAB_TOKEN"
    - code-review run --token "$GITLAB_TOKEN" --project "$CI_MERGE_REQUEST_PROJECT_ID" --mr "$CI_MERGE_REQUEST_IID" --assignees "$CI_MERGE_REQUEST_ASSIGNEES" --userId "$GITLAB_USER_ID" --defaultAssigneeId 59
  only:
    - merge_requests
  allow_failure: true