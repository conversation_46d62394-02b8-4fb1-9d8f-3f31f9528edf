import SEQUELIZE, { Sequelize, QueryInterface, DataTypes } from 'sequelize';
import { writeFile } from 'fs/promises';
import { resolve } from 'path';

interface IModalTemplate {
  tableName: string;
  defineInfo: Record<string, any>;
  fileName: string;
  sequelizeTypes: string[];
}
interface IControllerTemplate {
  fileName: string;
  filterInfo: Array<{
    key: string
    type: string
  }>;
}
interface ITableInfo {
  type: string;
  allowNull: boolean;
  defaultValue: string;
  primaryKey: boolean;
  autoIncrement: boolean;
  comment: string;
}

function toCamelCase(str) {
  return str.replace(/_(\w)/g, (_: string, letter: string) => letter.toUpperCase());
}

function toCamelCaseWithCapitalizedFirstLetter(str: string) {
  const camelCaseStr = toCamelCase(str);
  return camelCaseStr.charAt(0).toUpperCase() + camelCaseStr.slice(1);
}

class Template {
  public static modal({ tableName, defineInfo = {}, fileName, sequelizeTypes }: IModalTemplate) {
    let stringifyText = JSON.stringify(defineInfo, null, 2);
    stringifyText = stringifyText.replace(/"([^"]+)": "(.*?)"/g, (match, p1, p2) => `${p1}: ${p2}`);
    return `
      import { config } from '../../lib/baseModel';

      export default app => {
        const { ${sequelizeTypes.join(', ')} } = app.Sequelize;

        const ${fileName} = app.model.define(
          '${tableName}',
          ${stringifyText},
          {
            ...config
          }
        );


        return ${fileName};
      };

    `;
  }
  public static controller({ fileName, filterInfo }: IControllerTemplate) {
    const whereInfo = filterInfo.map(({ key, type }) => {
      let whereValue = `query.${key}`;
      if (type.includes('in')) {
        whereValue = `query.${key}.split(',')`;
      }
      return `
        if (query.${key}) {
          where.${key} = {
            [Op.${type}]: ${whereValue}
          }
        }
      `;
    });

    const whereResult = whereInfo?.length ? whereInfo.join('  ') : '';
    return `
      import Controller from '../base';
      import sequelize from 'sequelize';

      const { Op } = sequelize;
      export default class ${fileName}Controller extends Controller {
        async index() {
          const { ctx, service } = this;
          const { query } = ctx;
          const params = await this.getParams(query);
          const result = await service.${fileName}.find(params);
          const total = await service.${fileName}.count(params);
          this.success(result, ctx.__('FetchSuccess'), { total });
        }

        async getParams(query) {
          const { Op } = sequelize;
          const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
          const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
          const { } = query;
          const where = { };
          const order = [['id', 'DESC']];
          // 主表筛选条件

          const params = {
            where,
            order,
            raw: true
          } as any;
          if (pageSize) {
            params.limit = pageSize;
          }
          if (pageIndex) {
            params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
          }
          return params;
        }

        async show() {
          const { ctx, service } = this;
          const { params } = ctx;
          const { id } = params;
          const result = await service.${fileName}.findOne({ where: { id } });
          this.success(result, ctx.__('FetchSuccess'));
        }

        async create() {
          const { ctx, service } = this;
          const { body } = ctx.request;
          body.creator = ctx.user.pub_name;
          body.modifier = ctx.user.pub_name;
          delete body.id;
          const result = await service.${fileName}.create(body);
          this.success(result, ctx.__('CreateSuccess'));
        }

        async update() {
          const { ctx, service } = this;
          const { id } = ctx.params;
          const updateData = { ...ctx.request.body };
          updateData.modifier = ctx.user.pub_name;
          const result = await service.${fileName}.update(id, updateData);
          this.success(result, ctx.__('UpdateSuccess'));
        }

        // DELETE	/xxxx/:id
        async destroy() {
          const { ctx, service } = this;
          const { id } = ctx.params;
          await service.${fileName}.destroy(id);
          this.success({}, ctx.__('DeleteSuccess'));
        }
      }

    `;
  }

  public static service({ fileName }: { fileName: string }) {
    return `

      import { Service } from 'egg';

      export default class ${fileName}Service extends Service {
        async find(query) {
          const { ctx } = this;
          const result = await ctx.model.${fileName}.findAll(query);
          return result;
        }

        async findOne(query) {
          const { ctx } = this;
          const result = await ctx.model.${fileName}.findOne(query);
          return result;
        }

        async count(query) {
          const { ctx } = this;
          const count = await ctx.model.${fileName}.count(query);
          return count;
        }

        async create(data) {
          const { ctx } = this;
          const result = await ctx.model.${fileName}.create(data, { individualHooks: true, egg: this });
          return result;
        }

        async update(query, data) {
          const { ctx } = this;
          query = typeof query === 'object' ? query : { id: query };
          const result = await ctx.model.${fileName}.update(data, { where: query, individualHooks: true, egg: this });
          return result;
        }

        async destroy(query) {
          const { ctx } = this;
          query = typeof query === 'object' ? query : { id: query };
          const result = await ctx.model.${fileName}.destroy({
            where: query,
            individualHooks: true,
            egg: this
          });
          return result;
        }
      }

    `;
  }
}

class SequelizeAutoGenerate {
  sequelize: Sequelize;
  instance: QueryInterface;

  private typeStrategy = {
    'bigint': 'BIGINT',
    'varchar': 'STRING',
    'int': 'INTEGER',
    'tinyint': 'BOOLEAN',
    'datetime': 'DATE',
    'date': 'DATEONLY',
    'timestamp': 'DATE',
    'text': 'TEXT',
    'float': 'FLOAT',
    'double': 'DOUBLE',
    'decimal': 'DECIMAL',
    'enum': 'ENUM',
    'json': 'JSON',
    'jsonb': 'JSONB',
    'uuid': 'UUID',
    // 继续添加其他 MySQL 字段类型和相应的 Sequelize DataTypes 映射关系
  };

  constructor() {
    // @ts-ignore
    this.sequelize = new SEQUELIZE({
      database: 'nemo',
      username: 'app_fe',
      password: 'qnaevfqCrAvrFvniU2cL',
      host: 'rm-gs509ga2ae1l0yx3ydo.mysql.singapore.rds.aliyuncs.com', // 公网域名, 通测试环境： adx-center.mysql.singapore.rds.aliyuncs.com
      port: 3306,
      dialect: 'mysql',
    });
    this.instance = this.sequelize.getQueryInterface();
  }

  async run(tableName: string) {
    const tableInfo = await this.getTableInfo(tableName);
    const fileName = toCamelCaseWithCapitalizedFirstLetter(tableName);

    const filterInfo = [
      { key: 'channel', type: 'eq' },
      { key: 'offer', type: 'in' }
    ];

    const updateInfo = [];
    const createInfo = [];

    this.generateModal({ tableName, fileName, tableInfo });
    this.generateService({ fileName });
    this.generateController({ fileName, filterInfo });

    // console.log(tables);
  }

  private async getTableInfo(tableName: string) {
    const tableInfo = await this.instance.describeTable(tableName) as Record<string, ITableInfo>;
    return tableInfo;
    // console.log(modalResult)
  }
  private async generateModal({ tableName, fileName, tableInfo }: { tableName: string, fileName: string, tableInfo: Record<string, ITableInfo> }) {
    const { defineInfo, sequelizeTypes } = this.getDefineInfo(tableInfo);

    const modalResult = Template.modal({ tableName, fileName, defineInfo, sequelizeTypes });
    const formatModalResult = modalResult;
    writeFile(resolve(__dirname, `./gen-result/${fileName}Modal.ts`), formatModalResult, 'utf-8').then(res => {
      console.log('写入Modal成功');
    }).catch(err => {
      console.log('写入失败', err);
    });
  }
  private async generateController({ fileName, filterInfo = [] }: IControllerTemplate) {

    const modalResult = Template.controller({ fileName, filterInfo });
    const formatModalResult = modalResult;
    writeFile(resolve(__dirname, `./gen-result/${fileName}Controller.ts`), formatModalResult, 'utf-8').then(res => {
      console.log('写入Controller成功');
    }).catch(err => {
      console.log('写入失败', err);
    });
  }
  private async generateService({ fileName }: { fileName: string }) {

    const modalResult = Template.service({ fileName });
    const formatModalResult = modalResult;
    writeFile(resolve(__dirname, `./gen-result/${fileName}Service.ts`), formatModalResult, 'utf-8').then(res => {
      console.log('写入Service成功');
    }).catch(err => {
      console.log('写入失败', err);
    });
  }
  private getDefineInfo(tableInfo: Record<string, ITableInfo>) {
    const sequelizeTypes: string[] = [];
    const defineInfo: Record<string, any> = Object.entries(tableInfo).reduce((map, [colKey, describe]) => {
      const formatKey = this.formatColumnType(describe.type);
      sequelizeTypes.push(formatKey.type);
      let result: string | Record<string, any> = '';
      if (formatKey.typeValue) {
        result = `${formatKey.type}(${formatKey.typeValue}), // ${describe.comment}`;
      } else {
        result = `${formatKey.type}, // ${describe.comment}`;
      }
      if (describe.autoIncrement) {
        result = { type: `${formatKey.type}(${formatKey.typeValue})`, primaryKey: describe.primaryKey, autoIncrement: describe.autoIncrement };
      }
      map[colKey] = result;
      return map;
    }, {});

    return {
      defineInfo,
      sequelizeTypes: [...new Set(sequelizeTypes)]
    };
  }

  private formatColumnType(typeStr: string) {
    console.log(typeStr);
    if (typeStr.includes('DECIMAL')) {
      return {
        type: 'DECIMAL',
        typeValue: typeStr.match(/\((.*?)\)/)?.[1]
      };
    }
    if (typeStr.includes('JSON')) {
      return {
        type: 'JSON',
        typeValue: ''
      };
    }
    if (typeStr.includes('TIMESTAMP')) {
      return {
        type: 'DATE',
        typeValue: ''
      };
    }
    if (typeStr.includes('TEXT')) {
      return {
        type: 'TEXT',
        typeValue: ''
      };
    }
    const typeReg = /([A-Z]+)\((\d+)\)/;
    const match = typeStr.match(typeReg)!;
    let type = match[1].toLowerCase();
    type = this.typeStrategy[type] || type;
    const typeValue = match[2];

    return {
      type,
      typeValue
    };
  }
}

const autoGenerate = new SequelizeAutoGenerate();
autoGenerate.run('cms_auth');
