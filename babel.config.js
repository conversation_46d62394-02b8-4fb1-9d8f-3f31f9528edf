const config = {
  env: {
    node: {
      presets: [
        "@babel/preset-react",
        [
          "@babel/preset-env",
          {
            modules: false,
            targets: {
              node: "current",
            },
          },
        ],
      ],
      plugins: ["@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-object-rest-spread"],
    },
    web: {
      presets: [
        "@babel/preset-react",
        [
          "@babel/preset-env",
          {
            modules: false,
            targets: {
              browsers: ["last 2 versions", "safari >= 8"],
            },
          },
        ],
      ],
      plugins: ["@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-object-rest-spread"],
    },
  },
  comments: false,
};

if (!process.env.EASY_ENV || (process.env.EASY_ENV && process.env.EASY_ENV !== "prod")) {
  config.env.web.plugins.push("react-hot-loader/babel");
}

module.exports = config;
