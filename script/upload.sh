NAME="ad-crm-oms"
PROJECT_PATH="/home/<USER>/app"
LOG_PREFIX="======[build-log]======:"
# 外网ip：************ sgb-front-test-001
HOST=("**************")
# 编译状态， 0 失败， 1 成功
DEPLOY_STATUS=0

# 部署开始
if [ "${build_env}" = "test" ];then
  curl -H "Cache-Control: no-cache" http://**************:6600/api/system-update-notification/update-start
fi 

if [ "${build_env}" = "prod" ];then
  curl -H "Cache-Control: no-cache" http://crm.mobshark.net/api/system-update-notification/update-start
fi 

echo "============== env: ${build_env}; is_first: ${is_first}; is_install: ${is_install} ================"
if [ "${is_install}" = "true" ];then
  npm i
  echo $LOG_PREFIX" npm i ..."
else
  echo $LOG_PREFIX" No need to installation"
fi

npm run clean
npm run build-both
if [ $? -eq 0 ]; then
  DEPLOY_STATUS=1
  echo 'npm run build-both successfully'
else
  DEPLOY_STATUS=0
  echo 'npm run build-both failed'
  # 发送编译失败钉钉消息
  TXT_CONTENT="Jenkins部署: ${NAME} 构建失败"
  curl -X POST \
  'https://oapi.dingtalk.com/robot/send?access_token=8a01595326808329c70831f76ea55cc4ef01bc29f662a983ba00fdb2d7892e89' \
  -H 'Content-Type: application/json' \
  -d "{\"msgtype\":\"text\", \"text\": {\"content\": \"${TXT_CONTENT}\"}}"
fi

if [ $DEPLOY_STATUS -eq 1 ]; then
  mkdir dist
  rsync -av --exclude typings/ --exclude app/web/component/ --exclude app/web/page/ --exclude app/web/framework/ --exclude app/web/modules/ --exclude app/view/img/ --exclude .git/ --exclude dist/ --exclude logs/ --exclude node_modules/ --exclude database/ --exclude '*.ts' --exclude '*.scss' --exclude '*.LICENSE.txt'  ./  ./dist
  # 拷贝守护进程脚本
  if [ "${build_env}" = "prod" ]
  then
  # 正式环境 sgb-front-003-*************-************
    HOST=("************")
  fi
  tar -czvf ${NAME}.tar ./dist
  rm -rf ./dist


  for host in ${HOST[@]}
  do
    ssh ${host} "cd ${PROJECT_PATH} && if [ ! -d ${NAME} ];then mkdir -p ${NAME};fi"
    scp -P 22 ${NAME}.tar webserver@${host}:${PROJECT_PATH}/${NAME}
    echo $LOG_PREFIX" Upload ${host} to ${build_env} is success"
  done


  # 删除本地tar包
  rm ./${NAME}.tar

  for host in ${HOST[@]}
  do
    if [ "${is_first}" = "true" ];then
      if [ "${build_env}" = "prod" ];then
        SCRIPT="start"
      else
        SCRIPT="start-test"
      fi
      ssh ${host} "cd ${PROJECT_PATH}/${NAME} && tar -xvf ${NAME}.tar && mv -fb dist/* ./ && rm -rf ./dist ${NAME}.tar ./*~ && npm i --production && npm run ${SCRIPT}"
    else
      ssh ${host} "cd ${PROJECT_PATH}/${NAME} && tar -xvf ad-crm-oms.tar && sh ./dist/script/restart_${build_env}.sh ${is_install}"
    fi
    echo $LOG_PREFIX" Restart ${host} to ${build_env} is success"
  done

  # 构建成功后删除编译出来的js文件
  npm run clean
  echo "npm run clean successfully"
fi

# 部署完成, 脚本最后添加
if [ "${build_env}" = "test" ];then
  curl -H "Cache-Control: no-cache" http://**************:6600/api/system-update-notification/update-finish
fi 


if [ "${build_env}" = "prod" ];then
  curl -H "Cache-Control: no-cache" http://crm.mobshark.net/api/system-update-notification/update-finish
fi 
