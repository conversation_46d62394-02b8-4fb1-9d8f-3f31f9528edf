# 使用该脚本传入两参数：
# 参数1 运行标题，与pageckage.json中 npm run start title参数内容必须一致；
# 参数2 运行项目名, 即服务器中项目的文件夹名 ~/app/xxxx
# 范例： sh create_monitor.sh flat_cms flat_cms
# 获取输入参数
varTitle=$1;
varProName=$2;
varfileName="monitor.sh";
varMonitorName="$1_monitor.sh";
if [[ ${varTitle} =~ 'test' ]];then 
  varfileName="monitor_test.sh";
else 
  varfileName="monitor.sh";
fi
echo "run title: $varTitle";
echo "project name: $varProName";
echo "monitor name: $varfileName";
echo "crontab name: $varMonitorName";
# 文件不存在则创建
if [ -f "./$varfileName" ];then
  echo '#!/bin/bash' >$varfileName;
else
  touch $varfileName;
  echo '#!/bin/bash' >>$varfileName;
fi
# 开始写入内容
echo 'source ~/.bash_profile' >>$varfileName;
echo 'DEAMON_NUM=`ps -ef|grep "\-\-title='$varTitle'"|wc -l`' >>$varfileName;
echo 'if [ $DEAMON_NUM -lt 1 ] ; then' >>$varfileName;
echo '  BASE_PATH=`dirname $(readlink -f $0)`' >>$varfileName;
echo '  cd BASE_PATH' >>$varfileName;
if [[ ${varTitle} =~ 'test' ]];then
  echo '  npm run start-test' >>$varfileName;
else 
  echo '  npm run start' >>$varfileName;
fi
echo 'fi' >>$varfileName;
# 文件复制到上一层
cp ./$varfileName ../$varfileName
# 创建软链
echo "ln -s /home/<USER>/app/$varProName/$varfileName /home/<USER>/local/crontab/1m/$varMonitorName";
ln -s /home/<USER>/app/$varProName/$varfileName /home/<USER>/local/crontab/1m/$varMonitorName;
echo "create monitor suceessfully!";