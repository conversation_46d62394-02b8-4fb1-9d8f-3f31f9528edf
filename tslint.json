{"defaultSeverity": "error", "extends": ["tslint:recommended"], "jsRules": {}, "rules": {"prefer-for-of": false, "member-access": false, "ordered-imports": false, "trailing-comma": false, "quotemark": [true, "single", "jsx-double"], "eofline": false, "object-literal-sort-keys": false, "interface-name": false, "arrow-parens": false, "no-console": false, "max-line-length": false, "only-arrow-functions": false, "max-classes-per-file": [true, 3, "exclude-class-expressions"], "no-empty": [true, "allow-empty-catch", "allow-empty-functions"], "no-shadowed-variable": false, "space-before-function-paren": false, "object-literal-key-quotes": false, "no-var-requires": false}, "paths": {"component": "app/web/component", "@/*": ["*"], "@@/*": ["../*"], "request": ["./modules/request.ts"]}, "rulesDirectory": ["app"]}