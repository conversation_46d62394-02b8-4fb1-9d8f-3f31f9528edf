{
  "extends": "./config/tsconfig.json",
  "compileOnSave": true,
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    /* Experimental Options */
    "experimentalDecorators": true /* Enables experimental support for ES7 decorators. */,
    "emitDecoratorMetadata": true /* Enables experimental support for emitting type metadata for decorators. */,
    "strictNullChecks": false,
    "importHelpers": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  },
  "include": [
    "plugin/**/*.ts",
    "index.ts",
    "app/**/*.ts",
    "config/**/*.ts",
    "mock/**/*.ts",
    "test/**/*.ts",
    "app.ts",
    "agent.ts",
    "typings/*.ts"
  ],
  "exclude": [
    "public",
    "app/web",
    "app/public",
    "app/view",
    "dist",
    "node_modules"
  ]
}
