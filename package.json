{"name": "@flat/ad-crm-oms", "version": "1.0.0", "description": "ad-crm-oms", "scripts": {"dev": "egg-bin dev --port=6600 -r egg-ts-helper/register --title=ad-crm-oms", "debug": "egg-bin debug --title=ad-crm-oms", "build": "easy build", "start-test": "egg-scripts start --env=test --workers=1 --port=6600 --daemon --title=ad-crm-oms-test --stdout=/home/<USER>/logs/logger/ad-crm-oms/master-stdout.log --stderr=/home/<USER>/logs/logger/ad-crm-oms/master-stderr.log", "stop-test": "egg-scripts stop --title=ad-crm-oms-test", "start": "egg-scripts start --env=prod --workers=2 --port=6600 --daemon --title=ad-crm-oms --stdout=/home/<USER>/logs/logger/ad-crm-oms/master-stdout.log --stderr=/home/<USER>/logs/logger/ad-crm-oms/master-stderr.log", "stop": "egg-scripts stop --title=ad-crm-oms", "tsc": "ets && tsc -p tsconfig.json", "build-both": "npm run tsc && npm run build", "clean": "ets clean && easy clean", "analyze-dev": "easy build -s", "analyze-test": "easy build test -s", "analyze-prod": "easy build prod -s", "kill": "easy kill", "lint": "tslint --project . -c tslint.json", "fix": "tslint --fix --project . -c tslint.json"}, "dependencies": {"@alicloud/dingtalk": "^2.1.16", "@fe-design/egg-sse": "^1.0.12", "@fe-design/egg-system-update-notification": "^1.0.52", "@flat-design/components-pc": "^1.122.0", "@flat-design/egg-login-verify": "^1.0.8", "@flat-design/hooks": "^1.61.1", "@flat/egg-plugin": "^1.0.25", "@flat/egg-system-config": "^0.0.16", "@flat/rollup-plugin-watermark": "0.0.1", "@hubcarl/json-typescript-mapper": "2.0.0", "@pigerla/copy-paste-js": "^1.0.4", "@rjsf/antd": "^2.3.0", "@rjsf/core": "^2.3.0", "@wangeditor/editor": "^5.1.23", "adm-zip": "^0.4.16", "ahooks": "^3.7.10", "ali-oss": "^6.18.1", "antd": "^4.23.5", "app-root-path": "^3.1.0", "autod-egg": "^1.0.0", "await-stream-ready": "^1.0.1", "axios": "^0.19.2", "buffer": "^6.0.3", "cls-hooked": "^4.2.2", "compressing": "1.5.1", "cross-env": "^5.0.0", "crypto-js": "^4.0.0", "dayjs": "^1.8.33", "egg": "^2.27.0", "egg-bin": "^4.7.1", "egg-cors": "^2.2.3", "egg-logger": "^1.8.0", "egg-mysql": "^3.0.0", "egg-passport": "^2.1.1", "egg-passport-local": "^1.2.1", "egg-postgres": "^1.0.2", "egg-redis": "^2.6.0", "egg-scripts": "^2.13.0", "egg-sequelize": "^6.0.0", "egg-validate": "^1.1.1", "egg-view-react-ssr": "^3.0.0", "egg-xtransit": "^1.2.2", "elasticsearch": "^16.7.2", "extend": "^3.0.2", "history": "^4.10.1", "jsencrypt": "^3.0.0-rc.1", "lodash-id": "^0.14.0", "lowdb": "^1.0.0", "md5": "^2.3.0", "memcached": "^2.2.2", "mysql2": "^2.1.0", "node-rsa": "^1.1.1", "nodemailer": "^6.7.1", "number-precision": "^1.6.0", "react": "^16.13.1", "react-activation": "^0.12.4", "react-beautiful-dnd": "^13.1.1", "react-dom": "^16.13.1", "react-loadable": "^5.5.0", "react-router": "^5.2.0", "react-router-config": "^5.1.1", "react-router-dom": "^5.2.0", "react-transition-group": "^4.4.5", "sequelize": "6.11", "sequelize-auto": "^0.8.8", "shortid": "^2.2.8", "showdown": "^1.8.6", "spark-md5": "^3.0.2", "urlencode": "^1.1.0", "webpack-bundle-analyzer": "^4.9.1", "website-scraper": "^4.2.3", "xlsx": "^0.17.4", "zustand": "^4.5.2"}, "devDependencies": {"@easy-team/easywebpack-cli": "^4.5.2", "@easy-team/easywebpack-react": "^4.1.1", "@types/react": "^16.14.52", "@types/react-dom": "^16.0.3", "@types/react-router-dom": "^5.3.3", "babel-plugin-import": "^1.13.0", "commitlint": "^8.3.5", "egg-ts-helper": "^1.9.0", "egg-webpack": "^4.5.5", "egg-webpack-react": "^3.0.0", "eslint": "^4.6.1", "eslint-config-egg": "^5.1.1", "eslint-plugin-react": "^7.1.0", "husky": "^4.2.3", "ip": "^1.1.5", "less": "^3.7.0", "less-loader": "^4.0.5", "lint-staged": "^10.0.8", "mini-css-extract-plugin": "^0.9.0", "sass": "^1.68.0", "sass-loader": "^9.0.3", "sass-resources-loader": "^2.0.3", "sequelize-cli": "^6.4.1", "terser-webpack-plugin": "^3.0.8", "ts-loader": "^4.0.0", "tslint": "^5.9.1", "tslint-loader": "^3.5.3", "typescript": "^4.1.6"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "egg": {"typescript": true}, "engines": {"node": ">=8.0.0"}, "ci": {"version": "8, 10, 12"}, "repository": {"type": "git", "url": "http://git.flatincbr.com:3000/fe/flat-template.git"}, "author": "wujj", "license": "MIT", "homepage": "http://git.flatincbr.com:3000/fe/flat-template", "webpack": {"loaders": {"scss": true}}, "iceworks": {"type": "react", "adapter": "adapter-react-v3"}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS", "pre-commit": "npm run fix"}}, "lint-staged": {"*.{ts,tsx}": "prettier --write"}, "prettier": {"printWidth": 500, "trailingComma": "none", "arrowParens": "avoid", "tabWidth": 2, "semi": true, "singleQuote": true}}