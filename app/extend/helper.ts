'use strict';

import fs from 'fs';
import Base64 from 'crypto-js/enc-base64';
import Utf8 from 'crypto-js/enc-utf8';

import { dirname } from 'path';
interface IMessageObj {
  label: string;
  value: string;
}

export default {
  /**
   * 写入并保存文件
   * @param path 将要写入的文件路径
   * @param content 写入文件的内容
   */
  writeFile(path: string, content: any) {
    return new Promise((resolve, reject) => {
      fs.mkdir(dirname(path), { recursive: true }, err => {
        if (err) {
          return reject(err);
        }
        const data = new Uint8Array(Buffer.from(content));
        fs.writeFile(path, data, err => {
          if (err) {
            return reject(err);
          }
          resolve('ok');
        });
      });
    });
  },

  /**
   * 清除指定module的require's cache
   * @param modulePath 模块的路径
   */
  deleteCacheByPath(modulePath) {
    const module = require.cache[modulePath];
    if (module && module.parent) {
      module.parent.children.splice(module.parent.children.indexOf(module), 1);
    }
    delete require.cache[modulePath];
  },

  /**
   *
   * @param data 待转数据
   * @returns 参数字符串
   */
  serialize(data, prefix) {
    const str = [];
    for (const p in data) {
      if (data.hasOwnProperty(p)) {
        const key = prefix ? prefix + '[' + p + ']' : p;
        const value = data[p];
        let encodeValue;
        if (value !== null && typeof value === 'object') {
          encodeValue = this.serialize(value, key);
        } else {
          if (key.includes('_b64')) {
            encodeValue = encodeURIComponent(key) + '=' + Base64.stringify(Utf8.parse(value));
          } else if (key.includes('_uncode')) {
            encodeValue = encodeURIComponent(key.split('_uncode')[0]) + '=' + value;
          } else {
            encodeValue = encodeURIComponent(key) + '=' + encodeURIComponent(value);
          }
        }
        str.push(encodeValue);
      }
    }
    return str.join('&');
  },

  /**
   * 生成dd消息模版
   */
  createDDMessageTemplate(messageObjArr: IMessageObj[], ddTitle: string, atMobilesText?: string) {
    const messageTemplate = messageObjArr
      .map(item => {
        return `**${item.label}**: ${item.value || ''}  \n  `;
      })
      .join('');

    const result = `**${ddTitle}**  \n  ${messageTemplate}  \n  ${atMobilesText || ''}`;
    // 去掉结尾多余的空格和换行符
    return result.replace(/(?<=\S)\n$/, '');
  },
};
