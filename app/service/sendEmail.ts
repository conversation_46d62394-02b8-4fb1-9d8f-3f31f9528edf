import { Service } from 'egg';
import nodemailer from 'nodemailer';
import { AppConfig } from '../lib/constant';

// 发送邮件 公共服务
export default class SendEmailService extends Service {
  // error catch，send to ding talk robot
  async emailTo(params: any) {
    const { email, title, text, html } = params;
    const { smtpHost, port, user, pass } = {
      smtpHost: 'smtp.live.com',
      port: 587,
      user: '<EMAIL>',
      pass: 'chengfeng@2021'
    };

    // create reusable transporter object using the default SMTP transport
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port,
      secure: false, // true for 465, false for other ports
      auth: {
        user, // generated ethereal user
        pass // generated ethereal password
      }
    });
    const sendObj: any = {
      from: `<${user}>`, // sender address
      to: `${email}`, // list of receivers
      subject: title // Subject line
      // text: "Hello world?", // plain text body
      // html: "<b>Hello world?</b>", // html body
    };
    if (text) {
      sendObj.text = text;
    }
    if (html) {
      sendObj.html = html;
    }
    let result: any = {};
    try {
      // send mail with defined transport object
      const info = await transporter.sendMail(sendObj);
      result = { msg: 'success', isSuccess: true };
    } catch (error) {
      result = { msg: `${JSON.stringify(error)}`, isSuccess: false };
    }
    return result;
  }
}
