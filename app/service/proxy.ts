import { Service } from 'egg';
import { isObject, isPureObject } from '../lib/tool';

export default class RequestService extends Service {
  async request(url: string, data: object, method: any = 'get', contentType?: string, headers?: {}) {
    const {
      app: { config },
      ctx
    } = this;
    const host = typeof data === 'string' ? data : method;
    const dataHost: any = config.dataHost[host];
    let result: any = {};
    let isProtoBuf = false;
    // console.log("url, data, method, contentType, headers: ");
    // console.log(url, data, method, contentType, headers);
    // 调用MC或者ES方法
    if (dataHost) {
      result = await getData(url, data);
    } else {
      if (headers) {
        Object.keys(headers).forEach(key => {
          // 约定的特征
          if (key && key.startsWith('x-') && key.endsWith('-bin')) {
            isProtoBuf = true;
          }
        });
      }
      // headers有值则透传下去
      if (isProtoBuf) {
        headers = { ...headers, 'content-type': 'application/json' };
      }
      const requestConfig: any = {
        method,
        // 不需要设置 contentType，HttpClient 会默认以 application/x-www-form-urlencoded 格式发送请求
        data,
        // 明确告诉 HttpClient 以 JSON 格式处理响应 body
        dataType: 'json',
        headers: { ...headers },
        timeout: 25000 // 20秒超时
      };
      if (contentType) {
        requestConfig.contentType = contentType;
      }
      // console.log("url:", url);
      result = await ctx.curl(url, requestConfig);
    }

    // 返回 proto buf结果 开始
    if (isProtoBuf) {
      let pbResult: any = {};
      try {
        const resBin = result?.headers?.['x-rspcontrol-bin'];
        const resBinObj: any = JSON.parse(String(resBin));
        const code = resBinObj?.result?.code || '0';
        if (code === '0') {
          pbResult.status = 1;
          pbResult.data = result.data;
        } else {
          pbResult.data = resBinObj;
          pbResult.status = code;
          pbResult.msg = resBinObj?.result?.message;
        }
      } catch (error) {
        pbResult = result.res;
      }
      return pbResult;
    }
    // 返回 proto buf结果 结束

    // MC,ES方法
    async function getData(url: string, data: object) {
      const result = { status: -1, msg: 'Something wrong with service, please try again' };
      // ES
      if (isPureObject(url)) {
        return await dataHost.search(url);
      }

      // MC
      if (isPureObject(data)) {
        const str = ctx.helper.serialize(data, '');
        url += url.includes('?') ? '&' : '?' + str;
      }

      return new Promise((resolve, reject) => {
        dataHost.get(url, (err: any, res: any) => {
          // console.log('PROXY URL:', url);
          if (err) {
            //  console.log('MC ERROR:', err);
            reject(err);
          } else {
            //  console.log('RES DATA:', url, res);
            if (!res) {
              resolve(result);
            } else {
              res = typeof res === 'string' ? JSON.parse(res) : res;
              resolve(res);
            }
          }
        });
      });
    }
    if (result.data || result.data === 0) {
      return result.data;
    }

    if (result.res || result.res === 0) {
      return result.res;
    }
    return result;
  }

  /** proto buf 请求 */
  async pbRequest({
    xMethod = '',
    data = {},
    action = '',
    itemId = '',
    pathname = '',
    method = 'POST',
    contentType = 'application/json',
    createIdKey = '',
  }) {
    let result: any = {};
    const { ctx } = this;
    const url = `${ctx.app.config.pbApiHost}/sgw/api/mgr`;
    const headers = {
      'x-token-bin': ctx.session.jwt,
      'x-pubpara-bin': '{}',
      'x-reqcontrol-bin': JSON.stringify({
        service: 'social.xtools',
        method: xMethod
      })
    };
    result = await this.request(url, data, method, contentType, headers);
    if (action && result.status === 1) { // 更新、创建、删除 请求, 入库操作日志
      // console.log("result:", result);
      let logItemId = itemId || -1;
      if (createIdKey) { // 不同业务不同，根据业务接口返回数据结构处理
        const data = result.data;
        if (data[createIdKey]) {
          logItemId = data[createIdKey];
        }
      }
      this.service.operateLog.saveLog({
        realPathname: pathname,
        action,
        itemId: logItemId,
        afterData: {
          ...data,
        }
      });
    }
    return result;
  }

  public async curlGetProxy(params: Record<string, any>) {
    const { dataType, host, url, data } = params;
    const { app } = this;
    const urlPrefix = app.config.all_host[host];
    const path = `${urlPrefix}${url}`;

    const query = isObject(data) ? data : JSON.parse(data);
    const httpParams: any = {
      method: 'get',
      path,
      requestData: query
    };
    if (dataType) {
      httpParams.dataType = dataType;
    }
    const result: any = { isSuccess: false, msg: '', data: '' };
    try {
      const data = await this.ctx.service.http.http(httpParams);
      if (data && data.status === 1) {
        result.msg = 'success';
        result.isSuccess = true;
        result.data = data.data;
      } else {
        result.msg = data.msg || '';
      }
    } catch (error: any) {
      result.msg = error.message;
    }
    return result;
  }
  public async curlPostProxy(params: Record<string, any>) {
    const { dataType, host, url, headers, data } = params;
    const { app } = this;
    const urlPrefix = app.config.all_host[host];
    const path = `${urlPrefix}${url}`;
    const requestData = data;
    const httpParams: any = {
      method: 'POST',
      path,
      requestData
    };
    if (headers) {
      httpParams.headers = headers;
    }
    if (dataType) {
      httpParams.dataType = dataType;
    }
    const result: any = { isSuccess: false, msg: '', data: '' };

    try {
      const data = await this.ctx.service.http.http(httpParams);
      if (data) {
        result.msg = 'success';
        result.isSuccess = true;
        result.data = data;
      }
    } catch (error) {
      result.msg = JSON.stringify(error);
    }

    return result;
  }
}
