import { Service } from 'egg';
// import { NotifyEvent } from '../modules/redis';

export default class RedisService extends Service {
  // public async publish(info: {
  //   event: NotifyEvent;
  //   data: Record<string, any>
  // }) {
  //   this.app.redis.get('pub').publish(this.app.config.msCenter.redisChannel, JSON.stringify(info));
  // }

  async get(key) {
    return this.app.redis.get('common').get(key);
  }

  async set(key, value, expire: number) {

    const res = await this.app.redis.get('common').set(key, value);
    if (expire > 0) {
      this.expire(key, expire);
    }
    return res;
  }

  async del(key) {
    return this.app.redis.get('common').del(key);
  }

  async expire(key, time) {
    return this.app.redis.get('common').expire(key, time);
  }

  async setnx(key, value) {
    return this.app.redis.get('common').setnx(key, value);
  }
  /**
   *
   * @param flagKey 锁的key
   * @param expire 过期时间(秒)
   * @returns
   */
  async isLock(flagKey, expire = 60) {
    const flag = await this.setnx(flagKey, 1);
    if (!flag) {
      // 已经存在
      return true;
    } else {
      // 不存在，则设置过期时间
      await this.service.redis.expire(flagKey, expire);
    }
    return false;
  }

}
