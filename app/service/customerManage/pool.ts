import { Service } from 'egg';
import moment from 'moment';
import { QueryTypes } from 'sequelize';

const createRandomChar = () => {
  let result: string = '';
  const passwordList: string[] = [];
  // A~Z, a-z, 0-9, 16位密码
  // a-z：97-122, A-Z：65-90, 0-9：48-57
  let lowerStartNum: number = 97;
  let upperStartNum: number = 65;
  let noStartNum: number = 48;
  while (lowerStartNum <= 122) {
    passwordList.push(String.fromCharCode(lowerStartNum));
    lowerStartNum++;
  }
  while (upperStartNum <= 90) {
    passwordList.push(String.fromCharCode(upperStartNum));
    upperStartNum++;
  }
  while (noStartNum <= 57) {
    passwordList.push(String.fromCharCode(noStartNum));
    noStartNum++;
  }
  const len: number = passwordList.length;
  for (let i = 0; i < 16; i++) {
    const randomIndex = Math.floor(Math.random() * len);
    result += passwordList[randomIndex];
  }
  return result;
};

export default class CrmClientService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.CustomerManage.Pool.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.CustomerManage.Pool.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.CustomerManage.Pool.count(query);
    return count;
  }
  /**
   * 同步客户信息给广告系统
   */
  async syncFlatAdsDB(data: Record<string, any>) {
    // 创建之后，需要同步创建广告系统的客户
    if (data.coop_bus === 'Affiliate,Publisher Group') {
      // 创建网盟下游Group
      const isExist = await this.ctx.model.query('SELECT * FROM affiliate_publisher_group WHERE name = :name', {
        type: QueryTypes.SELECT,
        replacements: {
          name: data.client_name
        }
      });
      if (isExist?.length) {
        return false;
      }
      const userInfo = await this.service.authority.user.findOne({
        where: {
          id: data.bd_id
        },
        attributes: ['pub_name', 'email'],
        raw: true
      });
      const res = await this.ctx.model.query('INSERT INTO affiliate_publisher_group (name, bd, status, ctime, company, data_status) VALUES (:name, :bd, :status, :ctime, :company, :data_status)',
        {
          type: QueryTypes.INSERT,
          replacements: {
            name: data.client_name,
            bd: userInfo?.pub_name,
            company: data.company_name,
            status: 1,
            ctime: moment().unix(),
            data_status: 100001
          }
        });
      return {
        id: res[0],
        isSuccess: res[1] === 1
      };
    }
    if (data.coop_bus.includes('Affiliate,Advertiser Group')) {
      // 创建网盟下游Group
      const splitCoop = data.coop_bus.split(',');
      let type = splitCoop.pop(); // 最后一个

      const isExist = await this.ctx.model.query('SELECT * FROM affiliate_advertiser_group WHERE name = :name', {
        type: QueryTypes.SELECT,
        replacements: {
          name: data.client_name
        }
      });
      if (isExist?.length) {
        return false;
      }
      const userInfo = await this.service.authority.user.findOne({
        where: {
          id: data.bd_id
        },
        attributes: ['pub_name', 'email'],
        raw: true
      });
      if (type === 'Affiliate / Agency') {
        type = 'affiliate/agency';
      } else if (type === 'Direct') {
        type = 'direct';
      } else if (type === 'Owned') {
        type = 'owned';
      } else if (type === 'Other') {
        type = 'other';
      }
      const res = await this.ctx.model.query('INSERT INTO affiliate_advertiser_group (name, co_type, company_name, bd, status, ctime, pay_type, data_status, country, address, payment_legal_name) VALUES (:name, :type, :company_name, :bd, :status, :ctime, :pay_type, :data_status, :country, :address, :payment_legal_name)',
        {
          type: QueryTypes.INSERT,
          replacements: {
            name: data.client_name,
            type,
            company_name: data.company_name,
            address: data.comp_address,
            bd: userInfo?.pub_name,
            ctime: moment().unix(),
            status: 1,
            pay_type: data.pay_type === 'Pre-Payment' ? 'advance_pay' : 'post_pay',
            data_status: 100001,
            country: data.country,
            payment_legal_name: data.payment_legal_name
          }
        });
      return {
        id: res[0],
        isSuccess: res[1] === 1
      };
    }
    if (data.coop_bus === 'Adx,Demand Group' || data.coop_bus === 'Commercialize,Interative Demand Group') {
      const isExist = await this.ctx.model.query('SELECT * FROM demand_source WHERE name = :name', {
        type: QueryTypes.SELECT,
        replacements: {
          name: data.client_name
        }
      });
      if (isExist?.length) {
        return false;
      }
      const userInfo = await this.service.authority.user.findOne({
        where: {
          id: data.bd_id
        },
        attributes: ['pub_name', 'email'],
        raw: true
      });

      const replacements = {
        name: data.client_name,
        bd: userInfo?.pub_name,
        api_key: createRandomChar(),
        note: data.coop_bus === 'Commercialize,Interative Demand Group' ? 'interative' : '',
        company: data.company_name,
        ctime: moment().unix(),
        data_status: 100001,
        country: data.country,
        address: data.comp_address,
        payment_legal_name: data.payment_legal_name
      };
      const res = await this.ctx.model.query(`INSERT INTO demand_source
      (name, bd, api_key, note, company, ctime, data_status, country, address, payment_legal_name) VALUES (:name, :bd, :api_key, :note, :company, :ctime, :data_status, :country, :address, :payment_legal_name)`,
        {
          type: QueryTypes.INSERT,
          replacements
        });
      return {
        id: res[0],
        isSuccess: res[1] === 1
      };
    }

    if (data.coop_bus.includes('Adx,Publisher Group') || ['Other,H5 Content Provider', 'Other,H5 Traffic Channel', 'Commercialize,Interative Publisher Group'].includes(data.coop_bus)) {
      let type = 'SDK';
      if (data.coop_bus.includes('Adx,Publisher Group')) {
        const splitCoop = data.coop_bus.split(',');
        type = splitCoop.pop(); // 最后一个
      }

      const isExist = await this.ctx.model.query('SELECT * FROM publisher_group WHERE name = :name', {
        type: QueryTypes.SELECT,
        replacements: {
          name: data.client_name
        }
      });
      if (isExist?.length) {
        return false;
      }
      const userInfo = await this.service.authority.user.findOne({
        where: {
          id: data.bd_id
        },
        attributes: ['pub_name', 'email'],
        raw: true
      });

      const res = await this.ctx.model.query('INSERT INTO publisher_group (name, bd, ctime, api_key, company, status, type, data_status) VALUES (:name, :bd, :ctime, :api_key, :company, :status, :type, :data_status)',
        {
          type: QueryTypes.INSERT,
          replacements: {
            name: data.client_name,
            bd: userInfo?.pub_name,
            ctime: moment().unix(),
            api_key: createRandomChar(),
            company: data.company_name,
            status: 1,
            type,
            data_status: 100001
          }
        });
      return {
        id: res[0],
        isSuccess: res[1] === 1
      };
    }

    return {
      id: 0,
      isSuccess: true
    };

  }

  /**
   * 同步修改广告系统客户信息
   */
  async syncUpdateAdsClientData(id: number, updateData: Record<string, any>) {
    if (!id) { return; }
    const curClientInfo = await this.service.customerManage.pool.findOne({
      where: {
        id
      },
      attributes: ['id', 'coop_bus', 'association_id'],
      raw: true
    });
    if (!curClientInfo
      || !curClientInfo?.association_id
      || Number(curClientInfo.association_id) === 0
      || !updateData?.client_name
    ) { return true; }

    if (curClientInfo.coop_bus === 'Affiliate,Publisher Group') {
      // 创建网盟下游Group
      const isExist = await this.ctx.model.query(`SELECT * FROM affiliate_publisher_group WHERE name = :name and id != ${curClientInfo.association_id}`, {
        type: QueryTypes.SELECT,
        replacements: {
          name: updateData.client_name
        }
      });
      if (isExist?.length) {
        return false;
      }
      await this.ctx.model.query(`update affiliate_publisher_group set name = :name, utime = :utime, company = :company where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            name: updateData.client_name,
            company: updateData.company_name,
            utime: moment().unix(),
          }
        });

      return true;
    }
    if (curClientInfo.coop_bus.includes('Affiliate,Advertiser Group')) {
      const isExist = await this.ctx.model.query(`SELECT * FROM affiliate_advertiser_group WHERE name = :name and id != ${curClientInfo.association_id}`, {
        type: QueryTypes.SELECT,
        replacements: {
          name: updateData.client_name
        }
      });
      if (isExist?.length) {
        return;
      }
      const splitCoop = updateData.coop_bus.split(',');
      let type = splitCoop.pop(); // 最后一个
      if (type === 'Affiliate / Agency') {
        type = 'affiliate/agency';
      } else if (type === 'Direct') {
        type = 'direct';
      } else if (type === 'Owned') {
        type = 'owned';
      } else if (type === 'Other') {
        type = 'other';
      }

      await this.ctx.model.query(`update affiliate_advertiser_group set name = :name, pay_type = :pay_type, utime = :utime, company_name = :company_name, country = :country, address = :address, payment_legal_name = :payment_legal_name, co_type = :co_type where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            name: updateData.client_name,
            company_name: updateData.company_name,
            utime: moment().unix(),
            pay_type: updateData.pay_type === 'Pre-Payment' ? 'advance_pay' : 'post_pay',
            country: updateData.country,
            address: updateData.comp_address,
            payment_legal_name: updateData.payment_legal_name,
            co_type: type
          }
        });

      return true;
    }
    if (curClientInfo.coop_bus === 'Adx,Demand Group' || curClientInfo.coop_bus === 'Commercialize,Interative Demand Group') {
      const isExist = await this.ctx.model.query(`SELECT * FROM demand_source WHERE name = :name and id != ${curClientInfo.association_id}`, {
        type: QueryTypes.SELECT,
        replacements: {
          name: updateData.client_name
        }
      });
      if (isExist?.length) {
        return;
      }

      await this.ctx.model.query(`update demand_source set name = :name, company = :company, country = :country, address = :address, payment_legal_name = :payment_legal_name where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            name: updateData.client_name,
            company: updateData.company_name,
            country: updateData.country,
            address: updateData.comp_address,
            payment_legal_name: updateData.payment_legal_name
          }
        });
      return true;
    }

    if (curClientInfo.coop_bus.includes('Adx,Publisher Group') || ['Commercialize,Interative Publisher Group', 'Other,H5 Content Provider', 'Other,H5 Traffic Channel'].includes(curClientInfo.coop_bus)) {

      const isExist = await this.ctx.model.query(`SELECT * FROM publisher_group WHERE name = :name and id != ${curClientInfo.association_id}`, {
        type: QueryTypes.SELECT,
        replacements: {
          name: updateData.client_name,
        }
      });
      if (isExist?.length) {
        return;
      }
      let type = 'SDK';
      if (updateData.coop_bus.includes('Adx,Publisher Group')) {
        const splitCoop = updateData.coop_bus.split(',');
        type = splitCoop.pop(); // 最后一个
      }
      await this.ctx.model.query(`update publisher_group set name = :name, company = :company, utime = :utime, type = :type where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            name: updateData.client_name,
            company: updateData.company_name,
            utime: moment().unix(),
            type
          }
        });
      return true;
    }
    return true;
  }

  /**
   * 修改广告系统客户BD信息
   */
  async syncUpdateAdsClientBD(clientId: number, bd: string) { // 广告系统存的bd是name
    if (!clientId) { return; }
    const curClientInfo = await this.service.customerManage.pool.findOne({
      where: {
        id: clientId
      },
      attributes: ['id', 'coop_bus', 'association_id'],
      raw: true
    });

    if (curClientInfo.coop_bus === 'Affiliate,Publisher Group') {
      await this.ctx.model.query(`update affiliate_publisher_group set bd = :bd where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            bd
          }
        });

      return true;
    }
    if (curClientInfo.coop_bus.includes('Affiliate,Advertiser Group')) {

      await this.ctx.model.query(`update affiliate_advertiser_group set bd = :bd where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            bd
          }
        });

      return true;
    }
    if (curClientInfo.coop_bus === 'Adx,Demand Group' || curClientInfo.coop_bus === 'Commercialize,Interative Demand Group') {

      await this.ctx.model.query(`update demand_source set bd = :bd where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            bd
          }
        });
      return true;
    }

    if (curClientInfo.coop_bus.includes('Adx,Publisher Group') || ['Other,H5 Content Provider', 'Other,H5 Traffic Channel', 'Commercialize,Interative Publisher Group'].includes(curClientInfo.coop_bus)) {

      await this.ctx.model.query(`update publisher_group set bd = :bd where id = ${curClientInfo.association_id}`,
        {
          type: QueryTypes.UPDATE,
          replacements: {
            bd

          }
        });
      return true;
    }
    return true;
  }

  async create(data) {
    const { ctx } = this;
    Reflect.deleteProperty(data, 'id');
    const adsSystemResult = await this.syncFlatAdsDB(data);
    if (!adsSystemResult) {
      return {
        isSuccess: false,
        msg: `Customer names are not allowed to be repeated。`
      };
    }
    data.association_id = adsSystemResult.id;

    const result = await ctx.model.CustomerManage.Pool.create(data, { individualHooks: true, egg: this });
    return {
      isSuccess: true,
      result
    };
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const adsSystemResult = await this.syncUpdateAdsClientData(query.id, data);
    if (!adsSystemResult) {
      return {
        isSuccess: false,
        msg: `Customer names are not allowed to be repeated。`
      };
    }
    // 同步修改主线索的company
    this.service.clueManage.pool.update({
      client_id: query.id,
      clue_identity: 1
    }, {
      company_name: data.company_name,
      customer_name: data.client_name
    });
    const result = await ctx.model.CustomerManage.Pool.update(data, { where: query, individualHooks: true, egg: this });

    return {
      isSuccess: true,
      result
    };
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.CustomerManage.Pool.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async clientCreate(values) {
    // 公司名称 +  业务类型 唯一
    const isExist = await this.findOne({
      where: {
        client_name: values.client_name,
        bus_line: values.bus_line
      }
    });
    if (isExist) {
      return {
        isSuccess: false,
        msg: 'Customer names are not allowed to be repeated。'
      };
    }
    const { isSuccess, result, msg } = await this.create(values);
    if (!isSuccess) {
      return {
        isSuccess,
        msg
      };
    }
    return {
      isSuccess: true,
      result
    };
  }
}
