
import { Service } from 'egg';

export default class CrmTodoListStashService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.DingtalkTodo.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.DingtalkTodo.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.SystemNotify.DingtalkTodo.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.DingtalkTodo.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.SystemNotify.DingtalkTodo.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.SystemNotify.DingtalkTodo.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
}
