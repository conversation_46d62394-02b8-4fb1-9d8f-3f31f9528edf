
import { Service } from 'egg';
import moment from 'moment';
import sequelize from 'sequelize';
import { Op } from 'sequelize';
import { NODE1_RECEIVER } from '../../lib/globalVar';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../../lib/systemEvent';

export default class CrmNotifyService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.Notify.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.Notify.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.SystemNotify.Notify.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.SystemNotify.Notify.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.SystemNotify.Notify.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.SystemNotify.Notify.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
  /**
   * 获取指定节点的通知数据, 默认是昨天
   */
  async getKeyNodeNotify({
    notify_type,
    relation_type,
    time_range = [
      moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss')
    ],
    group
  }) {
    const findParams: Record<string, any> = {
      where: {
        notify_type,
        relation_type,
        ctime: {
          [Op.between]: time_range
        },

      },
      raw: true,
      attributes: ['receiver_id', 'creator', 'other_info', 'relation_id'],
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['pub_name', 'd_union_id', 'd_user_id']
        }
      ]
    };
    if (group && Array.isArray(group)) {
      findParams.group = group;
      findParams.attributes.push([sequelize.fn('count', sequelize.col('receiver_id')), 'count']);
    }
    const notifys = await this.service.systemNotify.notify.find(findParams);
    return notifys;
  }

  /**
   * 节点1：录入线索后，通知BD leader
   */
  async sendClueNotify({
    relation_id
  }) {
    const result = await this.ctx.service.systemNotify.notify.create({
      relation_type: 'clue',
      relation_id,
      receiver_id: NODE1_RECEIVER,
      notify_type: 'node1',
      creator: this.ctx.user.pub_name
    });
    return result;
  }
  /**
   * 节点2：商务负责人分配线索给BD 通知BD 本人
   */
  async sendClueAssignNotify({
    relation_id,
    bd_id,
    note
  }) {
    const result = await this.ctx.service.systemNotify.notify.create({
      relation_type: 'clue',
      relation_id,
      receiver_id: bd_id, // 通知给这个BD
      notify_type: 'node2',
      creator: this.ctx.user?.pub_name,
      remark: note
    });
    return result;
  }

  /**
   * 节点3： BD之间转让线索，通知被转让的BD本人
   */
  async sendClueTransferNotify({
    relation_id,
    bd_id,
    note
  }) {
    const result = await this.ctx.service.systemNotify.notify.create({
      relation_type: 'clue',
      relation_id,
      receiver_id: bd_id, // 通知给这个BD
      notify_type: 'node3',
      creator: this.ctx.user?.pub_name,
      remark: note
    });
    return result;
  }

  /**
   * 节点6: 客户回退到线索池通知
   */
  async sendClueRollbackNotify({
    relation_id,
    receiver_id,
    other_info
  }) {
    const result = await this.ctx.service.systemNotify.notify.create({
      relation_type: 'clue',
      relation_id,
      receiver_id,
      notify_type: 'node6',
      creator: this.ctx.user?.pub_name || 'system',
      other_info
    });
    return result;
  }

  async commonNoticeToSystemAndDingtalk({
    notifyUserId,
    title,
    otherInfo = {},
    jumpPath
  }) {
    const creator = this.ctx?.user?.pub_name || 'system';

    await this.ctx.service.systemNotify.notify.create({
      receiver_id: notifyUserId,
      notify_type: 'common',
      other_info: {
        ...otherInfo,
        path: jumpPath,
        title,
      },
      creator
    });

    const userInfo = await this.service.authority.user.findOne({
      where: {
        id: notifyUserId
      },
      attributes: ['pub_name', 'd_union_id'],
      raw: true
    });
    if (!userInfo.d_union_id) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `发送待办失败`,
        title: `${userInfo.pub_name} 未绑定钉钉ID`
      });
      return;
    }
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}${jumpPath}`;

    // 发送待办
    await this.service.ddCloud.sendNotificationToDo({
      executor_id: userInfo.d_union_id,
      title,
      jump_url: jumpUrlOrigin
    });
  }

}
