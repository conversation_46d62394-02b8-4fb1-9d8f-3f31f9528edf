
import { Service } from 'egg';
import sequelize, { Op } from 'sequelize';
import { userIsADMI<PERSON>, userIsBDLeader, userIsOPLeader } from '../../lib/constant';

export default class CrmTodoManagerService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.CrmTodoManager.Index.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.CrmTodoManager.Index.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.CrmTodoManager.Index.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.CrmTodoManager.Index.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.CrmTodoManager.Index.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.CrmTodoManager.Index.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async getOverallData() {
    const { ctx } = this;
    const where: any = {};
    const query = ctx.query;
    const opAnd: any[] = [];
    if (query.global_user) {
      where.owner = sequelize.literal(`FIND_IN_SET(${query.global_user}, owner)`);
    }
    let pageSql = '';
    this.userRoleFilterHandler(opAnd);

    if (opAnd.length) {
      where[Op.and] = opAnd;
    }

    const result = await this.findOne({
      attributes: [
        [sequelize.fn('sum', sequelize.literal('case when status in (1, 3) then 1 else 0 end')), 'all_pending'],
        [sequelize.fn('sum', sequelize.literal('case when type = 1 and status in (1, 3) then 1 else 0 end')), 'finance'],
        [sequelize.fn('sum', sequelize.literal('case when type = 2 and status in (1, 3) then 1 else 0 end')), 'contract'],
        [sequelize.fn('sum', sequelize.literal('case when type = 3 and status in (1, 3) then 1 else 0 end')), 'leads'],
        [sequelize.fn('sum', sequelize.literal('case when status in (3) then 1 else 0 end')), 'task_overdue'],
      ],
      raw: true,
      where,
      logging(sql) {
        pageSql = sql;
      }
    });
    return {
      result,
      sql: ctx.locals.env === 'prod' ? '' : pageSql
    };
  }

  async userRoleFilterHandler(opAnd) {
    const currentUser = this.ctx.user;
    const isAdmin = userIsADMIN(currentUser);
    const isBDLeader = userIsBDLeader(currentUser);
    const isOPLeader = userIsOPLeader(currentUser);
    const isBusinessUser = !isAdmin && !isBDLeader && !isOPLeader; // 非这三个角色，都当成业务角色

    if (isBDLeader) {
      const bdMember = currentUser.bd_member;
      // 可以看到自己和下面子成员的数据
      const opOr: any[] = [
        sequelize.literal(`FIND_IN_SET(${currentUser.id}, owner)`),
      ];
      if (bdMember) {
        opOr.push(...bdMember.split(',').map(owner => sequelize.literal(`FIND_IN_SET(${owner}, owner)`)));
      }
      opAnd.push({
        [Op.or]: opOr
      });
    } else if (isOPLeader) {
      const opMember = currentUser.op_member;
      // 可以看到自己和下面子成员的数据
      const opOr: any[] = [
        sequelize.literal(`FIND_IN_SET(${currentUser.id}, owner)`)
      ];
      if (opMember) {
        opOr.push(...opMember.split(',').map(owner => sequelize.literal(`FIND_IN_SET(${owner}, owner)`)));
      }
      opAnd.push({
        [Op.or]: opOr
      });
    } else if (isBusinessUser) { // 只能看自己的
      opAnd.push(sequelize.literal(`FIND_IN_SET(${currentUser.id}, owner)`));
    }
  }
}
