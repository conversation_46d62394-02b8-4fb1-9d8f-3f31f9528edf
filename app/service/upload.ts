import { Service } from 'egg';
import path from 'path';
import fs from 'fs';
import FormStream from 'formstream';
import { write } from 'await-stream-ready';
import sendToWormhole from 'stream-wormhole';
import xlsx from 'xlsx';
import { join } from 'path';
import { readFileSync, writeFileSync, unlink, createWriteStream } from 'fs';
import axios from 'axios';
import { SavePath } from '../lib/constant';

interface MultiFileType {
  form: any;
  filename: string;
  filePath: string;
}

export default class UploadService extends Service {
  async saveToLocalFile(options) {
    const { ctx } = this;
    const { savePath, name } = options;
    const baseDir = ctx.app.baseDir;
    const stream = await ctx.getFileStream();
    const filename = path.basename(stream.filename);
    const result = { form: null, filename, filePath: '' };
    const filePath = path.join(baseDir, `${savePath}/`, typeof name === 'function' ? name(filename) : name || filename);
    // 判断文件夹是否存在， 不存在则创建
    const hasFolder = fs.existsSync(path.join(baseDir, savePath));
    if (!hasFolder) {
      fs.mkdirSync(path.join(baseDir, savePath));
    }
    const writeStream = fs.createWriteStream(filePath);
    try {
      // 写入文件
      await write(stream.pipe(writeStream));
      // 调用接口上传
      const form = new FormStream();
      form.file('file', filePath);
      result.form = form;
      result.filePath = filePath;
    } catch (err) {
      // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
      await sendToWormhole(stream);
      throw err;
    }
    return result;
  }

  // 多文件上传式
  // async saveToLocalMultiFile(options) {
  //   const { ctx } = this;
  //   const { savePath } = options;
  //   const baseDir = ctx.app.baseDir;
  //   const parts = await ctx.multipart();
  //   let part;
  //   const result: MultiFileType[] = [];
  //   // parts() 返回 promise 对象
  //   while ((part = await parts()) != null) {
  //     const filename = part.filename;
  //     const tempObj = {
  //       form: null,
  //       filename,
  //       filePath: ''
  //     };
  //     const filePath = path.join(baseDir, `${savePath}/`, filename);
  //     // 判断文件夹是否存在， 不存在则创建
  //     const hasFolder = fs.existsSync(path.join(baseDir, savePath));
  //     if (!hasFolder) {
  //       fs.mkdirSync(path.join(baseDir, savePath));
  //     }
  //     const writeStream = fs.createWriteStream(filePath);
  //     try {
  //       // 写入文件
  //       await write(part.pipe(writeStream));
  //       // 调用接口上传
  //       const form = new FormStream();
  //       form.file('file', filePath);
  //       tempObj.form = form;
  //       tempObj.filePath = filePath;
  //       result.push(tempObj);
  //     } catch (err) {
  //       // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
  //       await sendToWormhole(part);
  //       throw err;
  //     }
  //   }
  //   return result;
  // }

  async saveToOssFile({ savePath, form }) {
    const { ctx } = this;
    const { config } = ctx.app;
    try {
      const url = `${config.uploadUrl.oss}&filename=${savePath}&bucket=dsp-adcreative`;
      const result = await ctx.curl(url, {
        method: 'POST',
        headers: form.headers(),
        stream: form,
        timeout: 1000 * 30 // 20秒内请求完成
      });
      return result;
    } catch (error) {
      this.ctx.logger.error(`upload image stream form error: ${error}`);
      return {};
    }
  }
  async deleteFromLocalFile() { }

  getFileBuffers(stream: any) {
    const buffers = [];
    return new Promise((resolve, reject) => {
      stream.on('data', chunk => {
        buffers.push(chunk);
      }).on('end', () => {
        let buffer = null;
        buffer = Buffer.concat(buffers);
        const workbook = xlsx.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const result = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName], { defval: null });
        resolve(result);
      }).on('error', err => {
        reject(err);
      });
    });
  }

  async downloadFileFromPath({
    downloadUri,
    fileName
  }) {
    const stashFilePath = join(this.ctx.app.baseDir, `${SavePath}/`, fileName);
    const result = await axios({
      method: 'GET',
      url: downloadUri,
      responseType: 'stream'
    }).then((response) => {
      const writer = createWriteStream(stashFilePath);
      response.data.pipe(writer);
      return new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
      });
    }).then(() => {
      console.log('文件下载完成');
      return {
        isSuccess: true,
        message: '',
        stashFilePath
      };
    }).catch((err) => {
      return {
        isSuccess: false,
        message: err.message,
        stashFilePath
      };
    });
    return result;
  }
}
