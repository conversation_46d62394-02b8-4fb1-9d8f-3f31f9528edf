import { Service } from 'egg';

export default class AuthorityService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Authority.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Authority.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.Authority.Authority.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    data.creator = data.modifier = ctx.user.pub_name;
    const result = await ctx.model.Authority.Authority.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    let result = {};
    if (Array.isArray(data)) {
      result = await Promise.all(data.map(item => ctx.model.Authority.Authority.update(item, { where: { id: item.id } })));
    } else {
      result = await ctx.model.Authority.Authority.update(data, { where: query, individualHooks: true, egg: this });
    }

    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.Authority.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async findByIdAndDelete(id, data) {
    const result = await this.update(id, data);
    return result;
  }
}
