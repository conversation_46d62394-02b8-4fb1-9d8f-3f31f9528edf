import { Service } from 'egg';

export default class RoleService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Role.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Role.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.Authority.Role.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Role.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data, transaction?: any) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.Role.update(data, { where: query, individualHooks: true, egg: this, transaction });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.Role.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
  async distinctField(fieldVal) {
    const { ctx } = this;
    const result = await ctx.model.Authority.Role.aggregate(fieldVal, 'DISTINCT', { plain: false });
    const fieldList: any = [];
    result.forEach((item: any) => {
      const itemStr = item.DISTINCT;
      if (itemStr) {
        fieldList.push(itemStr);
      }
    });
    return fieldList;
  }
}
