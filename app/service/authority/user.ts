import { Service } from 'egg';
import { ADMIN_KEY, ADV_BD_KEY, ADV_OP_KEY, ASSET_GROUP_KEY, BD_ASSISTANT_KEY, BD_LEADER_KEY, FINANCE_KEY, LEGAL_KEY, MARKETER_KEY, OP_LEADER_KEY, PROGRAMMATIC_OP_KEY, PUB_BD_KEY, PUB_OP_KEY, SUPER_ADMIN_KEY, TAX_KEY } from '../../lib/constant';

interface ICommonRecord {
  label: string;
  value: number | string;
  aud_type: string[];
}

export default class UserService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.Authority.User.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.Authority.User.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.Authority.User.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.Authority.User.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.User.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.User.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async findByIdAndDelete(id, data) {
    const result = await this.update(id, data);
    return result;
  }

  /**
   * 获取指定角色的用户
   */
  async getByRoleUsers() {

    const advOps: ICommonRecord[] = [];
    const pubOps: ICommonRecord[] = [];
    const ops: ICommonRecord[] = [];
    const advBds: ICommonRecord[] = [];
    const pubBds: ICommonRecord[] = [];
    const bds: ICommonRecord[] = [];
    const bdLeaders: (ICommonRecord)[] = [];
    const opLeaders: (ICommonRecord)[] = [];
    const bdAssistants: ICommonRecord[] = [];
    const mkts: ICommonRecord[] = [];
    const finances: ICommonRecord[] = [];
    const taxs: ICommonRecord[] = [];
    const legals: ICommonRecord[] = [];
    const assetGroups: ICommonRecord[] = [];
    const programmaticOps: ICommonRecord[] = [];

    const superAdmins: ICommonRecord[] = [];
    const admins: ICommonRecord[] = [];

    const omsRoleObj: any = {
      model: this.ctx.model.Authority.Role,
      as: 'crm_oms_role',
      attributes: { exclude: ['ctime', 'utime', 'id'] },
      required: false
    };
    const UserRoleObj: any = {
      model: this.ctx.model.Authority.UserRole,
      attributes: {
        exclude: ['user_code', 'ctime', 'utime']
      },
      include: omsRoleObj,
      required: false
    };

    const params = {
      where: {
        role_type: 1,
        // status: 1 // 不需要过滤下线的角色
      },
      attributes: ['id', 'pub_name', 'crm_audit_type', 'd_user_id', 'bd_member', 'op_member'],
      order: [
        ['id', 'DESC'],
      ],
      include: [
        UserRoleObj,
        {
          model: this.ctx.model.Authority.AdsUserTable,
          attributes: ['name', 'id']
        }
      ],
    };
    const result = await this.find(params);

    result.forEach(item => {
      if (!item.crm_oms_user_roles?.length) { return; }
      const codes: any[] = item.crm_oms_user_roles.map(item => item.role_code) || [];
      const useName = item.cms_auth?.dataValues?.name || item.pub_name;
      const data: ICommonRecord & { aud_type?: string[], d_user_id?: string, bd_member?: string, op_member?: string } = {
        label: useName,
        value: String(item.id),
        aud_type: item.crm_audit_type ? item.crm_audit_type.split(',') : [],
        d_user_id: item.d_user_id,
        bd_member: item.bd_member,
        op_member: item.op_member
      };
      if (codes.includes(SUPER_ADMIN_KEY)) {
        superAdmins.push(data);
      }
      if (codes.includes(ADMIN_KEY)) {
        admins.push(data);
      }
      if (codes.includes(ADV_OP_KEY)) {
        // 上游运营
        advOps.push(data);
      }
      if (codes.includes(PUB_OP_KEY)) {
        // 下游运营
        pubOps.push(data);
      }
      if (codes.includes(ADV_OP_KEY) || codes.includes(PUB_OP_KEY)) {
        // 运营
        ops.push(data);
      }
      if (codes.includes(ADV_BD_KEY)) {
        // 上游BD
        advBds.push(data);
      }
      if (codes.includes(PUB_BD_KEY)) {
        // 下游BD
        pubBds.push(data);
      }
      if (codes.includes(ADV_BD_KEY) || codes.includes(PUB_BD_KEY)) {
        // BD
        bds.push(data);
      }
      if (codes.includes(BD_LEADER_KEY)) {
        // BD Leader
        bdLeaders.push(data);
      }
      if (codes.includes(OP_LEADER_KEY)) {
        opLeaders.push(data);
      }
      if (codes.includes(PROGRAMMATIC_OP_KEY)) {
        programmaticOps.push(data);
      }
      if (codes.includes(BD_ASSISTANT_KEY)) {
        // BD Assistant
        bdAssistants.push(data);
      }
      if (codes.includes(MARKETER_KEY)) {
        // 市场
        mkts.push(data);
      }
      if (codes.includes(FINANCE_KEY)) {
        // 财务
        finances.push(data);
      }
      if (codes.includes(TAX_KEY)) {
        // 税务
        taxs.push(data);
      }
      if (codes.includes(LEGAL_KEY)) {
        // 法务
        legals.push(data);
      }
      if (codes.includes(ASSET_GROUP_KEY)) {
        // 资产组
        assetGroups.push(data);
      }
    });
    return {
      advOps,
      pubOps,
      ops,
      advBds,
      pubBds,
      bds,
      bdLeaders,
      opLeaders,
      bdAssistants,
      mkts,
      finances,
      legals,
      assetGroups,
      taxs,
      programmaticOps,

      superAdmins,
      admins,

      // auditors
      auditors: [
        ...assetGroups,
        ...finances,
        ...taxs,
        ...legals
      ] as Array<ICommonRecord & { aud_type?: string[], d_user_id?: string }>,
    };
  }
}
