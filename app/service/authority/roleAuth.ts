import { Service } from 'egg';
import { OperateStatus } from '../../lib/constant';

export default class RoleAuthService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.Authority.RoleAuth.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.Authority.RoleAuth.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.Authority.RoleAuth.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.Authority.RoleAuth.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async bulkCreate(records, options) {
    const { ctx } = this;
    const result = await ctx.model.Authority.RoleAuth.bulkCreate(records, options);
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.RoleAuth.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.RoleAuth.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async bulkUpdate(data, options) {
    const { ctx } = this;
    try {
      const result = await ctx.model.Authority.RoleAuth.bulkCreate(data, {
        updateOnDuplicate: options.updateOnDuplicate
      });
      return {
        status: OperateStatus.Success,
        data: result
      };
    } catch (e) {
      return Promise.reject(e);
    }
  }
}
