import { Service } from 'egg';

export default class UserRoleService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.Authority.UserRole.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.Authority.UserRole.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.Authority.UserRole.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.Authority.UserRole.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async bulkCreate(records, options) {
    const { ctx } = this;
    const result = await ctx.model.Authority.UserRole.bulkCreate(records, options);
    return result;
  }

  async update(query, data, transaction?: any) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.UserRole.update(data, { where: query, individualHooks: true, egg: this, transaction });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.Authority.UserRole.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
  async distinctField(fieldVal) {
    const { ctx } = this;
    const result = await ctx.model.Authority.UserRole.aggregate(fieldVal, 'DISTINCT', { plain: false });
    const fieldList: any = [];
    result.forEach((item: any) => {
      const itemStr = item.DISTINCT;
      if (itemStr) {
        fieldList.push(itemStr);
      }
    });
    return fieldList;
  }
}
