import { Service } from 'egg';
import { ADS_PRESET_ROLE_CODE, userIsAdvBD, userIsAdvOP, userIsBDLeader, userIsOPLeader, userIsPubBD, userIsPubOP } from '../lib/constant';
import { Op, QueryTypes } from 'sequelize';
// import { NotifyEvent } from '../modules/redis';

const ALL_DATA_ROLE_CODE = [
  ADS_PRESET_ROLE_CODE.SUPER_ADMIN,
  ADS_PRESET_ROLE_CODE.ADMIN,
  ADS_PRESET_ROLE_CODE.PA_Manager,
  ADS_PRESET_ROLE_CODE.INTERACTIVE_MANAGER,
  ADS_PRESET_ROLE_CODE.OP_LEADER,
  ADS_PRESET_ROLE_CODE.BD_LEADER,
  ADS_PRESET_ROLE_CODE.FINANCE,
  ADS_PRESET_ROLE_CODE.ALL_DATA_BUT_NOT_ADMIN

];

// 如果是Admin 和 对应角色多选，则属于Admin
const userIsAdsAm = (roleCodes: string[]) => {
  return roleCodes.includes(ADS_PRESET_ROLE_CODE.ADV_OP) && !roleCodes.some(code => ALL_DATA_ROLE_CODE.includes(code));
};

const userIsAdsPm = (roleCodes: string[]) => {
  return roleCodes.includes(ADS_PRESET_ROLE_CODE.PUB_OP) && !roleCodes.some(code => ALL_DATA_ROLE_CODE.includes(code));
};

const userIsAdsAdvBd = (roleCodes: string[]) => {
  return roleCodes.includes(ADS_PRESET_ROLE_CODE.ADV_BD) && !roleCodes.some(code => ALL_DATA_ROLE_CODE.includes(code));
};

const userIsAdsPubBd = (roleCodes: string[]) => {
  return roleCodes.includes(ADS_PRESET_ROLE_CODE.PUB_BD) && !roleCodes.some(code => ALL_DATA_ROLE_CODE.includes(code));
};

export default class RoleService extends Service {
  async bdMemberToInfo() {
    // 首先自己的数据是一定要看到的。
    const selfId = this.ctx.user?.id;
    // 在获取属于的bd角色
    const bdMember = this.ctx.user?.bd_member;
    // 存储的是id分隔的字符串
    const ids = [...bdMember.split(','), selfId].filter(it => it); // 防止空字符串的情况
    if (!ids.length) {
      return [];
    }

    const res = await this.app.model.query(`
      select id, pub_name from affiliate_publisher where id in (${ids.join(',')}) and role_type = 1
    `, {
      type: QueryTypes.SELECT
    });

    return res;
  }

  async opMemberToInfo() {
    // 首先自己的数据是一定要看到的。
    const selfId = this.ctx.user?.id;
    // 在获取属于的bd角色
    const opMember = this.ctx.user?.op_member;
    // 存储的是id分隔的字符串
    const ids = [...opMember.split(','), selfId].filter(it => it); // 防止空字符串的情况
    if (!ids.length) {
      return [];
    }
    const res = await this.app.model.query(`
      select id, pub_name from affiliate_publisher where id in (${ids.join(',')}) and role_type = 1
    `, { type: QueryTypes.SELECT });
    return res;
  }

  /** 根据owner获取对应ID列表 */
  async getIdListByAdvOwnerArr(owners: string[]) {
    const ownerFindInSets = owners.map((owner) => `find_in_set('${owner}', owner)`);

    const result = await this.app.model.query(`
      select id, owner, advertiser_group_id from affiliate_network_agency where ${ownerFindInSets.join(' OR ')}
    `, { type: QueryTypes.SELECT });

    let idList: number[] = [];
    if (result?.length > 0) {
      idList = result.map((item: any) => item.advertiser_group_id);
    }
    // console.log("idList:", idList);
    return idList;
  }

  async getIdListByPublisherOwner(owners: string[]) {
    const ownerFindInSets = owners.map((owner) => `find_in_set('${owner}', owner)`);

    const result = await this.app.model.query(`
      select id, owner, affiliate_publisher_group from affiliate_publisher where ${ownerFindInSets.join(' OR ')}
    `, { type: QueryTypes.SELECT });

    let idList: number[] = [];
    if (result?.length > 0) {
      idList = result.map((item: any) => item.affiliate_publisher_group);
    }
    return idList;
  }

  async opLeaderHandlerAdvAndPub(successCallback: (advIds, pubIds) => void, advSuccessCallback, pubSuccessCallback, nullCallback) {
    const memberInfo = await this.service.role.opMemberToInfo();
    const accountIds = memberInfo.map(it => it.id);

    const advIds = await this.getIdListByAdvOwnerArr(accountIds);
    const pubIds = await this.getIdListByPublisherOwner(accountIds);
    if (advIds?.length && pubIds?.length) {
      successCallback(advIds, pubIds);
    } else if (advIds?.length) {
      advSuccessCallback(advIds);
    } else if (pubIds?.length) {
      pubSuccessCallback(pubIds);
    } else {
      nullCallback();
    }
  }

  async amAndPmHandler(successCallback: (advIds, pubIds) => void, advSuccessCallback, pubSuccessCallback, nullCallback) {
    // 通过am 查询 advid
    const advIds = await this.getIdListByAdvOwnerArr([this.ctx.user?.id]);
    const pubIds = await this.getIdListByPublisherOwner([this.ctx.user?.id]);
    if (advIds?.length && pubIds?.length) {
      successCallback(advIds, pubIds);
    } else if (advIds?.length) {
      advSuccessCallback(advIds);
    } else if (pubIds?.length) {
      pubSuccessCallback(pubIds);
    } else {
      nullCallback();
    }

  }

  async amHandler(successCallback: (result) => void, nullCallback: () => void) {
    // 通过am 查询 advid
    const advIds = await this.getIdListByAdvOwnerArr([this.ctx.user?.id]);
    if (advIds?.length) {
      successCallback(advIds);
    } else {
      nullCallback();
    }
  }

  async pmHandler(successCallback: (result) => void, nullCallback: () => void) {
    // 通过owner 反查publisherId
    const pubIds = await this.getIdListByPublisherOwner([this.ctx.user?.id]);
    if (pubIds?.length) {
      successCallback(pubIds);
    } else {
      nullCallback();
    }
  }

  async bdMemberToAdvGroupIds() {
    const bdMemberInfo = await this.bdMemberToInfo();
    const bdNames = bdMemberInfo.map(it => it.pub_name);

    const res = await this.app.model.query(`
      select id from affiliate_advertiser_group where  bd in (${bdNames.map(it => `'${it}'`).join(',')})
    `, { type: QueryTypes.SELECT });
    return res.map(it => it.id);
  }

  async bdMemberToPubGroupIds() {
    const bdMemberInfo = await this.bdMemberToInfo();
    const bdNames = bdMemberInfo.map(it => it.pub_name);

    const res = await this.app.model.query(`
      select id from affiliate_publisher_group where  bd in (${bdNames.map(it => `'${it}'`).join(',')})
    `, { type: QueryTypes.SELECT });
    return res.map(it => it.id);
  }

  async bdLeaderHandlerAdvAndPub(successCallback: (advIds, pubIds) => void, advSuccessCallback, pubSuccessCallback, nullCallback) {
    const advGroupIds = await this.bdMemberToAdvGroupIds();
    const pubGroupIds = await this.bdMemberToPubGroupIds();
    if (advGroupIds?.length && pubGroupIds?.length) {
      successCallback(advGroupIds, pubGroupIds);
    } else if (advGroupIds?.length) {
      advSuccessCallback(advGroupIds);
    } else if (pubGroupIds?.length) {
      pubSuccessCallback(pubGroupIds);
    } else {
      nullCallback();
    }
  }

  async getAdvertisertGroupIdByBd(bd) {
    if (!bd) { return []; }
    const result = await this.app.model.query(`
      select id from affiliate_advertiser_group where  bd = '${bd}'
    `, { type: QueryTypes.SELECT });
    if (result?.length) {
      return result.map(it => it.id);
    }
    return [];
  }

  async getPublisherGroupIdByBd(bd) {
    if (!bd) { return []; }
    const result = await this.app.model.query(`
      select id from affiliate_publisher_group where  bd = '${bd}'
    `, { type: QueryTypes.SELECT });
    if (result?.length) {
      return result.map(it => it.id);
    }
    return [];
  }

  async advBdAndPubBdHandler(successCallback: (advIds, pubIds) => void, advSuccessCallback, pubSuccessCallback, nullCallback) {
    const advGroupIds = await this.getAdvertisertGroupIdByBd(this.ctx.user?.pub_name);
    const pubGroupIds = await this.getPublisherGroupIdByBd(this.ctx.user?.pub_name);
    if (advGroupIds?.length && pubGroupIds?.length) {
      successCallback(advGroupIds, pubGroupIds);
    } else if (advGroupIds?.length) {
      advSuccessCallback(advGroupIds);
    } else if (pubGroupIds?.length) {
      pubSuccessCallback(pubGroupIds);
    } else {
      nullCallback();
    }
  }

  async advBdHandler(successCallback: (result) => void, nullCallback: () => void) {
    const advGroupIds = await this.getAdvertisertGroupIdByBd(this.ctx.user?.pub_name);
    if (advGroupIds?.length) {
      successCallback(advGroupIds);
    } else {
      nullCallback();
    }
  }

  async pubBdHandler(successCallback: (result) => void, nullCallback: () => void) {
    const pubGroupIds = await this.getPublisherGroupIdByBd(this.ctx.user?.pub_name);
    if (pubGroupIds?.length) {
      successCallback(pubGroupIds);
    } else {
      nullCallback();
    }
  }

  async roleComposeStrCommon(sqlWhereDefault) {
    const orSql = [];
    if (userIsOPLeader(this.ctx.user)) {
      await this.opLeaderHandlerAdvAndPub(
        (advIds, pubIds) => orSql.push(`(adv_group IN ('${advIds.join('\',\'')}') and data_type = 'aff') OR (pub_group IN ('${pubIds.join('\',\'')}') and data_type = 'aff')`),
        (advIds) => orSql.push(` (adv_group IN ('${advIds.join('\',\'')}') and data_type = 'aff')`),
        (pubIds) => orSql.push(`(pub_group IN ('${pubIds.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(`adv_group IN ('-1')`),
      );
    }
    if (userIsAdvOP(this.ctx.user)) {

      await this.amHandler(
        ids => orSql.push(`(adv_group IN ('${ids.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(`adv_group IN ('-1')`),
      );
    }
    if (userIsPubOP(this.ctx.user)) {

      await this.pmHandler(
        (ids) => orSql.push(`(pub_group IN ('${ids.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(`(pub_group IN ('-1') and data_type = 'aff')`),
      );
    }
    if (userIsBDLeader(this.ctx.user)) {
      await this.bdLeaderHandlerAdvAndPub(
        (advGroupIds, pubGroupIds) => orSql.push(` (adv_group IN ('${advGroupIds.join('\',\'')}') and data_type = 'aff') OR (pub_group IN ('${pubGroupIds.join('\',\'')}') and data_type = 'aff')`),
        (advGroupIds) => orSql.push(`(adv_group IN ('${advGroupIds.join('\',\'')}') and data_type = 'aff')`),
        (pubGroupIds) => orSql.push(`(pub_group IN ('${pubGroupIds.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(`((adv_group = -1 or pub_group = -1) and data_type = 'aff')`)
      );
    }
    if (userIsAdvBD(this.ctx.user)) {
      // adv bd 角色
      /**
       * 1. 先查出 该 bd的所有adv group
       * 2. 找到adv group 下的 adv
       */

      await this.advBdHandler(
        (advGroupIds) => orSql.push(`(adv_group IN ('${advGroupIds.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(` adv_group = -1 `)
      );
    }
    if (userIsPubBD(this.ctx.user)) {
      // pub bd 角色
      /**
       * 1. 先查出 该 bd的所有pub group
       * 2. 找到pub group 下的 pub
       */

      await this.pubBdHandler(
        (pubGroupIds) => orSql.push(`(pub_group IN ('${pubGroupIds.join('\',\'')}') and data_type = 'aff')`),
        () => orSql.push(`pub_group = -1`)
      );
    }

    if (orSql.length) {
      sqlWhereDefault += `  (${orSql.join(' OR ')})`;
    }

    return sqlWhereDefault;
  }

  // 下面是adx的数据权限
  async getAdsUserRoleCode() {
    const { ctx, service } = this;
    const userId = this.ctx.user?.id;
    const userInfo = await this.service.authority.user.findOne({ where: { id: userId || -1 }, raw: true, attributes: ['ads_user'] });
    if (!userInfo) {
      return [];
    }
    const adsUserId = userInfo.ads_user || '-1';
    const sql = `select id, role_code from ads_user_role where user_code = ${adsUserId} and system = 'ads'`;
    const list = await this.app.model.query(sql, { type: QueryTypes.SELECT });
    return list.map(it => it.role_code);
  }

  async advOpToDemand() {
    const adsUserInfo = await this.getAdsUserInfo();
    if (!adsUserInfo.name) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const sql = `
      select id from demand_source where op = '${adsUserInfo.name}'
    `;
    const sqlRes = await this.app.model.query(sql, { type: QueryTypes.SELECT });
    const groupIds = sqlRes.map((item: any) => item.id);
    if (groupIds.length === 0) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const demandRes = await this.app.model.query(`select id, demand_name from ad_demand where demand_source_id in (${groupIds.map((it: number) => `'${it}'`).join(',')})`, { type: QueryTypes.SELECT });
    return {
      names: demandRes.map((item: any) => item.demand_name),
      ids: demandRes.map((item: any) => item.id),
      groupIds,
    };
  }
  async advBDToDemand() {
    const adsUserInfo = await this.getAdsUserInfo();
    if (!adsUserInfo.name) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const sql = `
      select id from demand_source where bd = '${adsUserInfo.name}'
    `;
    const sqlRes = await this.app.model.query(sql, { type: QueryTypes.SELECT });
    const groupIds = sqlRes.map((item: any) => item.id);
    if (groupIds.length === 0) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const demandRes = await this.app.model.query(`select id, demand_name from ad_demand where demand_source_id in (${groupIds.map((it: number) => `'${it}'`).join(',')})`, { type: QueryTypes.SELECT });

    return {
      names: demandRes.map((item: any) => item.demand_name),
      ids: demandRes.map((item: any) => item.id),
      groupIds,
    };
  }

  async getAdsUserInfo() {
    const userId = this.ctx.user?.id;
    const userInfo = await this.service.authority.user.findOne({ where: { id: userId }, raw: true, attributes: ['ads_user'] });
    if (!userInfo) {
      return {};
    }
    const adsUserId = userInfo.ads_user;
    const adsUserInfo = await this.app.model.query(`select id, name from cms_auth where id = ${adsUserId}`, { type: QueryTypes.SELECT });
    return adsUserInfo[0] || {};
  }

  async pubOpToPublisher() {
    const adsUserInfo = await this.getAdsUserInfo();
    if (!adsUserInfo.name) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const sql = `
      select id from publisher_group where op = '${adsUserInfo.name}'
    `;
    const sqlRes = await this.app.model.query(sql, { type: QueryTypes.SELECT });
    const publisherGroupIds = sqlRes.map((item: any) => item.id);
    if (publisherGroupIds.length === 0) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const publisherRes = await this.app.model.query(`select id, account from publisher where publisher_group_id in (${publisherGroupIds.map((it: number) => `'${it}'`).join(',')})`, { type: QueryTypes.SELECT });
    return {
      names: publisherRes.map((item: any) => item.account),
      ids: publisherRes.map((item: any) => item.id),
      groupIds: publisherGroupIds,
    };
  }

  async pubBDToPublisher() {
    const adsUserInfo = await this.getAdsUserInfo();
    if (!adsUserInfo.name) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const sql = `
      select id from publisher_group where bd = '${adsUserInfo.name}'
    `;
    const sqlRes = await this.app.model.query(sql, { type: QueryTypes.SELECT });
    const publisherGroupIds = sqlRes.map((item: any) => item.id);
    if (publisherGroupIds.length === 0) {
      return {
        names: ['-1'],
        ids: [-1],
        groupIds: [-1],
      };
    }
    const publisherRes = await this.app.model.query(`select id, account from publisher where publisher_group_id in (${publisherGroupIds.map((it: number) => `'${it}'`).join(',')})`, { type: QueryTypes.SELECT });
    return {
      names: publisherRes.map((item: any) => item.account),
      ids: publisherRes.map((item: any) => item.id),
      groupIds: publisherGroupIds,
    };
  }

  async getAdsRoleFilterSql(type: 'all' | 'revenue' | 'cost' = 'all') {

    const roleCodes = await this.getAdsUserRoleCode();

    const advIds = [];
    const advNames = [];
    const advGroupIds = [];
    const pubIds = [];
    const pubNames = [];
    const pubGroupIds = [];
    const groupOpWhere = [];
    if (userIsAdsAm(roleCodes) && (type === 'all' || type === 'revenue')) {
      const { names, ids, groupIds } = await this.advOpToDemand();
      advIds.push(...ids);
      advNames.push(...names);
      advGroupIds.push(...groupIds);
      groupOpWhere.push({
        demand_source: {
          [Op.in]: advGroupIds
        }
      });
    }
    if (userIsAdsAdvBd(roleCodes) && (type === 'all' || type === 'revenue')) {
      const { names, ids, groupIds } = await this.advBDToDemand();
      advIds.push(...ids);
      advNames.push(...names);
      advGroupIds.push(...groupIds);
      groupOpWhere.push({
        demand_source: {
          [Op.in]: advGroupIds
        }
      });
    }
    if (userIsAdsPm(roleCodes) && (type === 'all' || type === 'cost')) {
      const { names, ids, groupIds } = await this.pubOpToPublisher();
      pubIds.push(...ids);
      pubNames.push(...names);
      pubGroupIds.push(...groupIds);
      groupOpWhere.push({
        publisher_group_id: {
          [Op.in]: pubGroupIds
        }
      });
    }
    if (userIsAdsPubBd(roleCodes) && (type === 'all' || type === 'cost')) {
      const { names, ids, groupIds } = await this.pubBDToPublisher();
      pubIds.push(...ids);
      pubNames.push(...names);
      pubGroupIds.push(...groupIds);
      groupOpWhere.push({
        publisher_group_id: {
          [Op.in]: pubGroupIds
        }
      });
    }
    return {
      advIds,
      advNames,
      advGroupIds,
      pubIds,
      pubNames,
      pubGroupIds,
      groupOpWhere
    };

  }
}
