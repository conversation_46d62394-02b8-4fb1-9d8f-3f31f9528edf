import { Service } from 'egg';

export default class PayCmsLogService extends Service {
  async find(query, skip = 0, limit = 20) {
    const { ctx } = this;
    const result = await ctx.model.OperateLog.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.OperateLog.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.OperateLog.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.OperateLog.create(data);
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.OperateLog.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.OperateLog.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
  /** 写入操作日志 */
  async saveLog({ action, afterData, beforeData = {}, tableName = '', itemId, realPathname = '' }) {
    const { service, ctx } = this;
    const ip = ctx.get('X-Real-IP') || ctx.ip;
    const pathname = realPathname ? realPathname : ctx.headers.pathname;
    const url = ctx.url;
    const { pub_name } = ctx.user || {};
    const operator = pub_name;

    const data = {
      item_id: itemId,
      action,
      record: JSON.stringify(afterData),
      old_record: JSON.stringify(beforeData),
      table_name: tableName,
      operator,
      uid: pub_name,
      url,
      pathname,
      ip,
      ctime: new Date()
    };
    service.operateLog.create(data);
  }
  async distinctField(fieldVal, pathname) {
    const { ctx } = this;
    const result = await ctx.model.OperateLog.aggregate(fieldVal, 'DISTINCT', { plain: false, where: { pathname } });
    const fieldList: any = [];
    result.forEach((item: any) => {
      const itemStr = item.DISTINCT;
      if (itemStr) {
        fieldList.push(itemStr);
      }
    });
    return fieldList;
  }
}
