
import { Service } from 'egg';
import NP from 'number-precision';
import { SORT_DESC_LIST } from '../../lib/constant';
import sequelize, { literal, QueryTypes } from 'sequelize';
import { getPagingData } from '../../lib/tool';
import moment from 'moment';

export default class CommissionReportService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.CommissionReport.Index.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.CommissionReport.Index.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.CommissionReport.Index.count(query);
    return count;
  }

  async getCommissionList(params) {
    const dimensionList = params.dimensionStr.split(',');
    let repSql = '';
    const getCondition = async () => {
      const {
        startDate, endDate,
        sorter,
        billing_start_month, billing_end_month, adv_bd,
        adv_owner, pub_bd, pub_owner,
        paid_start_month, paid_end_month,
        adv_group, pub_group,
        custom_attributes = []
      } = params;
      const { offset, limit } = getPagingData(params);
      const Op = sequelize.Op;
      // 'source_type',
      const attributes = [

        'month', 'adv_group', 'payment_period', 'adv_bd', 'adv_owner', 'adv_type',
        'pub_group', 'pub_type', 'pub_bd', 'pub_owner', 'paid_month', 'paid_date',
        'overdue_payment_date', 'deduction', 'id', 'data_type',
        'generate_info',
        [sequelize.fn('sum', sequelize.col('billing_revenue')), 'billing_revenue'],
        [sequelize.fn('sum', sequelize.col('paid_revenue')), 'paid_revenue'],
        [sequelize.fn('sum', sequelize.col('pub_cost')), 'pub_cost'],
        [sequelize.fn('sum', sequelize.col('commissionable_amount')), 'commissionable_amount'],
        [sequelize.literal(`sum(paid_revenue) - sum(pub_cost)`), 'gross_profit'],
        [sequelize.literal(`(sum(paid_revenue) - sum(pub_cost)) / sum(paid_revenue)`), 'gross_profit_margin'],
        ...custom_attributes

      ];

      const sequelizeCondition: Record<string, any> = {
        where: {},
        group: dimensionList,
        attributes,
        raw: true,
        logging: (str: string) => {
          repSql = str;
        }
      };
      sequelizeCondition.limit = limit;
      sequelizeCondition.offset = offset;
      // 排序
      const [sortBy, orderBy] = sorter?.split?.(',') || [];
      if (sortBy && orderBy) {
        const orderByStr = (SORT_DESC_LIST.includes(orderBy)) ? 'DESC' : 'ASC';
        if (sortBy === 'month') {
          sequelizeCondition.order = [[sortBy, orderByStr]];
        } else if (sortBy === 'gross_profit_margin') {
          sequelizeCondition.order = [[sequelize.col(sortBy), orderByStr]];
        } else {
          sequelizeCondition.order = [[sequelize.fn('sum', sequelize.col(sortBy)), orderByStr]];
        }
      }
      if (startDate && endDate) {
        sequelizeCondition.where.yearmonth = {
          [Op.gte]: startDate,
          [Op.lte]: endDate,
        };
      }
      const inCondition = [
        'adv_type', 'pub_type',
      ];
      const affRoleFilterStr = await this.service.role.roleComposeStrCommon(``);
      const {
        advGroupIds: adxDemandSourceIds,
        pubGroupIds: adxPubGroupIds
      } = await this.service.role.getAdsRoleFilterSql();
      const roleFilterOr = [];
      if (adxDemandSourceIds?.length) {
        roleFilterOr.push({
          data_type: 'adx',
          adv_group: {
            [Op.in]: adxDemandSourceIds
          }
        });
      }
      if (adxPubGroupIds?.length) {
        roleFilterOr.push({
          data_type: 'adx',
          pub_group: {
            [Op.in]: adxPubGroupIds
          }
        });
      }
      const opAnd: any[] = [
      ];
      if (affRoleFilterStr) {
        roleFilterOr.push(sequelize.literal(affRoleFilterStr));
      }
      if (roleFilterOr.length) {
        opAnd.push({
          [Op.or]: roleFilterOr
        });
      }

      if (adv_group) {
        const advGroup = adv_group.split(',');
        const opOr = [];
        // find_in_sets
        opOr.push(...advGroup.map(it => {
          const [type, id] = it.split(':');
          return {
            [Op.and]: [
              {
                data_type: type,
              },
              {
                adv_group: id,
              }
            ]
          };
        }));
        opAnd.push({
          [Op.or]: opOr
        });
      }
      if (pub_group) {
        const pubGroup = pub_group.split(',');
        const opOr = [];
        opOr.push(...pubGroup.map(it => {
          const [type, id] = it.split(':');
          return {
            [Op.and]: [
              {
                data_type: type,
              },
              {
                pub_group: id,
              }
            ]
          };
        }));
        opAnd.push({
          [Op.or]: opOr
        });
      }
      if (adv_bd) {
        const advBd = adv_bd.split(',');
        // find_in_sets
        sequelizeCondition.where.adv_bd = {
          [Op.or]: advBd.map(it => sequelize.literal(`FIND_IN_SET('${it}', adv_bd)`))
        };
      }
      if (adv_owner) {
        const advOwner = adv_owner.split(',');
        // find_in_sets
        sequelizeCondition.where.adv_owner = {
          [Op.or]: advOwner.map(it => {
            const [id, name] = it.split(':');
            return sequelize.literal(`(FIND_IN_SET('${id}', adv_owner) or FIND_IN_SET('${name}', adv_owner))`);
          })
        };
      }
      if (pub_bd) {
        const pubBd = pub_bd.split(',');
        // find_in_sets
        sequelizeCondition.where.pub_bd = {
          [Op.or]: pubBd.map(it => sequelize.literal(`FIND_IN_SET('${it}', pub_bd)`))
        };
      }
      if (pub_owner) {
        const pubOwner = pub_owner.split(',');
        // find_in_sets
        sequelizeCondition.where.pub_owner = {
          [Op.or]: pubOwner.map(it => {
            const [id, name] = it.split(':');
            return sequelize.literal(`(FIND_IN_SET('${id}', pub_owner) or FIND_IN_SET('${name}', pub_owner))`);
          })
        };
      }
      if (billing_start_month && billing_end_month) {
        opAnd.push({
          month: {
            [Op.gte]: billing_start_month,
            [Op.lte]: billing_end_month,
          }
        });
      }
      if (paid_start_month && paid_end_month) {
        // paid_month存的是多个的，所以得用find_in_set, 时间范围
        const paidMonthDiff = moment(paid_end_month).diff(moment(paid_start_month), 'month');
        const paidMonthArr = [];
        for (let i = 0; i <= paidMonthDiff; i++) {
          paidMonthArr.push(moment(paid_start_month).add(i, 'month').format('YYYY-MM'));
        }
        sequelizeCondition.where.paid_month = {
          [Op.or]: paidMonthArr.map(it => sequelize.literal(`FIND_IN_SET('${it}', paid_month)`))
        };

      }

      if (opAnd?.length) {
        sequelizeCondition.where[Op.and] = opAnd;
      }
      // if (opOr?.length) {
      //   sequelizeCondition.where[Op.or] = opOr;
      // }

      inCondition.forEach((key) => {
        if (params[key]) {
          sequelizeCondition.where[key] = {
            [Op.in]: params[key].split(',')
          };
        }
      });

      return sequelizeCondition;
    };
    const sequelizeCondition = await getCondition();
    const records = await this.find(sequelizeCondition);
    const { total, totalResult } = await this.getTotalResult(sequelizeCondition);

    // 一些数据额外处理
    this.recordsTransform(records);

    await this.addEdgeData(records, {
      isNeedPublisherGroupName: dimensionList.includes('pub_group'),
      isNeedAdvertiserGroupName: dimensionList.includes('adv_group'),
      isNeedAdvOwnerName: dimensionList.includes('adv_owner'),
      isNeedPubOwnerName: dimensionList.includes('pub_owner'),
    });

    records.unshift(totalResult);

    const result = {
      records,
      total,
      repSql
    };
    return result;
  }

  recordsTransform(records: Array<Record<string, any>>) {
    records.forEach((item) => {

    });
  }

  /**
   *
   * 增加边缘数据，例如offer_name, publisher_name, advertiser_name
   */

  async addEdgeData(records: Array<Record<string, any>>, { isNeedOfferName, isNeedPublisherName, isNeedAdvertiserName, isNeedPublisherGroupName, isNeedAdvertiserGroupName, isNeedAdvOwnerName, isNeedPubOwnerName }: Record<string, boolean>) {
    if (records.length < 1) { return; }
    if (isNeedPublisherGroupName) {
      const affGroupId = records.filter(it => it.data_type === 'aff').map(item => item.pub_group);
      const adxGroupId = records.filter(it => it.data_type === 'adx').map(item => item.pub_group);
      let affGroupList = [];
      let adxGroupList = [];
      if (affGroupId.length) {
        const result = await this.app.model.query(`
        select id, name from affiliate_publisher_group where id in (${affGroupId.join(',')})
      `, { type: QueryTypes.SELECT });
        affGroupList = result;
      }
      if (adxGroupId.length) {
        const result = await this.app.model.query(`
        select id, name from publisher_group where id in (${adxGroupId.join(',')})
        `, { type: QueryTypes.SELECT });
        adxGroupList = result;
      }

      records.forEach(item => {
        if (item.data_type === 'adx') {
          item.publisher_group_name = adxGroupList.find(adxGroup => Number(adxGroup.id) === Number(item.pub_group))?.name;
          return;
        }
        item.publisher_group_name = affGroupList.find(publisherGroup => Number(publisherGroup.id) === Number(item.pub_group))?.name;
      });
    }
    if (isNeedAdvertiserGroupName) {
      const affGroupId = records.filter(it => it.data_type === 'aff').map(item => item.adv_group);
      const adxGroupId = records.filter(it => it.data_type === 'adx').map(item => item.adv_group);
      let advertiserGroupList = [];
      let adxGroupList = [];
      if (affGroupId.length) {
        const result = await this.app.model.query(`
        select id, name from affiliate_advertiser_group where id in (${affGroupId.join(',')})
      `, { type: QueryTypes.SELECT });
        advertiserGroupList = result;
      }
      if (adxGroupId.length) {
        const result = await this.app.model.query(`
        select id, name from demand_source where id in (${adxGroupId.join(',')})
        `, { type: QueryTypes.SELECT });
        adxGroupList = result;
      }

      records.forEach(item => {
        if (item.data_type === 'adx') {
          item.advertiser_group_name = adxGroupList.find(adxGroup => Number(adxGroup.id) === Number(item.adv_group))?.name;
          return;
        }
        item.advertiser_group_name = advertiserGroupList.find(advertiserGroup => Number(advertiserGroup.id) === Number(item.adv_group))?.name;
      });
    }
    if (isNeedAdvOwnerName) {
      const dataOwners = [...new Set(records.map(item => item.adv_owner ? item.adv_owner.split(',') : []).flat())];
      if (dataOwners.length) {
        const findPubNames = await this.app.model.query(`
        select id, pub_name from  affiliate_publisher where id in (${dataOwners.map(it => `'${it}'`).join(',')})
      `, { type: QueryTypes.SELECT });

        records.forEach(item => {
          const owners = item.adv_owner;
          if (!owners) { return; }
          const ownersSplit = owners.split(',');
          item.adv_owner_name = ownersSplit.map(id => findPubNames.find(detail => Number(detail.id) === Number(id))?.pub_name || id).join(',');
        });
      }

    }
    if (isNeedPubOwnerName) {
      const dataOwners = [...new Set(records.map(item => item.pub_owner ? item.pub_owner.split(',') : []).flat())];
      if (dataOwners.length) {
        const findPubNames = await this.app.model.query(`
        select id, pub_name from  affiliate_publisher where id in (${dataOwners.map(it => `'${it}'`).join(',')})
      `, { type: QueryTypes.SELECT });

        records.forEach(item => {
          const owners = item.pub_owner;
          if (!owners) { return; }
          const ownersSplit = owners.split(',');
          item.pub_owner_name = ownersSplit.map(id => findPubNames.find(detail => Number(detail.id) === Number(id))?.pub_name || id).join(',');
        });
      }

    }
  }

  async getTotalResult(sequelizeCondition) {
    // 获取总数
    const { limit, offset, ...countCondition } = sequelizeCondition;
    const totalRes = await this.find(countCondition);
    const total = totalRes.length;

    const totalResult: Record<string, any> = {
      month: '',
      billing_revenue: 0,
      paid_revenue: 0,
      pub_cost: 0,
      commissionable_amount: 0,
      gross_profit: 0,
      gross_profit_margin: 0,
    };

    for (const item of totalRes) {
      totalResult.billing_revenue = NP.plus(totalResult.billing_revenue, item.billing_revenue || 0);
      totalResult.paid_revenue = NP.plus(totalResult.paid_revenue, item.paid_revenue || 0);
      totalResult.pub_cost = NP.plus(totalResult.pub_cost, item.pub_cost || 0);
      totalResult.commissionable_amount = NP.plus(totalResult.commissionable_amount, item.commissionable_amount || 0);
    }
    totalResult.gross_profit = NP.minus(totalResult.paid_revenue, totalResult.pub_cost);
    totalResult.gross_profit_margin = !totalResult.paid_revenue ? 0 : NP.divide(totalResult.gross_profit, totalResult.paid_revenue);

    return { total, totalResult };
  }
}
