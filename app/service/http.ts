import { Service } from 'egg';

export default class Http extends Service {
  // egg curl的http请求
  async http(opt) {
    const { method, path, requestData, headers } = opt;
    try {
      let dataType = 'json';
      if (opt.dataType) {
        dataType = opt.dataType;
      }
      if (this.ctx.locals.env === 'local') {
        console.log(`===== ${method} proxy:`, opt);
      }

      const result = !!headers
        ? await this.ctx.curl(path, {
            method,
            headers,
            dataType,
            data: requestData,
            timeout: 20000
          })
        : await this.ctx.curl(path, {
            method,
            dataType,
            data: requestData,
            timeout: 20000
          });
      // console.log('result:', result);
      if (this.ctx.locals.env === 'local') {
        console.log(`===== ${method} proxy result:`, result);
      }

      if (result.status === 200) {
        return result.data;
      }
      this.ctx.logger.error(`HTTP callback status:${result.status}, fail!`);
      return { status: -1, msg: 'err code:' + result.status };
    } catch (e) {
      this.ctx.logger.error(`HTTP error：${e}`);
      return { status: -1, msg: 'request err' };
    }
  }
}
