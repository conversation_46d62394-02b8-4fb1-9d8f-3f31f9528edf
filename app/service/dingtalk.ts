import { Service } from 'egg';
import { AppConfig } from '../lib/constant';
import * as crypto from 'crypto';
import axios from 'axios';

const LogoUrl = 'http://admin.appnow.store/public/img/logo.e8b9576e.png';
const nailDDMessageQueue: Array<() => Promise<any>> = [];
let taskTimer = null;
interface IBtns {
  title?: string;
  actionURL?: string;
}
// dd消息类型，详情请看文档：https://open.dingtalk.com/document/orgapp/custom-robots-send-group-messages
export interface ICommonMessageProps {
  msgtype: 'text' | 'link' | 'markdown' | 'actionCard' | 'feedCard';
  text?: {
    content?: string;
  };
  link?: {
    text?: string;
    title?: string;
    picUrl?: string;
    messageUrl?: string;
  };
  markdown?: {
    title?: string;
    text?: string;
  };
  actionCard?: {
    hideAvatar?: '0' | '1';
    title?: string;
    text?: string;
    btnOrientation?: '0' | '1';
    singleTitle?: string;
    singleURL?: string;
    btns?: IBtns[];
  };
  feedCard?: Array<{
    title?: string;
    messageURL?: string;
    picURL?: string;
  }>;
  at?: {
    atMobiles?: string[];
    atUserIds?: string[];
    isAtAll?: boolean;
  };
}

export default class DingtalkService extends Service {
  // error catch，send to ding talk robot
  async index(params) {
    const { app, ctx } = this;
    let ddUrl = '';
    if (ctx.locals.env === 'test') {
      ddUrl = app.config.dingtalk.env_test_url;
    } else if (ctx.locals.env === 'prod') {
      ddUrl = app.config.dingtalk.env_prod_url;
    }
    if (!ddUrl) { return {}; }
    const result = await ctx.service.proxy.request(ddUrl, params, 'post', 'json');
    return result;
  }

  async notify(asset) {
    const { app, ctx } = this;
    const {
      locals: { env },
      user: { pub_name },
      service,
      params: { id }
    } = ctx;
    const {
      data: { code, name, screenshot, description },
      typeName
    } = asset;

    // 本地环境不进行钉钉提醒
    if (env === 'local') {
      return;
    }

    const title = `${pub_name}${id ? '更新' : '创建'}${typeName}[${name}]`;
    const picUrl = screenshot || LogoUrl;
    const path = `${env === 'prod' ? '' : 'test.'}appnow.store/${code}`;
    const messageUrl = `http${env === 'prod' ? 's' : ''}://${path}`;
    const text = `项目: ${AppConfig.name}(环境: ${env})\n链接: ${path}\n描述: ${description || '-'}`;

    const params = {
      msgtype: 'link',
      link: {
        title,
        text,
        picUrl,
        messageUrl
      }
    };
    const result = await service.proxy.request(app.config.dingtalk.url, params, 'post', 'json');
    return result;
  }

  // 业务 通用钉钉消息推送
  async commonSend(params) {
    const { ctx } = this;
    const {
      locals: { env },
      service
    } = ctx;
    const { dingtalkUrl, dingtalkParams } = params;
    // 本地环境不进行钉钉提醒
    // if (env === 'local') {
    //   return;
    // }
    ctx.logMethod = '通知';
    // console.log("dingtalkUrl: ", dingtalkUrl);
    const result = await service.proxy.request(dingtalkUrl, dingtalkParams, 'post', 'json');
    return result;
  }

  getSign() {

    const prefix = this.ctx.app.config.DD_WARNING_ROBOT;
    const secret = this.ctx.app.config.DD_WARNING_ROBOT_SECRET;

    const timestamp = Date.now();
    const sign = crypto.createHmac('sha256', secret).update(`${timestamp}\n${secret}`).digest('base64');
    return { timestamp, sign, prefix };
  }
  // 发送任意一种钉钉消息
  async sendCommonDDMessage(options: ICommonMessageProps) {
    const { sign, timestamp, prefix } = this.getSign();
    const ddUrl = `${prefix}&timestamp=${timestamp}&sign=${encodeURIComponent(sign)}`;

    // 封装请求参数
    const { msgtype, at } = options;
    const msgTypeData = options[msgtype];
    const data = { at, msgtype };
    data[msgtype] = msgTypeData;

    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const task = () => {
      return new Promise((resolve, reject) => {
        axios
          .post(ddUrl, data, config)
          .then(response => {
            resolve({
              status: response.status,
              data: response.data
            });
          })
          .catch(error => {
            console.log('error: ', error);
            reject(error.message);
          });
      });
    };
    nailDDMessageQueue.push(task);
    return this.executeTask();
  }

  executeTask() {
    if (taskTimer || !nailDDMessageQueue.length) {
      return;
    }
    taskTimer = setTimeout(() => {
      const task = nailDDMessageQueue.shift();
      if (task) {
        task().finally(() => {
          taskTimer = null;
          this.executeTask();
        });
      } else {
        taskTimer = null;
      }
    }, 3500);
  }

  async sendCrmWarning({ message, code, title }) {
    if (this.ctx.locals.env === 'local') {
      return;
    }
    const contentArr = [
      { label: 'Code', value: code },
      { label: 'Message', value: message },
    ];
    const markdownText = this.ctx.helper.createDDMessageTemplate(contentArr, title);
    this.sendCommonDDMessage({
      msgtype: 'markdown',
      markdown: { title: markdownText.replace(/\*/g, ''), text: markdownText },
    });
  }
}
