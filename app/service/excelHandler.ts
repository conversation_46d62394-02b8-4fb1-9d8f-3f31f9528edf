import { Service } from 'egg';
import * as XLSX from 'xlsx';
const moment = require('moment');

interface ColumnObjValue {
  // 数据字段名
  dataIndex: string;
  // 展示标题
  title: string;
  // 排序下标
  sortedIndex: string;
  // 样式
  style?: {
    width: string
  };
  // 数据映射表
  mapping: Record<string, any>;
  rel_key?: string;
  // excel中展示类型
  type: 'timeZone-UTC';
}

interface ColHeader {
  // 名称
  name: string;
  // 排序索引
  index: number;
  // 列宽
  width: number;
}

export default class ExcelHandler extends Service {

  async export(
    sheetName: string,
    dataSource: Array<Record<string, any>>,
    columnObj: Record<string, ColumnObjValue> = {}
  ) {
    const { app, service, ctx } = this;
    try {
      const {
        formattedData,
        headers
      } = this.dataHandler(dataSource, columnObj);
      const ws = XLSX.utils.json_to_sheet(formattedData, { header: headers.map(i => i.name) });
      this.wsColHandler(ws, headers);
      const range = XLSX.utils.decode_range(ws['!ref']);
      for (let col = range.s.c; col <= range.e.c; col++) {
        const header = `${XLSX.utils.encode_col(col)}1`;
        if (columnObj[ws[header].v] && ws[header].v === columnObj[ws[header].v]?.dataIndex) {
          ws[header].v = columnObj[ws[header].v].title;
        }
      }

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, sheetName);
      const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
      return buf;
    } catch (e) {
      app.coreLogger.error('[ExcelHandler => export]', e);
      return Promise.reject(e);
    }
  }

  /**
   * 数据处理
   * @param dataSource
   * @param columnObj
   */
  dataHandler(
    dataSource: Array<Record<string, any>>,
    columnObj: Record<string, ColumnObjValue>
  ): {
    // 格式化数据
    formattedData: any[],
    // 列定义
    headers: ColHeader[]
  } {
    const formattedData = [];
    const headerObjs = [];
    const headerSet = new Set<string>();

    dataSource.forEach(item => {
      const newItem = { ...item };
      for (const [key, value] of Object.entries(newItem)) {
        const define = columnObj[key];
        if (define) {
          if (!headerSet.has(define.title)) {
            headerObjs.push({
              name: define.dataIndex,
              index: Number(define.sortedIndex || 0),
              width: Number(define.style.width || 110)
            });
            headerSet.add(define.title);
          }
          if (define.type) {
            switch (define.type) {
              case 'timeZone-UTC':
                newItem[key] = value ? moment.tz(value).format('[UTC]Z') : value;
                break;
            }
          }
          if (define.mapping) {
            newItem[key] = define.mapping[value] || value;
          }
        } else {
          Reflect.deleteProperty(newItem, key);
        }
      }
      formattedData.push(newItem);
    });

    const headers = headerObjs.sort((a, b) => a.index - b.index);

    return {
      formattedData,
      headers,
    };
  }

  /**
   * @description 列处理
   * @param ws
   * @param headers
   */
  wsColHandler(ws: XLSX.WorkSheet, headers: ColHeader[]) {
    // 设置列宽
    ws['!cols'] = headers.map(i => ({
      wpx: i.width || 100
    }));
  }
}
