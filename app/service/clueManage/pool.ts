import { Service } from 'egg';
import moment from 'moment';
import sequelize from 'sequelize';
import { Op } from 'sequelize';
import { userIsBD, userIsOP } from '../../lib/constant';

export default class CrmClueService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.ClueManage.Pool.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.ClueManage.Pool.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.ClueManage.Pool.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.ClueManage.Pool.create(data, { individualHooks: true, egg: this });

    // 录入线索后，通知BD leader
    this.service.systemNotify.notify.sendClueNotify({
      relation_id: result.id
    });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ClueManage.Pool.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ClueManage.Pool.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async clueIsExist(body: Record<string, any>, id?: number) {
    // 校验是否重复 公司 +  联系人+ 联系方式
    const existWehre: any = {
      company_name: body.company_name,
      connect_name: body.connect_name
    };
    if (id) {
      existWehre.id = {
        [Op.ne]: id
      };
    }
    if (body.contact_type) {
      existWehre[Op.and] = sequelize.fn('JSON_CONTAINS', sequelize.col('contact_type'), JSON.stringify(body.contact_type));
    }
    const isExist = await this.findOne({
      where: existWehre
    });

    return isExist;
  }

  async createClue(body: Record<string, any>, isJumpValidate = false) {
    if (!isJumpValidate) {
      const isExist = await this.clueIsExist(body);
      if (isExist) {
        return {
          isSuccess: false,
          msg: 'the clue already exists'
        };
      }
    }

    if (body.clue_status === 'assigned') {
      const now = moment().format('YYYY-MM-DD HH:mm:ss');
      body.assignment_time = now;
    }
    if (!body.creator) {
      body.creator = this.ctx.user?.pub_name || '';
    }
    body.modifier = this.ctx.user?.pub_name || '';
    if (!body.creator_id) {
      body.creator_id = this.ctx.user?.id || null;
    }
    delete body.id;
    const result = await this.create(body);
    return {
      isSuccess: true,
      result
    };
  }

  async getClueCompany() {
    const { service } = this;
    const result = await service.clueManage.pool.find({
      attributes: ['company_name', 'id', 'client_id'],
      order: [['id', 'desc']],
      include: [
        {
          model: this.ctx.model.CustomerManage.Pool,
          attributes: ['id', 'client_name', 'client_status', 'coop_bus']
        }
      ]
    });
    return result;
  }
}
