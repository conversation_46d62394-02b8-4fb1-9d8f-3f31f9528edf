import { Service } from 'egg';
import { AppConfig } from '../lib/constant';

export default class OnErrorService extends Service {
  // error catch，send to ding talk robot
  async index(err) {
    const { app, ctx } = this;
    const { locals, request, service } = ctx;
    const { env } = locals;
    const ip = ctx.get('X-Real-IP') || ctx.ip;
    const ua = ctx.get('user-agent') || '-';
    if (env && env !== 'local' && ip !== '127.0.0.1') {
      const params = {
        msgtype: 'text',
        text: {
          content: `• 项目: ${AppConfig.name}\n• 类型: 服务报错\n• 环境: ${env}\n• 路由: ${request.url}\n• IP: ${ip}\n• UA: ${ua}\n• 错误: ${err}`
        }
      };
      service.dingtalk.index(params);
    }
  }
}
