
import { Service } from 'egg';

export default class CrmContractChatRecordService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Chat.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Chat.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.ContractManage.Chat.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Chat.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ContractManage.Chat.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ContractManage.Chat.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
}
