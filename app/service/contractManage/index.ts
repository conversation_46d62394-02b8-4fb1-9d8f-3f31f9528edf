
import { Service } from 'egg';
import { CONTRACT_CREATE_TYPE, CONTRACT_DATA_TYPE, CONTRACT_TYPE_ENUM, OPTION_SEPARATOR, userIsAssetGroup, userIsBD, userIsBDAssistant, userIsBDLeader, userIsFinance, userIsLegal, userIsTax } from '../../lib/constant';
import { Op, QueryTypes } from 'sequelize';
import { IInitiateApprove } from '../ddCloud';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../../lib/systemEvent';
import moment from 'moment';
import sequelize from 'sequelize';

export default class CrmContractService extends Service {

  async getParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const {
      id, contract_code, contract_type, apply_id,
      our_company_name,
      our_contact_person, partner_company_name,
      partner_contact_person, contract_desc, status,
      customers,
      dataType, // pending or all or processed
      PAGE_TYPE, // owned or pool or all
      sorter,

      template,
      pay_type,
      payment_terms
    } = query;
    const where: any = {};
    const order = [
      [sequelize.literal(`case status when 'draft' then 1 else 2 end`), 'ASC'],
      ['submission_time', 'DESC']
    ];
    const opAnd = [];
    const opOr = [];
    if (sorter) {
      const [field, ord] = sorter.split(',');
      order[1][0] = field;
      order[1][1] = ord;
    }
    // 主表筛选条件
    const user = this.ctx.user;

    const params = {
      where,
      order,
      distinct: true,
      include: [
        {
          model: this.ctx.model.CustomerManage.Pool,
          // attributes: ['id', 'client_name'], // 后续可以做性能优化，现在是打开customer的弹窗需要，可以动态获取
          through: {
            attributes: [] // 空数组表示不包含中间表 CrmClientContract 的其他字段
          },
          // 后续可以做性能优化，现在是打开customer的弹窗需要，可以动态获取
          include: [
            {
              model: this.ctx.model.ClueManage.Pool,
              attributes: ['id', 'bd_id', 'assist_id'],
              required: false,
              where: {
                clue_identity: 1
              }
            }
          ]
        }
      ]
    } as any;

    if (PAGE_TYPE !== 'all' && PAGE_TYPE !== 'shared') {
      if (userIsBDLeader(user)) {
        // 他下面的成员的合同单
        const bdMembers = user.bd_member ? user.bd_member.split(',') : [];
        bdMembers.push(user.id);
        opAnd.push({
          apply_id: {
            [Op.in]: bdMembers
          }
        });
      } else if (userIsBD(user)) {
        opAnd.push({
          apply_id: user.id
        });
      } else if (userIsBDAssistant(user)) {
        // 先查出有哪些客户
        const clients = await this.service.customerManage.pool.find({
          attributes: ['id'],
          include: [
            {
              model: this.ctx.model.ClueManage.Pool,
              attributes: ['id', 'bd_id', 'assist_id'],
              where: {
                assist_id: user.id,
                clue_identity: 1
              }
            }
          ],
          raw: true
        });
        const contractRes = await this.app.model.query(`
          SELECT contract_id
            FROM crm_client_contract
          WHERE client_id in (${clients.map(item => item.id).join(',') || '-1'})
        `, {
          type: QueryTypes.SELECT
        });
        const contractIds = [...new Set(contractRes.map(item => item.contract_id))];
        if (contractIds.length === 0) {
          contractIds.push(-1);
        }
        opAnd.push({
          id: {
            [Op.in]: contractIds
          }
        });

      }
    }

    if (PAGE_TYPE === 'owned') {
      opAnd.push({
        apply_id: user.id
      });
    } else if (PAGE_TYPE === 'shared') {
      opAnd.push(
        sequelize.literal(` find_in_set(${user.id}, shared_user_ids) `)
      );
    }
    if (dataType === CONTRACT_DATA_TYPE.PENDING) {
      const { opAnd: and, opOr: or } = this.getParamsPending({ user });
      opAnd.push(...and);
      opOr.push(...or);
    } else if (dataType === CONTRACT_DATA_TYPE.PROCESSED) {
      const { opAnd: and } = this.getParamsProcess({ user });
      opAnd.push(...and);
      // opOr.push(...or);
    }
    if (id) {
      const ids = id.split(',');
      where.id = {
        [Op.in]: ids
      };
    }
    if (contract_code) {
      where.contract_code = {
        [Op.like]: `%${contract_code}%`
      };
    }
    if (contract_type) {
      where.contract_type = {
        [Op.in]: contract_type.split(OPTION_SEPARATOR)
      };
    }
    if (apply_id) {
      where.apply_id = {
        [Op.in]: apply_id.split(OPTION_SEPARATOR)
      };
    }

    if (our_company_name) {
      where.our_company_name = {
        [Op.substring]: `%${our_company_name}%`
      };
    }

    if (our_contact_person) {
      where.our_contact_person = {
        [Op.substring]: `%${our_contact_person}%`
      };
    }

    if (partner_company_name) {
      where.partner_company_name = {
        [Op.substring]: `%${partner_company_name}%`
      };
    }

    if (partner_contact_person) {
      where.partner_contact_person = {
        [Op.substring]: `%${partner_contact_person}%`
      };
    }

    if (contract_desc) {
      where.contract_desc = {
        [Op.substring]: `%${contract_desc}%`
      };
    }

    if (status) {
      where.status = {
        [Op.in]: status.split(OPTION_SEPARATOR)
      };
    }
    if (customers) {
      const contractRes = await this.app.model.query(`
        SELECT contract_id
          FROM crm_client_contract
        WHERE client_id in (${customers.split(OPTION_SEPARATOR)})
      `, {
        type: QueryTypes.SELECT
      });
      const contractIds = [...new Set(contractRes.map(item => item.contract_id))];
      if (contractIds.length === 0) {
        contractIds.push(-1);
      }
      opAnd.push({
        id: {
          [Op.in]: contractIds
        }
      });
    }
    if (template) {
      const templates = template.split(OPTION_SEPARATOR);
      const opOr = templates.map(item => {
        // find_in_set
        if (item.includes(',')) {
          return {
            [Op.and]: item.split(',').map(id => sequelize.literal(`find_in_set('${id}', crm_contract.template)`))
          };
        }
        return {
          template: sequelize.literal(`find_in_set('${item}', crm_contract.template)`)
        };
      });
      opAnd.push({
        [Op.or]: opOr
      });
    }
    if (pay_type) {
      where.pay_type = pay_type;
    }
    if (payment_terms) {
      where.payment_terms = {
        [Op.substring]: payment_terms
      };
    }
    if (opAnd.length) {
      // where[Op.and] = opAnd;
      opOr.push({
        [Op.and]: opAnd
      });
    }

    if (opOr.length) {
      where[Op.or] = opOr;
    }

    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }

    return params;
  }

  getParamsPending({ user }) {
    // 资产组，财务，税务，法务
    const isAuditUser = userIsAssetGroup(user) || userIsFinance(user) || userIsTax(user) || userIsLegal(user);
    const userAuditType = user?.crm_audit_type ? user.crm_audit_type.split(',') : [];
    const createTransmittorLiteralOr = (key: CONTRACT_TYPE_ENUM) => {
      return sequelize.literal(`
        (JSON_EXTRACT(transmittor_info, '$.${key}.transmittor') = ${user.id} AND JSON_EXTRACT(transmittor_info, '$.${key}.status') = false)
      `);
    };
    const createTransmittorLiteralAnd = (key: CONTRACT_TYPE_ENUM) => {
      return sequelize.literal(`
        (JSON_EXTRACT(transmittor_info, '$.${key}.origin') != ${user.id} OR JSON_EXTRACT(transmittor_info, '$.${key}.origin') IS NULL)
      `);
    };
    const opOr: any[] = [
      createTransmittorLiteralOr(CONTRACT_TYPE_ENUM.ASSET_APPROVAL),
      createTransmittorLiteralOr(CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL),
      createTransmittorLiteralOr(CONTRACT_TYPE_ENUM.TAX_APPROVAL),
      createTransmittorLiteralOr(CONTRACT_TYPE_ENUM.LEGAL_APPROVAL),
    ];
    const opAnd: any[] = [
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.ASSET_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.TAX_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.LEGAL_APPROVAL),
    ];

    if (isAuditUser && userAuditType) {
      const status = [];
      if (userIsAssetGroup(user)) {
        status.push(CONTRACT_TYPE_ENUM.ASSET_APPROVAL);
      }
      if (userIsFinance(user)) {
        status.push(CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL);
      }
      if (userIsTax(user)) {
        status.push(CONTRACT_TYPE_ENUM.TAX_APPROVAL);
      }
      if (userIsLegal(user)) {
        status.push(CONTRACT_TYPE_ENUM.LEGAL_APPROVAL);
      }
      opAnd.push(
        {
          contract_type: {
            [Op.in]: userAuditType
          }
        },
        {
          status: {
            [Op.in]: status
          }
        }
      );

    } else {
      // 不需要数据，直接置空
      opAnd.push({
        id: -1
      });
    }

    return {
      opAnd,
      opOr
    };
  }

  getParamsProcess({ user }) {
    const opAnd = [];

    const createTransmittorLiteralAnd = (key: CONTRACT_TYPE_ENUM) => {
      return sequelize.literal(`
        (JSON_EXTRACT(transmittor_info, '$.${key}.origin') = ${user.id} OR (JSON_EXTRACT(transmittor_info, '$.${key}.transmittor') = ${user.id} and JSON_EXTRACT(transmittor_info, '$.${key}.status') = true))
      `);
    };
    const opOr = [
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.ASSET_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.TAX_APPROVAL),
      createTransmittorLiteralAnd(CONTRACT_TYPE_ENUM.LEGAL_APPROVAL),
    ];

    // 查询该角色审批过的合同
    const needKeys = [];
    if (userIsAssetGroup(user)) {
      needKeys.push('asset_submit');
    }
    if (userIsFinance(user)) {
      needKeys.push('financial_submit');
    }
    if (userIsTax(user)) {
      needKeys.push('tax_submit');
    }
    if (userIsLegal(user)) {
      needKeys.push('legal_submit');
    }
    if (needKeys.length) {
      needKeys.forEach(key => {
        opOr.push(
          sequelize.literal(`JSON_CONTAINS_PATH(approval_memo, 'one', '$.${key}')`)
        );
      });
    } else {
      // 直接置空
      opAnd.push({
        id: -1
      });
    }
    if (opOr.length) {
      opAnd.push({
        [Op.or]: opOr
      });
    }
    return {
      opAnd,
      opOr
    };
  }

  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Index.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Index.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.ContractManage.Index.count(query);
    return count;
  }

  async isExist({
    contract_code,
    id
  }: {
    contract_code: string;
    id?: number;
  }) {
    const where: Record<string, any> = {
      contract_code
    };
    if (id) {
      where.id = {
        [Op.ne]: id
      };
    }
    const isExist = await this.findOne({
      where
    });
    return !!isExist;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.ContractManage.Index.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ContractManage.Index.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ContractManage.Index.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }

  async syncClientInfo(values) {
    if (values.create_type === CONTRACT_CREATE_TYPE.TO_AUDIT && values.is_auto_sync_client && values.customers?.length) {
      // 去审核且勾选了需要同步，同步更新客户信息
      const { remark, creator, id, ...otherData } = values;
      otherData.modifier = otherData.modifier + `_contract-sync`;
      this.service.customerManage.pool.update({
        id: {
          [Op.in]: values.customers
        }
      }, otherData);
    }
  }

  async submitFileToDingtalk(attachmentInfo) {
    const user = this.ctx.user;
    if (!user.d_union_id) {
      return {
        isSuccess: false,
        msg: 'The user is not bound to a dingtalk ID. Please contact the administrator'
      };
    }
    const { isSuccess, msg, result } = await this.service.ddCloud.submitFile({
      fileName: attachmentInfo.name,
      fileSize: attachmentInfo.size,
      fileUrl: attachmentInfo.url
    });

    if (!isSuccess) {
      return {
        isSuccess,
        msg
      };
    }

    if (!result.id) {
      return {
        isSuccess: false,
        msg: 'File ID not found'
      };
    }

    return {
      isSuccess: true,
      dd_file_id: result.id,
      dd_file_type: result.extension
    };
  }

  async createTypeToAddComment(dataInfo: Record<string, any>, submitData: Record<string, any>) {
    const createType = submitData.create_type;

    if (![
      CONTRACT_CREATE_TYPE.TO_AUDIT,
      CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT,

      CONTRACT_CREATE_TYPE.ASSET_SUBMIT,
      CONTRACT_CREATE_TYPE.ASSET_REJECT,

      CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT,
      CONTRACT_CREATE_TYPE.FINANCIAL_REJECT,

      CONTRACT_CREATE_TYPE.TAX_SUBMIT,
      CONTRACT_CREATE_TYPE.TAX_REJECT,

      CONTRACT_CREATE_TYPE.LEGAL_SUBMIT,
      CONTRACT_CREATE_TYPE.LEGAL_REJECT
    ].includes(createType)) { return; }

    const user = this.ctx.user;
    const isAuditToAsset = (createType === CONTRACT_CREATE_TYPE.TO_AUDIT || createType === CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT) && submitData.status === CONTRACT_TYPE_ENUM.ASSET_APPROVAL;
    const strategy = {
      [CONTRACT_CREATE_TYPE.TO_AUDIT]: {
        type: isAuditToAsset ? 'apply_submit_to_asset' : 'apply_submit_to_financial',
        tag: 'Submitted',
      },
      [CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT]: {
        type: isAuditToAsset ? 'apply_submit_to_asset' : 'apply_submit_to_financial',
        tag: 'Resubmit',
      },
      [CONTRACT_CREATE_TYPE.ASSET_SUBMIT]: {
        type: 'asset_submit',
        tag: 'Approved',
      },
      [CONTRACT_CREATE_TYPE.ASSET_REJECT]: {
        type: 'asset_reject',
        tag: 'Rejected',
      },

      [CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT]: {
        type: 'finance_submit',
        tag: 'Approved',
      },
      [CONTRACT_CREATE_TYPE.FINANCIAL_REJECT]: {
        type: 'financial_reject',
        tag: 'Rejected',
      },

      [CONTRACT_CREATE_TYPE.TAX_SUBMIT]: {
        type: 'tax_submit',
        tag: 'Approved',
      },
      [CONTRACT_CREATE_TYPE.TAX_REJECT]: {
        type: 'tax_reject',
        tag: 'Rejected',
      },

      [CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]: {
        type: 'legal_submit',
        tag: 'Approved',
      },
      [CONTRACT_CREATE_TYPE.LEGAL_REJECT]: {
        type: 'legal_reject',
        tag: 'Rejected',
      },

    };
    const { type, tag } = strategy[createType];
    if (!type || !tag) { return; }

    const commentParams: Record<string, any> = {
      contract_id: dataInfo.id,
      tag,
      message: submitData.remark || submitData.comments,
      type,
      attachment_new: submitData.attachment,
      creator: user.pub_name,
    };

    if (submitData.contract_attachment === 'upload') {
      const prevAttachmentRes = await this.service.contractManage.chat.findOne({
        where: {
          contract_id: dataInfo.id
        },
        attributes: ['attachment', 'attachment_new'],
        raw: true
      });
      if (prevAttachmentRes) {
        let isUpdateAttachment = 0;
        const maxLen = Math.max(prevAttachmentRes.attachment_new?.length, submitData.attachment?.length) || 0;
        for (let i = 0; i < maxLen; i++) {
          const oldAttachment = prevAttachmentRes.attachment_new?.[i]?.url;
          const newAttachment = submitData.attachment[i]?.url;
          if (oldAttachment !== newAttachment) {
            isUpdateAttachment = 1;
            break;
          }
        }
        // 比较附件是否变更

        commentParams.is_update_attachment = isUpdateAttachment;
      }

    }
    await this.service.contractManage.chat.create(commentParams);

    this.noticePersonForAudit({
      create_type: submitData.create_type,
      is_audit_to_asset: isAuditToAsset,
      contract_id: dataInfo.id,
      apply_id: dataInfo.apply_id,
      contract_type: dataInfo.contract_type
    });

  }

  async noticeToSystemAndDingtalk({
    notifyUserId,
    contractId,
    title,
    headerStrong = false,
    applyId,
    isAudit = false
  }) {
    const creator = this.ctx?.user?.pub_name || 'system';
    const otherInfo: Record<string, any> = {
      title,
    };
    // headerStrong 是显示申请人的名字
    const pubRes = await this.service.authority.user.findOne({
      where: {
        id: applyId
      }
    });
    const applyUserName = pubRes?.pub_name;
    otherInfo.applyUserName = applyUserName;

    if (headerStrong) {
      otherInfo.headerStrong = headerStrong;
    }
    if (isAudit) {
      otherInfo.isAudit = isAudit;
    }
    await this.ctx.service.systemNotify.notify.create({
      relation_type: 'contract',
      relation_id: contractId,
      receiver_id: notifyUserId,
      notify_type: 'node7',
      other_info: otherInfo,
      creator
    });

    // 发送系统通知
    this.app.redis.get('pub').publish(this.app.config.sun.redisChannel, JSON.stringify({
      event: APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT,
      data: {
        targetUser: {
          id: notifyUserId
        },
        contractId,
        creator,
        title
      },
    }));
    const userInfo = await this.service.authority.user.findOne({
      where: {
        id: notifyUserId
      },
      attributes: ['pub_name', 'd_union_id'],
      raw: true
    });
    if (!userInfo.d_union_id) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `发送待办失败`,
        title: `${userInfo.pub_name} 未绑定钉钉ID`
      });
      return;
    }
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/contract-manage/${isAudit ? 'approval' : 'owned'}?id=${contractId}`;

    // 发送待办
    await this.service.ddCloud.sendNotificationToDo({
      executor_id: userInfo.d_union_id,
      title: headerStrong ? `"${applyUserName}" ${title}` : title,
      jump_url: jumpUrlOrigin
    });
  }

  async noticePersonForAudit({
    create_type,
    is_audit_to_asset,
    contract_id,
    apply_id,
    contract_type,
  }) {
    const result = await this.service.authority.user.getByRoleUsers();

    let auditIds = result.finances;

    if (is_audit_to_asset) {
      auditIds = result.assetGroups;
    }

    const strategy = {
      // 如果需要资产组审核，通知资产组，否则，通知财务组
      [CONTRACT_CREATE_TYPE.TO_AUDIT]: {
        ids: auditIds.filter(it => it.aud_type.includes(contract_type)).map(it => it.value),
        title: `has submitted an application for contract approval（id：${contract_id}）. Please complete the contract approval in time.`,
        headerStrong: true,
        isAudit: true,
      },
      // 跟TO_AUDIT逻辑一样
      [CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT]: {
        ids: auditIds.filter(it => it.aud_type.includes(contract_type)).map(it => it.value),
        title: `has submitted an application for contract approval（id：${contract_id}）. Please complete the contract approval in time.`,
        headerStrong: true,
        isAudit: true,
      },

      // 资产组审核通知财务
      [CONTRACT_CREATE_TYPE.ASSET_SUBMIT]: {
        ids: result.finances.filter(it => it.aud_type.includes(contract_type)).map(it => it.value),
        title: `has submitted an application for contract approval（id：${contract_id}）. Please complete the contract approval in time.`,
        headerStrong: true,
        isAudit: true,
      },

      // 财务组拒绝通知合同创建人
      [CONTRACT_CREATE_TYPE.ASSET_REJECT]: {
        ids: [apply_id],
        title: `Your contract approval form (id: ${contract_id}) has been rejected`
      },

      // 财务提交通知税务
      [CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT]: {
        ids: result.taxs.filter(it => it.aud_type.includes(contract_type)).map(it => it.value),
        title: `has submitted an application for contract approval（id: ${contract_id}）. Please complete the contract approval in time.`,
        headerStrong: true,
        isAudit: true,
      },

      // 法务拒绝通知合同创建人
      [CONTRACT_CREATE_TYPE.FINANCIAL_REJECT]: {
        ids: [apply_id],
        title: `Your contract approval form (id: ${contract_id}) has been rejected`
      },

      // 税务提交通知法务
      [CONTRACT_CREATE_TYPE.TAX_SUBMIT]: {
        ids: result.legals.filter(it => it.aud_type.includes(contract_type)).map(it => it.value),
        title: `has submitted an application for contract approval（id: ${contract_id}）. Please complete the contract approval in time.`,
        headerStrong: true,
        isAudit: true,
      },

      // 法务拒绝通知合同创建人
      [CONTRACT_CREATE_TYPE.TAX_REJECT]: {
        ids: [apply_id],
        title: `Your contract approval form (id: ${contract_id}) has been rejected`
      },

      // 法务提交通知合同创建人
      [CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]: {
        ids: [apply_id],
        title: `Your contract approval form has been approved in CRM. Please initiate DingTalk OA approval in time `
      },
      // 法务拒绝通知合同创建人
      [CONTRACT_CREATE_TYPE.LEGAL_REJECT]: {
        ids: [apply_id],
        title: `Your contract approval form (id: ${contract_id}) has been rejected`
      },
    };

    const { ids, title, headerStrong, isAudit } = strategy[create_type];

    if (!ids.length) { return; }

    for (const id of ids) {
      this.noticeToSystemAndDingtalk({
        notifyUserId: id,
        contractId: contract_id,
        title,
        headerStrong,
        applyId: apply_id,
        isAudit
      });
    }

  }

  async createTypeToApprovalMemo(body, { type, id }: { type: 'create' | 'update', id?: number } = { type: 'create' }) {
    let approvalMemo: Record<string, any> = {};
    if (type === 'update') {
      const { approval_memo } = await this.findOne({ where: { id } });
      approvalMemo = approval_memo || {};
    }
    // 如何这里增加条件，记得在AUDIT_SUBMIT_TYPE中增加
    if (body.create_type === CONTRACT_CREATE_TYPE.ASSET_SUBMIT) {
      // 资产组提交
      approvalMemo.asset_submit = {
        uid: this.ctx.user.id,
        time: moment().format('YYYY-MM-DD HH:mm:ss')
      };
    } else if (body.create_type === CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT) {
      // 财务组提交
      approvalMemo.financial_submit = {
        uid: this.ctx.user.id,
        time: moment().format('YYYY-MM-DD HH:mm:ss')
      };
    } else if (body.create_type === CONTRACT_CREATE_TYPE.TAX_SUBMIT) {
      // 税务组提交
      approvalMemo.tax_submit = {
        uid: this.ctx.user.id,
        time: moment().format('YYYY-MM-DD HH:mm:ss')
      };
    } else if (body.create_type === CONTRACT_CREATE_TYPE.LEGAL_SUBMIT) {
      // 法务组提交
      approvalMemo.legal_submit = {
        uid: this.ctx.user.id,
        time: moment().format('YYYY-MM-DD HH:mm:ss')
      };
    }
    return approvalMemo;
  }

  async handlerContractTransfer(body, { id }) {
    const { transmittor_info } = await this.findOne({ where: { id } });
    const transmittorInfo = transmittor_info || {};
    // 如何这里增加条件，记得在AUDIT_SUBMIT_TYPE中增加
    if ((
      body.create_type === CONTRACT_CREATE_TYPE.ASSET_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.ASSET_REJECT
    ) && transmittorInfo[CONTRACT_TYPE_ENUM.ASSET_APPROVAL]) {
      // 资产组提交
      transmittorInfo[CONTRACT_TYPE_ENUM.ASSET_APPROVAL].status = true;
    } else if ((
      body.create_type === CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.FINANCIAL_REJECT
    ) && transmittorInfo[CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL]) {
      // 财务组提交
      transmittorInfo[CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL].status = true;
    } else if ((
      body.create_type === CONTRACT_CREATE_TYPE.TAX_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.TAX_REJECT
    ) && transmittorInfo[CONTRACT_TYPE_ENUM.TAX_APPROVAL]) {
      // 税务组提交
      transmittorInfo[CONTRACT_TYPE_ENUM.TAX_APPROVAL].status = true;
    } else if ((
      body.create_type === CONTRACT_CREATE_TYPE.LEGAL_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.LEGAL_REJECT
    ) && transmittorInfo[CONTRACT_TYPE_ENUM.LEGAL_APPROVAL]) {
      // 法务组提交
      transmittorInfo[CONTRACT_TYPE_ENUM.LEGAL_APPROVAL].status = true;
    }
    return transmittorInfo;
  }

  async attachmentModifyCheck(body, { id }) {
    const { attachment, apply_id } = await this.findOne({ where: { id }, raw: true });
    const maxLen = Math.max(attachment.length, body.attachment.length);
    let isUpdateAttachment = 0;
    for (let i = 0; i < maxLen; i++) {
      const oriFileUrl = attachment[i]?.url;
      const submitFileUrl = body.attachment[i]?.url;
      if (oriFileUrl !== submitFileUrl) {
        isUpdateAttachment = 1;
      }
    }
    if (!isUpdateAttachment) { return; }

    if (
      body.create_type === CONTRACT_CREATE_TYPE.ASSET_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.TAX_SUBMIT ||
      body.create_type === CONTRACT_CREATE_TYPE.LEGAL_SUBMIT
    ) {
      const title = `Contract (id: ${id}) has new documents updated`;
      // 提交通知
      await this.ctx.service.systemNotify.notify.create({
        relation_type: 'contract',
        relation_id: id,
        receiver_id: apply_id, // 通知申请人
        notify_type: 'node9',
        other_info: {
          title
        },
        creator: this.ctx.user.pub_name,
      });

      // 发送系统通知
      this.app.redis.get('pub').publish(this.app.config.sun.redisChannel, JSON.stringify({
        event: APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT,
        data: {
          targetUser: {
            id: apply_id
          }
        },
      }));

      const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
      const jumpUrlOrigin = `${ddUseProjectUrl}/operation/contract-manage/owned?id=${id}`;

      const applyDetail = await this.service.authority.user.findOne({
        where: {
          id: apply_id
        },
        attributes: ['pub_name', 'd_union_id'],
        raw: true
      });
      if (!applyDetail.d_union_id) {
        this.service.dingtalk.sendCrmWarning({
          code: 500,
          message: `发送待办失败`,
          title: `${applyDetail.pub_name} 未绑定钉钉ID`
        });
        return;
      }

      // 发送待办
      await this.service.ddCloud.sendNotificationToDo({
        executor_id: applyDetail.d_union_id,
        title,
        jump_url: jumpUrlOrigin
      });
    }
  }

}
