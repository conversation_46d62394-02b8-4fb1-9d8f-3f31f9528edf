import { Service } from 'egg';
import DDClient from '../utils/aliCloud';
import { Op } from 'sequelize';
import dingtalktodo_1_0, * as $dingtalktodo_1_0 from '@alicloud/dingtalk/todo_1_0';
import * as $OpenApi from '@alicloud/openapi-client';
import Util, * as $Util from '@alicloud/tea-util';
import moment from 'moment';
import { ADMIN_UNION_ID, TODO_CREATOR_ID } from '../lib/globalVar';
import { AUDIT_SUBMIT_TYPE, CONTRACT_CREATE_TYPE, CONTRACT_TYPE_ENUM } from '../lib/constant';

export interface IInitiateApprove {
  token: string;
  userId: string; // 钉钉的用户id
  fileList: {
    fileId: string,
    fileName: string,
    fileType: string,
    spaceId: string,
    fileSize: string
  };
  contractInfo: Record<string, any>;
}

export default class DDCloudService extends Service {
  TODO_CLIENT: any;
  async getAccessToken() {
    const token = await this.service.redis.get('dd_access_token');
    if (token) {
      return token;
    }

    const { accessToken, expireIn, message }: any = await DDClient.getAccessToken();
    if (message) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message,
        title: '获取钉钉AccessToken失败'
      });
      return '';
    }
    await this.service.redis.set('dd_access_token', accessToken, expireIn - 20); // 减少20秒容错时间
    return accessToken;
  }
  async settingDDUserIds() {
    const token = await this.getAccessToken();
    const userList = await this.service.authority.user.find({
      where: {
        role_type: 1,
        [Op.or]: [
          {
            d_union_id: '',
          },
          {
            d_user_id: '',
          },
          {
            d_dept_id: null
          }

        ],
        mobile: {
          [Op.ne]: null
        }
      },
      raw: true
    });
    const errorList = [];
    for (const item of userList) {
      const res = await DDClient.getUserDDIds({
        token,
        mobile: item.mobile
      });
      if (!res.isSuccess) {
        errorList.push({
          id: item.id,
          msg: res.msg
        });
        continue;
      }
      await this.service.authority.user.update({
        id: item.id
      }, {
        d_user_id: res.userId,
        d_union_id: res.unionId,
        d_dept_id: res.deptId
      });

      const res2 = await DDClient.setUserSpaceRole({
        unionId: res.unionId,
        token
      });
      if (!res2.isSuccess) {
        this.service.dingtalk.sendCrmWarning({
          code: 500,
          message: res2.msg,
          title: '设置钉钉文件权限失败'
        });
      }
    }
    return {
      errorList
    };
  }

  async settingCorpSpaceRole() {
    const token = await this.getAccessToken();
    const res2 = await DDClient.settingCorpSpaceRole({
      unionId: ADMIN_UNION_ID,
      token
    });
    return res2;
  }

  async settingUserPermission() {
    const token = await this.getAccessToken();
    const userList = await this.service.authority.user.find({
      where: {
        role_type: 1,
        d_union_id: {
          [Op.ne]: ''
        },
        mobile: {
          [Op.ne]: null
        }
      },
      raw: true
    });
    const results = [];
    for (const item of userList) {
      const res = await DDClient.setUserSpaceRole({
        unionId: item.d_union_id,
        token
      });
      results.push({
        ...res,
        user_data_id: item.id
      });
      if (!res.isSuccess) {
        this.service.dingtalk.sendCrmWarning({
          code: 500,
          message: res.msg,
          title: '设置钉钉文件权限失败'
        });
      }
    }

    return results;
  }

  async setSpaceWithDownloadAuth({
    dingUserId,
    processInstanceId,
    fileId
  }) {
    const token = await this.getAccessToken();
    const res = await DDClient.setSpaceWithDownloadAuth({
      dingUserId,
      token,
      processInstanceId,
      fileId
    });
    if (!res.isSuccess) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: res.msg,
        title: '授权预览审批附件失败'
      });
    }

    return res;
  }

  async sendNotificationToDo({
    executor_id,
    title,
    jump_url
  }) {
    const token = await this.getAccessToken();
    const client = this.createTodoClient();
    const createTodoTaskHeaders = new $dingtalktodo_1_0.CreateTodoTaskHeaders({});
    createTodoTaskHeaders.xAcsDingtalkAccessToken = token;

    const notifyConfigs = new $dingtalktodo_1_0.CreateTodoTaskRequestNotifyConfigs({
      dingNotify: '1',
    });

    const generateTodo = async ({
      resolve,
    }) => {
      const now = Date.now().toString();
      jump_url = jump_url.includes('?') ? `${jump_url}&dingtalk_ts=${now}` : `${jump_url}?dingtalk_ts=${now}`;
      jump_url = encodeURIComponent(jump_url);
      const detailUrl = new $dingtalktodo_1_0.CreateTodoTaskRequestDetailUrl({
        appUrl: `dingtalk://dingtalkclient/page/link?url=${jump_url}&pc_slide=false`,
        pcUrl: `dingtalk://dingtalkclient/page/link?url=${jump_url}&pc_slide=false`,
      });

      const createTodoTaskRequest = new $dingtalktodo_1_0.CreateTodoTaskRequest({
        subject: title,
        executorIds: [
          executor_id
        ],
        detailUrl,
        isOnlyShowExecutor: true,
        priority: 10,
        notifyConfigs,
      });
      try {
        const res = await client.createTodoTaskWithOptions(TODO_CREATOR_ID, createTodoTaskRequest, createTodoTaskHeaders, new $Util.RuntimeOptions({}));
        // 插入到待办记录列表
        this.service.systemNotify.dingtalkTodo.create({
          task_time: now,
          task_id: res.body.id,
          executor_id
        });
        resolve(res);
        return res;
      } catch (err: any) {
        if (!Util.empty(err.code) && !Util.empty(err.message)) {
          // err 中含有 code 和 message 属性，可帮助开发定位问题
          this.service.dingtalk.sendCrmWarning({
            message: err.message,
            code: err.code,
            title: '发送钉钉待办失败'
          });
        }
        return Promise.resolve(err);
      }
    };

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 确保时间戳不冲突
        resolve(generateTodo({ resolve }));
      }, 10);
    });

  }

  async completeTodo({
    task_id,
    unionid
  }) {
    const token = await this.getAccessToken();
    const client = this.createTodoClient();
    const updateTodoTaskHeaders = new $dingtalktodo_1_0.UpdateTodoTaskHeaders({});
    updateTodoTaskHeaders.xAcsDingtalkAccessToken = token;
    const updateTodoTaskRequest = new $dingtalktodo_1_0.UpdateTodoTaskRequest({
      done: true,
    });
    try {
      const res = await client.updateTodoTaskWithOptions(unionid, task_id, updateTodoTaskRequest, updateTodoTaskHeaders, new $Util.RuntimeOptions({}));
      return res;
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题
        this.service.dingtalk.sendCrmWarning({
          message: err.message,
          code: err.code,
          title: '自动完成钉钉待办失败'
        });
      }
      return false;
    }
  }

  async submitFile({
    fileName,
    fileSize,
    fileUrl
  }) {
    const token = await this.getAccessToken();
    const unionId = this.ctx.user.d_union_id;

    const res = await DDClient.submitFile({
      token,
      unionId,
      fileName,
      fileSize,
      fileUrl
    });

    return res;
  }
  async initiateApproveClient(approveInfo: Omit<IInitiateApprove, 'token' | 'userId'>) {
    const token = await this.getAccessToken();
    const userId = this.ctx.user.d_user_id;
    const deptId = this.ctx.user.d_dept_id;
    let actionerUserIds = [];
    if (approveInfo.contractInfo.shared_user_ids) {
      const userDetails = await this.service.authority.user.find({
        where: {
          id: {
            [Op.in]: approveInfo.contractInfo.shared_user_ids.split(',')
          },
          d_user_id: {
            [Op.ne]: ''
          }
        },
        raw: true
      });
      actionerUserIds = userDetails.map(it => it.d_user_id);
    }
    return DDClient.initiateApproveClient({
      ...approveInfo,
      token,
      userId,
      deptId,
      env: this.ctx.locals.env,
      actionerUserIds
    });
  }

  async getAuditDetail(processInstanceId: string) {
    const token = await this.getAccessToken();
    return DDClient.getAuditDetail({
      token,
      processInstanceId
    });
  }

  async autoConfirmAudit(processInstanceId: string, curStatus: string, approvalMemo: Record<string, any>) {
    const token = await this.getAccessToken();
    const detail = await this.getAuditDetail(processInstanceId);
    if (!detail.isSuccess) {
      return detail;
    }
    const tasks = detail.result.tasks;
    const runningTask = tasks.filter(it => it.status === 'RUNNING');
    if (!runningTask?.length) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `操作人：${this.ctx.user.pub_name}`,
        title: '审批实例自动同意失败'
      });
      return;
    }
    if (
      curStatus === AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT]
      || curStatus === AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]) {
      const approvalUserId = approvalMemo[curStatus]?.uid;
      if (approvalUserId) {
        const userDetail = await this.service.authority.user.findOne({
          where: {
            id: approvalUserId
          },
          raw: true
        });
        const dUserId = userDetail?.d_user_id;
        // 审核人排最前面，其他的当抄送人
        runningTask.sort((a, b) => {
          if (a.userId === dUserId) { return -1; }
          if (b.userId === dUserId) { return 1; }
          return 0;
        });

      }
    }
    runningTask.forEach((it, idx) => {
      if (idx > 0) { return; }
      DDClient.autoConfirmAudit({
        token,
        processInstanceId,
        taskId: it.taskId,
        actionerUserId: it.userId
      });
    });

  }

  // 业务流程中，需要自动同意, 如果需要资产组审核，同意前3个，否则，同意前2个
  async autoConfirmProcess(contractId: number, processInstanceId: string) {
    const contractItem = await this.service.contractManage.index.findOne({
      where: {
        id: contractId
      },
      attributes: ['asset_team_audit_required', 'approval_memo'],
      raw: true
    });
    let count = 3;
    let stepStatus = [AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT], AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.TAX_SUBMIT], AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]];
    if (contractItem?.asset_team_audit_required === 1) {
      count = 4;
      stepStatus = [AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.ASSET_SUBMIT], AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT], AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.TAX_SUBMIT], AUDIT_SUBMIT_TYPE[CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]];
    }
    await new Promise(resolve => setTimeout(resolve, 2000)); // 先等待2秒, 不然钉钉那边任务会为空
    for (let i = 0; i < count; i++) {
      await this.autoConfirmAudit(processInstanceId, stepStatus[i], contractItem.approval_memo);

      // 等待1秒
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async downloadAuditFile({
    processInstanceId,
    fileId
  }) {
    const token = await this.getAccessToken();
    const result = await DDClient.downloadAuditFile({
      token,
      processInstanceId,
      fileId
    });
    if (!result.isSuccess) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `审批实例ID: ${processInstanceId}`,
        title: '下载双签合同附件失败'
      });
    }
    return result;
  }
  private createTodoClient(): dingtalktodo_1_0 {
    if (this.TODO_CLIENT) {
      return this.TODO_CLIENT;
    }
    const config = new $OpenApi.Config({});
    config.protocol = 'https';
    config.regionId = 'central';
    const client = new dingtalktodo_1_0(config);
    this.TODO_CLIENT = client;
    return client;
  }

}
