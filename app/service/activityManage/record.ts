
import { Service } from 'egg';

export default class CrmActiveService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.ActivityManage.Record.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.ActivityManage.Record.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.ActivityManage.Record.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.ActivityManage.Record.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ActivityManage.Record.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.ActivityManage.Record.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
}
