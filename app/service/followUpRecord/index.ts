
import { Service } from 'egg';

export default class CrmFollowUpRecordService extends Service {
  async find(query) {
    const { ctx } = this;
    const result = await ctx.model.FollowUpRecord.Index.findAll(query);
    return result;
  }

  async findOne(query) {
    const { ctx } = this;
    const result = await ctx.model.FollowUpRecord.Index.findOne(query);
    return result;
  }

  async count(query) {
    const { ctx } = this;
    const count = await ctx.model.FollowUpRecord.Index.count(query);
    return count;
  }

  async create(data) {
    const { ctx } = this;
    const result = await ctx.model.FollowUpRecord.Index.create(data, { individualHooks: true, egg: this });
    return result;
  }

  async update(query, data) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.FollowUpRecord.Index.update(data, { where: query, individualHooks: true, egg: this });
    return result;
  }

  async destroy(query) {
    const { ctx } = this;
    query = typeof query === 'object' ? query : { id: query };
    const result = await ctx.model.FollowUpRecord.Index.destroy({
      where: query,
      individualHooks: true,
      egg: this
    });
    return result;
  }
}
