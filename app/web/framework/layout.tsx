import React from 'react';
export default function Layout({ query, children, env, title = 'ssr-base', keywords = 'ssr', description = 'ssr-base' }) {
  const isDebug = query && query.hasOwnProperty('debug');
  return (
    <html>
      <head>
        <title>{title}</title>
        <meta charSet="utf-8"></meta>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"></meta>
        <meta name="keywords" content={keywords}></meta>
        <meta name="description" content={description}></meta>
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"></link>
      </head>
      <body>
        <main id="app">{children}</main>
        <script src="https://polyfill.io/v3/polyfill.min.js?features=es2015%2Ces2016%2Ces2017%2Ces2018%2Ces2019%2Ces6%2Ces7|gated&flags=gated&unknown=polyfill&callback=onPolyfillsLoad"></script>
        {env === 'prod' && (
          <>
            <script src="/public/lib/debug-console.js"></script>
            {isDebug ? <script>debugConsole(true)</script> : <script>debugConsole(false)</script>}
          </>
        )}
        {/* debug */}
        {isDebug && (
          <>
            <script src="/public/lib/vconsole.min.js"></script>
            <script>var vConsole = new VConsole();</script>
          </>
        )}
      </body>
    </html>
  );
}
