import React from 'react';
import ReactDOM from 'react-dom';
import Layout from './layout';

function bootstrap(Page) {
  function Boot(props) {
    return <Page {...props} />;
  }

  function ServerPage(props) {
    return (
      <Layout {...props}>
        <Boot {...props} />
      </Layout>
    );
  }

  // 服务端返回渲染模版
  if (EASY_ENV_IS_NODE) {
    return ServerPage;
  }

  // 浏览器端
  const props = window.__INITIAL_STATE__;
  const root = document.getElementById('app');
  const renderMethod = 'render';
  ReactDOM[renderMethod](<Boot {...props} />, root);
  if (EASY_ENV_IS_DEV) {
    module.hot.accept(() => {
      ReactDOM[renderMethod](<Boot {...props} />, root);
    });
  }

  // 注入copy脚本，解决复制带有空格的问题
  window.onload = function () {
    window.addEventListener('copy', (event: any) => {
      try {
        const curSelectionDom = document.getSelection();
        if (!event.clipboardData || !curSelectionDom) return;
        const copyText = curSelectionDom.toString().trim();
        event.clipboardData.setData('text/plain', copyText);
        event.preventDefault();
        event.stopPropagation();
      } catch { }
    })
  }
}

export default bootstrap;
