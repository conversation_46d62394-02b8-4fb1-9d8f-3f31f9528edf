import React, { useEffect, useState, useContext } from 'react';
import { useQueryList } from '@/hooks';
import { formatDate } from '@/modules/format';
import { EditUserModal, EmpowerModal, confirmModal, Filter, TableMain, CustomTable } from '@/component';
import { Tag, Space, Button, message, Image, Avatar, Popconfirm } from 'antd';
import { MehOutlined, PlusOutlined, Loading3QuartersOutlined } from '@ant-design/icons';
import request from '@/modules/request';
import { __ } from '@/modules/format';
import { AppContext } from '@/store';
import './index.scss';
import LoadingButton from '@/component/loading-button'
import CTag from '@/component/custom-tag'
import useUserStore from '@/store/user.store';
import { isValidArray, sleep, twoArraysCompare } from '@@/lib/tool';

export default function User() {
  const currentUserInfo = useUserStore(state => ({
    id: state.id,
    username: state.realname || state.username,
    dingUserId: state.dingUserId
  }));
  const {
    loading,
    listData,
    fetchData: fetchDataOrigin,
    total,
    pageIndex,
    setPageIndex,
    pageSize,
    setPageSize
  } = useQueryList({
    fetchUrl: '/omsUser',
    pageSize: 20
  });
  const { localeData } = useContext(AppContext);
  const [params, setParams] = useState({} as any);
  const [currentItem, setCurrentItem] = useState<any>({

  });
  const [userRoleList, setUserRoleList] = useState<any>([]);
  const [adsUserList, setAdsUserList] = useState<any>([]);
  const curRoleCode = useUserStore(state => state.roleCode);
  const handleDeleteUser = item => {
    confirmModal(
      item,
      () => {
        request
          // @ts-ignore
          .delete(`/omsUser/${item.id}`, { 'X-showMessage': true })
          .then(() => {
            fetchDataOrigin(params);
          })
          .catch(console.error);
      },
      { title: __('Delete', 'User', 'Operate') + '（硬删除）', content: `${item.pub_name || ''}` }
    );
  };

  // 用户授权后续操作
  const handleAfterAuth = async (values: any[], diffObj: any) => {
    fetchDataOrigin({
      ...params
    });
    const { env } = window.__INITIAL_STATE__;

    let changeText = '无';
    if (isValidArray(diffObj.addArr)) {
      changeText = `新增“${diffObj.addArr
        .map(i => i.oms_role?.name)
        .join(', ')}”`;
    }
    if (isValidArray(diffObj.delArr)) {
      if (changeText.length > 1) {
        changeText += ` / 移除“${diffObj.delArr
          .map(i => i.oms_role?.name)
          .join(', ')}”`;
      } else {
        changeText = `移除“${diffObj.delArr
          .map(i => i.oms_role?.name)
          .join(', ')}”`;
      }
    }

    if (currentItem.id === currentUserInfo.id) {
      message.destroy();
      message.info(`The current user role rights have been updated, the system is about to refresh...`);
      await sleep(1500);
      window.location.reload();
    } else {
      message.destroy();
      message.info(`${currentItem.pub_name} Role permissions have been updated`);
      if (window.__INITIAL_STATE__.env === 'local') return;
      request.post('/dingtalk/notifyUserRoleAuthIsApprove', {
        approver: {
          id: currentUserInfo.id,
          username: currentUserInfo.username
        },
        targetUser: {
          id: currentItem.id,
          username: currentItem.pub_name
        },
        auths: values
      });

    }
  };

  const columns = [
    {
      title: `${localeData.UserName}`,
      dataIndex: 'name',
      hidden: true,
      filter: {
        type: 'Input',
        allowClear: true
      }
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60
    },
    {
      title: 'User Name',
      dataIndex: 'pub_name',
      render(value, record) {
        return (
          <Space direction="horizontal">
            {record.avatar ? (
              <Image
                width={38}
                height={38}
                preview={false}
                src={record.avatar}
                loading="lazy"
                placeholder
                style={{
                  borderRadius: '50%',
                  aspectRatio: 'auto',
                  objectFit: 'cover'
                }}
              />
            ) : (
              <Avatar>{value}</Avatar>
            )}
            <span>{value}</span>
          </Space>
        );
      }
    },

    {
      title: 'User Roles',
      align: 'center',
      dataIndex: 'crm_oms_user_roles',
      filter: {
        type: 'Select',
        options: userRoleList.map((item) => {
          return { label: item.name, value: item.code };
        }),
        allowClear: true
      },
      render: arr => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          {
            arr.map((item, index) => (
              <Tag key={index} color="geekblue" style={{ alignSelf: 'center' }}>
                {item.crm_oms_role?.name}
              </Tag>
            ))
          }
        </div>
      )
    },
    {
      title: 'Email',
      align: 'center',
      dataIndex: 'email',
      filter: {
        type: 'Input'
      }
    },
    {
      title: 'ctime',
      align: 'center',
      dataIndex: 'ctime',
      render: time => formatDate(time * 1000, 'yyyy-MM-dd hh:mm:ss')
    },
    {
      title: 'DingTalk Association',
      dataIndex: 'd_user_id',
      align: 'center',
      filter: {
        type: 'Select',
        options: [
          { label: 'Success', value: '1' },
          { label: 'Failed', value: '2' }
        ]
      },
      render(_, record) {
        const isSuc = record.d_user_id && record.d_union_id;
        return isSuc ? <CTag classType="enable">Success</CTag> : <CTag classType='disable'>Failed</CTag>;
      }
    },
    {
      title: 'Operation',
      width: 250,
      fixed: 'right',
      align: 'center',
      render: (text, record) => (
        <Space size="middle">
          <Button
            type="primary"
            onClick={() => {
              record.username = record.pub_name;
              EditUserModal.prototype.open('edit', {
                ...record,
                crm_audit_is_open: !!record.crm_audit_type,
                crm_audit_type: record.crm_audit_type ? record.crm_audit_type.split(',') : [],
                bd_member: record.bd_member ? record.bd_member.split(',') : [],
                op_member: record.op_member ? record.op_member.split(',') : [],
              });
            }}
          >
            {localeData.Edit}
          </Button>
          <Popconfirm
            title="Are you sure to delete the user?"
            onConfirm={() => {
              return request
                // @ts-ignore
                .delete(`/omsUser/${record.id}`, { 'X-showMessage': true })
                .then(() => {
                  fetchDataOrigin(params);
                })
                .catch(console.error);
            }}
          >
            <Button
              danger
            >
              {localeData.Delete}
            </Button>
          </Popconfirm>

          <Button
            onClick={() => {
              setCurrentItem({
                ...record
              });
              EmpowerModal.prototype.open('add', { code: record.id, userRoles: record.crm_oms_user_roles });
            }}
          >
            {localeData.Role}
          </Button>
        </Space>
      )
    }
  ];

  useEffect(() => {
    fetchDataOrigin(params);
    getUserRoleList();
    getAdsUserList();
  }, []);

  const onTableChange = pagination => {
    if (pageSize !== pagination.pageSize) {
      setPageSize(pagination.pageSize);
      fetchDataOrigin({
        ...params,
        pageSize: pagination.pageSize,
        pageIndex,
      });
    }
    if (pageIndex !== pagination.current) {
      setPageIndex(pagination.current);
      fetchDataOrigin({
        ...params,
        pageSize,
        pageIndex: pagination.current,
      });
    }
  };

  /** 获取角色名列表 */
  function getUserRoleList() {
    request
      .get('/oms-user-role-list')
      .then((res: any) => {
        const { data } = res;
        setUserRoleList(data);
      })
      .catch(error => {
        const msg = error?.message ?? 'failed';
        message.error(msg);
      });
  }

  // 获取adx用户列表
  function getAdsUserList() {
    request
      .get('/getAdsUser')
      .then((res: any) => {
        const { data } = res;
        console.log('111', data)
        setAdsUserList(data);
      })
      .catch(error => {
        const msg = error?.message ?? 'failed';
        message.error(msg);
      });
  }

  return (
    <section className="user-wrap section-item">
      <Filter
        columns={columns}
        filterColumnNum={4}
        onChange={(values: any) => {
          setParams(values);
          fetchDataOrigin({
            ...values,
            pageIndex: 1
          });
          setPageIndex(1);
        }}
      />
      {/* <div className="operate-bar">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            EditUserModal.prototype.open('add', null);
          }}
        >
          {localeData.Add}
        </Button>
      </div> */}
      <TableMain
        title={''}
        btnGroup={
          <Space>
            {
              curRoleCode.includes('role-superadmin') && (
                <LoadingButton
                  type=""
                  api="ddCloud/settingDDUserIds"
                  method='post'
                  onOk={() => {
                    message.success('setting success');
                    fetchDataOrigin(params);
                  }}
                >Setting DD Info</LoadingButton>
              )
            }
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                EditUserModal.prototype.open('add', null);
              }}
            >
              {localeData.Create}
            </Button>
          </Space>
        }
        table={<CustomTable onChange={onTableChange}
          rowKey="id" loading={loading} columns={columns} pagination={{ total, current: pageIndex, defaultPageSize: pageSize }} dataSource={listData} style={{ width: '100%' }} />}
      />
      {/* <Divider /> */}
      {/* <Table rowKey="id" loading={loading} columns={columns} pagination={{ size: 'small', position: ['bottomCenter'] }} dataSource={listData} style={{ width: '100%' }}></Table> */}
      <EditUserModal title={localeData.User} adsUserList={adsUserList} onSuccess={() => [
        fetchDataOrigin(params),
      ]} name="omsUser" />
      <EmpowerModal title={localeData.Permission} name="userRole" onSuccess={(values, oldValues) => {
        const obj = twoArraysCompare(values, oldValues, 'role_code');
        handleAfterAuth(
          values.map(i => i.role_code),
          obj
        );
      }} />
    </section>
  );
}
