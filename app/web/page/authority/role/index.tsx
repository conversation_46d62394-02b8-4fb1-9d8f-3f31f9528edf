import React, { useEffect, useContext, useState } from 'react';
import { useQueryList } from '@/hooks';
import { But<PERSON>, Divider, Spin, Alert, Space, message } from 'antd';
import { EditRoleModal, EmpowerModal, confirmModal } from '@/component';
import { PartitionOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import request from '@/modules/request';
import { __ } from '@/modules/format';
import './index.scss';
import useUserStore from '@/store/user.store';

import EditRoleOperateAuthModal from '../../../component/edit-role-operate-auth-modal';
import { sleep } from '@@/lib/tool';



export default function authority(props) {
  const {
    loading,
    listData,
    fetchData: fetchDataOrigin,
  } = useQueryList({
    fetchUrl: '/role',
    pageSize: 20
  });
  const currentUserInfo = useUserStore(state => ({
    roles: state.roleCode
  }));
  const {
    BaseData: { authType },
    localeData
  } = useContext(AppContext);
  const [editRoleOperateAuthModalVisible, setEditRoleOperateAuthModalVisible] = useState(false);

  const [currentItem, setCurrentItem] = useState<any>({
    name: '',
    crm_oms_role_auths: []
  });

  useEffect(() => {
    fetchDataOrigin();
  }, []);

  async function handleDelete(item) {
    confirmModal(
      item,
      () => {
        request
          .delete(`/role/${item.id}?code=${item.code}`, { 'X-showMessage': true })
          .then(res => {
            fetchDataOrigin();
          })
          .catch(console.error);
      },
      { title: __('Delete', 'Role', 'Operate'), content: `${item.name} - ${item.code}` }
    );
  }

  const handleAfterAuthApprove = async () => {
    if (currentUserInfo.roles.includes(currentItem.code)) {
      message.success(`当前角色权限配置已更新，系统准备刷新...`);
      await sleep(2000);
      window.location.reload();
    } else {
      message.success(`${currentItem.name}权限配置已更新`);
    }
  };

  return (
    <section className="role-wrap section-item card-wrapper">
      <div className="operate-bar">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            EditRoleModal.prototype.open('add', null);
          }}
        >
          {localeData.Role}
        </Button>
      </div>
      <Divider />
      <article>
        {loading && <Spin tip={`${localeData.Loading}...`} />}
        <ul className="auth-list">
          {listData.map(item => (
            <li key={item.id}>
              <div className="auth-item">
                <p className="auth-name">
                  {item.name}
                  <small>({item.code})</small>
                </p>
                <span className="dash-divider"></span>
                <Space className="btn-group">
                  {/* {!['role-superadmin', 'role-admin'].includes(item.code) && (
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      size="small"
                      onClick={() => {
                        handleDelete(item);
                      }}
                    >
                      {localeData.Delete}
                    </Button>
                  )} */}
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => {
                      EditRoleModal.prototype.open('edit', item);
                    }}
                  >
                    {localeData.Edit}
                  </Button>
                  <Button
                    type="primary"
                    icon={<PartitionOutlined />}
                    size="small"
                    onClick={() => {
                      EmpowerModal.prototype.open('add', { code: item.code, roleAuths: item.crm_oms_role_auths });
                    }}
                  >
                    {localeData.MenuPermissions}
                  </Button>

                  <Button
                    type="primary"
                    icon={<PartitionOutlined />}
                    size="small"
                    onClick={() => {
                      setCurrentItem(item);
                      setEditRoleOperateAuthModalVisible(true);
                    }}
                  >
                    {localeData.OperatePermissions}
                  </Button>
                </Space>
              </div>
            </li>
          ))}
        </ul>
      </article>
      <Alert message={localeData.PermissionTips} description={localeData.RoleTipsText} type="info" closable />
      <EditRoleModal
        title={localeData.Role}
        name="role"
        onSuccess={() => {
          fetchDataOrigin({ pageIndex: 1, pageSize: 20 });
        }}
      />
      <EmpowerModal
        title={localeData.Permission}
        name="roleAuth"
        onSuccess={() => {
          fetchDataOrigin({ pageIndex: 1, pageSize: 20 });
        }}
      />

      <EditRoleOperateAuthModal
        title={`角色操作权限 / ${currentItem.name}`}
        auths={currentItem.crm_oms_role_auths.map(i => ({ id: i.id, auth_code: i.auth_code, operate_auth: i.operate_auth }))}
        visible={editRoleOperateAuthModalVisible}
        onCancel={() => setEditRoleOperateAuthModalVisible(false)}
        onOk={() => {
          setEditRoleOperateAuthModalVisible(false);
          fetchDataOrigin();
          handleAfterAuthApprove();
        }}
      />
    </section>
  );
}
