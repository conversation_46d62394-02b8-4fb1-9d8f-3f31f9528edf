.authority-wrap {
  height: 100%;

  .operate-bar {
    text-align: right;
  }

  article {
    min-height: 200px;
    position: relative;

    >.ant-spin {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .auth-list {
    list-style: none;
    padding-left: 0;

    li {
      padding: 5px 0;
      margin-bottom: 5px;

      small {
        color: #aaa;
        margin-left: 10px;
      }

      .auth-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        @include transition();

        &:hover {
          background-color: #ddd;
        }
      }

      .auth-name {
        margin-bottom: 0;
        margin-right: 20px;
        position: relative;
        padding: 5px 0px 5px 5px;
      }

      .up-down-btn {
        position: absolute;
        left: -56px;
        top: 2px;
        width: 55px;
        text-align: right;

        .move-down-btn {
          margin-left: 3px;
        }
      }

      .dash-divider {
        flex: 1;
        border-bottom: 1px dashed #ddd;
      }
    }

    .btn-group {
      margin-left: 20px;
    }

    & {
      margin-left: 26px;

      li {
        padding: 2px 0;
        margin-bottom: 0px;
      }
    }
  }
}
