import React, { useEffect, useState, useContext } from 'react';
import { getTargetText } from '@@/lib/tool';
import { useQueryList } from '@/hooks';
import request from '@/modules/request';
import { __ } from '@/modules/format';
import { Button, Divider, Spin, Space, Tooltip } from 'antd';
import { EditAuthModal, confirmModal } from '@/component';
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import './index.scss';

export default function authority() {
  const {
    loading,
    listData,
    fetchData: fetchDataOrigin } = useQueryList({
      fetchUrl: '/authority',
      pageSize: 20
    });
  const {
    BaseData: { authType },
    localeData
  } = useContext(AppContext);

  useEffect(() => {
    fetchDataOrigin();
  }, []);

  async function handleDelete(item) {
    // console.log(99, item);
    confirmModal(
      item,
      () => {
        request
          // @ts-ignore
          .delete(`/authority/${item.id}?code=${item.code}`, { 'X-showMessage': true })
          .then(() => {
            fetchDataOrigin();
          })
          .catch(console.error);
      },
      { title: __('Delete', 'Permission', 'Operate'), content: `${item.path} - ${item.code}` }
    );
  }

  function handlePosition(item, index, step, arr) {
    // console.log(123, item, index, step, arr);
    const target = arr[index + step];
    confirmModal(
      item,
      () => {
        request
          .put(
            `/authority/${item.id}`,
            [
              { id: item.id, position: target.position },
              { id: target.id, position: item.position }
            ],
            { 'X-showMessage': true }
          )
          .then(() => {
            fetchDataOrigin();
          })
          .catch(console.error);
      },
      { title: '移动位置操作', content: `将向${step > 0 ? '下' : '上'}移动一位: ${item.path} - ${item.remark}` }
    );
  }

  const renderAuthList = (arr, level) => {
    return arr && arr.length > 0 ? (
      <ul key={arr.length} className={`auth-list`}>
        {arr.map((item, index) => (
          <li key={item.id} className={`J_${item.type}_${level}`}>
            <div className="auth-item">
              <div className="auth-name">
                <div className="up-down-btn">
                  {index !== 0 && (
                    <Tooltip placement="top" title={'上移'}>
                      <Button
                        icon={<ArrowUpOutlined />}
                        size="small"
                        onClick={() => {
                          handlePosition(item, index, -1, arr);
                        }}
                      ></Button>
                    </Tooltip>
                  )}

                  {index !== arr.length - 1 && (
                    <Tooltip placement="top" title={'下移'}>
                      <Button
                        className="move-down-btn"
                        icon={<ArrowDownOutlined />}
                        size="small"
                        onClick={() => {
                          handlePosition(item, index, 1, arr);
                        }}
                      ></Button>
                    </Tooltip>
                  )}
                </div>
                {item[item.type === 'menu' ? 'path' : 'name']}
                <small>
                  {item.remark} ({item.code}、{getTargetText(item.type, authType)})
                </small>
              </div>
              <span className="dash-divider"></span>
              <Space className="btn-group">
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  size="small"
                  onClick={() => {
                    EditAuthModal.prototype.open('edit', { item });
                  }}
                >
                  {localeData.Edit}
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  onClick={() => {
                    handleDelete(item);
                  }}
                >
                  {localeData.Delete}
                </Button>
                {/* <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  size="small"
                  onClick={() => {
                    EditAuthModal.prototype.open('add', { item, type: 'parent' });
                  }}
                >
                  父权限
                </Button> */}
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  size="small"
                  onClick={() => {
                    EditAuthModal.prototype.open('add', { item, type: 'child' });
                  }}
                >
                  {__('Child', 'Permission')}
                </Button>
              </Space>
            </div>
            {renderAuthList(item.children, level + 1)}
          </li>
        ))}
      </ul>
    ) : null;
  };

  return (
    <section className="authority-wrap section-item card-wrapper">
      <div className="operate-bar">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            EditAuthModal.prototype.open('add', null);
          }}
        >
          {localeData.Permission}
        </Button>
      </div>
      <Divider />
      <article>
        {loading && <Spin tip={`${localeData.Loading}...`} />}
        {authType.map(item => (
          <div key={item.value}>{renderAuthList(listData[item.value], 0)}</div>
        ))}
      </article>
      <EditAuthModal title={localeData.Permission} name="authority" onSuccess={fetchDataOrigin} />
    </section>
  );
}
