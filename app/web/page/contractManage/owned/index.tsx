import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  FlatTableActionType,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import moment from 'moment';
import BasePageHoc from '@/component/base-page-hoc'
import { beforeSubmitHandler, usePrevCreateState } from '../hook/index'
import OperateAccess from '@/component/auth/operate-access';
import { Button, Space, Tooltip } from 'antd';
import CreateType from '../components/CreateType';
import CustomerAssociation from '../components/CustomerAssociation';
import { urlsToAntdUploadList } from '@/modules/utils';
import { COMMON_BEFORE_FILTER, CONTRACT_CREATE_TYPE, CONTRACT_TYPE_ENUM } from '@@/lib/constant';
import PopConfirmBtn from '../components/PopConfirmBtn';
import CustomerDetail from '@/page/clueManage/components/CustomerDetail';
import ChatRecord from '../components/ChatRecord';
import { getIsApproveProcess, getStatusMap, notNeedAssetAudit } from '../hook/contact'
import { debounce } from '@@/lib/tool';
import { clearUrlSearchEffect } from '@/hooks';
import '../approval/index.scss'
import { beforeEditHandler, useBasicState, useContractState, useEditHandlerStrategy } from '../hook/useCommonState';
import { AppContext } from '@/store';
import request from "@/modules/request";
import { CreateModal } from '@/component';
import Iconfont from '@/component/iconfont';
import SharedModal from '../components/SharedModal';


let isNeedPageParamsOpen: null | boolean = false;
let searchParamsId: number | string | null = 0;


function ContractApproval() {
  const searchParams = new URLSearchParams(location.search);
  const { localeData } = useContext(AppContext);
  const [chatCount, setChatCount] = useState(0); // 用于标记草稿状态是否显示消息列表(回退单才会有)
  const [shareModalInfo, setShareModalInfo] = useState({
    visible: false,
    itemData: {} as Recordable
  });

  const {
    CUSTOMER,
    curUserInfo,
    setDataType,
    customerAssociation,
    setCustomerAssociation,
    pageItemData,
    setPageItemData,
    tabsRef
  } = useBasicState()


  const {
    columns,
    curEditItem,
    curEditItemStatus,
    customerDetail,
    setCustomerDetail,
    isApplicant,
    isCreate,
    setCurEditItem,

  } = useContractState({
    itemData: pageItemData, setItemData: setPageItemData,
    getTableProps: () => tableProps
  }, 'owned');

  const shareHandler = () => {
    setShareModalInfo({
      visible: true,
      itemData: {}
    })
  }

  const beforeCreateRef = useRef<any>()
  const tableProps = useFlatTable({
    title: 'Contract Approval',
    fetchUrl: '/contract',
    actionWidth: 220,
    pageSize: 20,
    columns,
    defaultParams: {
      id: searchParams.get('id') || undefined,
      PAGE_TYPE: 'owned',
    },
    renderEditText(_, record) {
      const statusMap = getStatusMap(record.status);
      const isApproveProcess = getIsApproveProcess({
        statusMap,
        curUserInfo,
        contractType: record.contract_type
      })
      return isApproveProcess ? 'Approval' : 'Edit'
    },
    extra: (
      isCreate ? null : (
        <Tooltip title="Share the contract with other accounts">
          <Iconfont type="icon-fenxiang" style={{ cursor: 'pointer', fontSize: 20 }} onClick={shareHandler} />
        </Tooltip>
      )
    ),
    actions: [
      (record) => {
        if (record.apply_id === curUserInfo.id && record.status === CONTRACT_TYPE_ENUM.OA_PENDING) {
          return (
            <OperateAccess.Update>
              <PopConfirmBtn
                content="Confirm application for DingTalk contract approval ？"
                onConfirm={async () => {
                  const values: Recordable = {
                    ...record,
                    // 发起钉钉工单，状态为钉钉审核中
                    status: CONTRACT_TYPE_ENUM.OA_APPROVAL
                  }
                  const { id, ...updateData } = values;
                  await tableProps.handleUpdate(updateData, record.id)
                }}
              >
                <Button type='primary'>Apply for DingTalk</Button>

              </PopConfirmBtn>
            </OperateAccess.Update>
          )
        }
        return null
      },
      FlatTableActionType.Edit,

    ],
    btnGroup: [
      BtnGroupType.Create,
      // BtnGroupType.Export,
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    beforeCreate() {
      setCurEditItem(null);
      return {}
    },
    async beforeEdit(record) {
      beforeEditHandler(record)
      setPageItemData({
        ...pageItemData,
        pay_type: record.pay_type,
        contract_attachment: 'previous'
      })
      setCurEditItem(record);
      return record;
    },
    isDisabledAction(type, record) {
      if (type === 'delete') {
        return record.status !== CONTRACT_TYPE_ENUM.DRAFT
      }
      return false;
    },
    async beforeHandleCreate(values) {
      if (values.create_type === CONTRACT_CREATE_TYPE.DRAFT) {
        values.status = CONTRACT_TYPE_ENUM.DRAFT // 草稿
      } else {
        const notNeedAsset = notNeedAssetAudit(values.contract_type);

        if (!notNeedAsset) {
          values.asset_team_audit_required = 1;
        }
        values.status = notNeedAsset ? CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL : CONTRACT_TYPE_ENUM.ASSET_APPROVAL
      }
      beforeSubmitHandler(values);
      return {
        isSuc: true,
        values: values
      }
    },
    async beforeHandleUpdate(values) {
      beforeSubmitHandler(values);

      return {
        isSuc: true,
        values
      }
    },
    graspBeforeCreate(beforeCreateFunc) {
      setCreateType({
        visible: true,
        itemData: {}
      })
      beforeCreateRef.current = {
        beforeCreateFunc,
      }
    },

    // filter
    beforeFilter(params) {
      const filters = COMMON_BEFORE_FILTER(params)
      return filters
    },

    afterCreate() {

      tabsRef.current?.fetchPendingCountHandler?.()
    },
    afterUpdate() {
      tabsRef.current?.fetchPendingCountHandler?.()
    },

    dataFetchAfter: debounce((res) => {
      const pageId = searchParamsId;
      if (pageId && res?.[0] && !isNeedPageParamsOpen) {
        isNeedPageParamsOpen = true;
        tableProps.onEdit(res[0], 0);
      }
    }, 500),
  });

  const {
    columns: prevCreateColumns,
    createType, setCreateType,
    handleCreate: createTypeHandleCreate,
    createTypeRef,

    customerLeadColumn,
    customerLeadModal,
    setCustomerLeadModal,
    handleCreateClueAndCustomer

  } = usePrevCreateState(beforeCreateRef, CUSTOMER, (pageData) => {
    setPageItemData({
      ...pageItemData,
      ...pageData
    })
  })

  const {
    applicantEditHandlerStrategy,
    approverEditHandlerStrategy
  } = useEditHandlerStrategy({
    tableProps,
    curUserInfo,
    contractType: curEditItem?.contract_type,
    curEditItem: curEditItem || {}
  })

  const getChatCount = async () => {
    if (!curEditItem?.id) { return 0 };
    const res = await request.get('/contractChat/getChatCount', {
      contract_id: curEditItem?.id
    })
    setChatCount(res.data)
  }

  useEffect(() => {
    return () => {
      isNeedPageParamsOpen = null;
      searchParamsId = null;
    }
  }, []);

  useEffect(() => {
    if (!curEditItem?.id) { return };
    getChatCount()
  }, [curEditItem])

  clearUrlSearchEffect()

  useEffect(() => {
    const type = searchParams.get('type');
    if (type === 'open_create') {
      setCreateType({
        visible: true,
        itemData: {}
      })
      beforeCreateRef.current = {
        beforeCreateFunc: tableProps.onCreate,
      }
    }
  }, [searchParams.get('type')])

  useEffect(() => {
    const id = searchParams.get('id');
    if (!id) { return };
    isNeedPageParamsOpen = false;
    searchParamsId = id;
    tableProps.setParams({
      ...tableProps.params,
      id
    })
  }, [searchParams.get('id')]);


  return <section className={`approval-list-wrap`}>

    <FlatTable {...tableProps}
      modalType="drawer"
      modalWidth={1300}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      confirmText="Submit"
      isHiddenConfirm={true} // 编辑禁用掉确认按钮，用customBtnCreate控制
      customBtnCreate={(type, form, onFinish, loading) => {
        if (type === 'create') {
          return (
            <Space>
              <OperateAccess.Create>
                <Button type='primary' loading={loading} disabled={tableProps.submitDisabled} onClick={async () => {
                  onFinish({
                    create_type: CONTRACT_CREATE_TYPE.DRAFT,
                  })
                }}>Save as draft</Button>
              </OperateAccess.Create>
              <OperateAccess.Create>
                <PopConfirmBtn
                  content="Confirm to Submit the contract approval form?"
                  disabled={tableProps.submitDisabled}
                  onConfirm={async () => {
                    await onFinish({
                      create_type: CONTRACT_CREATE_TYPE.TO_AUDIT,
                      is_auto_sync_client: 1,
                      submission_time: moment().format('YYYY-MM-DD HH:mm:ss')
                    });

                  }}
                >
                  <Button type='primary' disabled={tableProps.submitDisabled}>Submit</Button>
                </PopConfirmBtn>

              </OperateAccess.Create>

            </Space>
          )
        }

        if (type === 'update') {
          if (isApplicant && applicantEditHandlerStrategy[curEditItem?.status]) {
            return applicantEditHandlerStrategy[curEditItem?.status](form, onFinish, loading)
          }

          // 其他都属于审批人
          if (!approverEditHandlerStrategy[curEditItem?.status]) {
            return null;
          }
          return approverEditHandlerStrategy[curEditItem?.status](form, onFinish, loading)
        }

        // 无其他情况
        return null
      }}
      createModalClose={() => {
        setPageItemData({});
      }}
      updateModalClose={() => {
        setPageItemData({});
      }}

      chattingRecordsRender={(isCreate || (curEditItemStatus.IS_DRAFT && !chatCount)) ? null : (type) => {
        return (
          <ChatRecord contractStatus={curEditItem!.status} contractType={curEditItem!.contract_type} contractId={curEditItem!.id} applyId={curEditItem!.apply_id} />
        )
      }}
    />
    <CreateType
      columns={prevCreateColumns}
      visible={createType.visible}
      ref={createTypeRef}
      onChange={createTypeHandleCreate}
      onClose={(isRefresh) => {
        setCreateType({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <CustomerAssociation
      customers={CUSTOMER}
      visible={customerAssociation.visible}
      id={customerAssociation.id}
      onClose={(isRefresh) => {
        setCustomerAssociation({
          visible: false,
          id: 0
        });
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <CustomerDetail visible={customerDetail.visible} onClose={() => {
      setCustomerDetail({
        visible: false,
        itemData: {}
      })
    }} customerData={customerDetail.itemData} />

    <CreateModal
      modalType="drawer"
      title={`Create Customer`}
      columns={customerLeadColumn}

      visible={customerLeadModal.visible}
      setVisible={() => {
        // props.setVisible(visible)
        setCustomerLeadModal({
          visible: false,
          itemData: {}
        })
      }}
      itemData={customerLeadModal.itemData}
      onChange={handleCreateClueAndCustomer}
      width={940}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
    />

    {
      shareModalInfo.visible && <SharedModal itemData={curEditItem} onCancel={(sharedUserIds: string) => {
        if (sharedUserIds) {
          // 更新了id
          setCurEditItem({
            ...curEditItem,
            shared_user_ids: sharedUserIds
          })
        }
        setShareModalInfo({
          visible: false,
          itemData: {}
        })
      }} />
    }

  </section>
}

export default BasePageHoc(ContractApproval)
