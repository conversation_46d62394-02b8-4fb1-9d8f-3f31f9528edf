import useFlatTableColumns from "@/component/flat-table/useFlatTableColumns";
import { contractCommonFields } from "@/page/clueManage/hook/useCustomerColumns";
import { useContext, useMemo, useRef, useState } from "react";
import { CONTRACT_OUR_COMPANY_NAME, getIsApproveProcess, getIsAuditUser, getStatusMap, notNeedAssetAudit } from "./contact";
import { CONTRACT_CREATE_TYPE, CONTRACT_DATA_TYPE, CONTRACT_IS_EXCEED_AMOUNT, CONTRACT_STATUS, CONTRACT_TEMPLATE, CONTRACT_TYPE, CONTRACT_TYPE_ENUM, userIsAssetGroup, userIsFinance, userIsLegal, userIsTax } from "@@/lib/constant";
import useAppStore from "@/store/app.store";
import useSelector from "@/hooks/useSelector";
import { But<PERSON>, FormInstance, Popover, Space, Tag, Tooltip } from "antd";
import moment from "moment";
import OperateAccess from "@/component/auth/operate-access";
import UploadInput from "@/component/upload-file";
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { useCustomerDetailData } from "@/page/clueManage/hook/commonFunc";
import useUserStore from "@/store/user.store";
import PopConfirmBtn from "../components/PopConfirmBtn";
import { AppContext } from "@/store";
import { urlsToAntdUploadList } from "@/modules/utils";
import { isNumberReg } from "@@/lib/tool";


export const useBasicState = (dataKey: CONTRACT_DATA_TYPE = CONTRACT_DATA_TYPE.PENDING) => {
  const { ACCOUNTS, CUSTOMER } = useAppStore(useSelector(['ACCOUNTS', 'CUSTOMER']));
  const curUserInfo = useUserStore(state => state);
  const searchParams = new URLSearchParams(location.search);
  const isAuditUser = getIsAuditUser(curUserInfo)
  const [dataType, setDataType] = useState(dataKey);

  const { localeData } = useContext(AppContext);
  const [customerAssociation, setCustomerAssociation] = useState({
    visible: false,
    id: 0
  });
  const [pageItemData, setPageItemData] = useState<Recordable>({
    contract_attachment: 'previous',
    is_update: false
  });

  const tabsRef = useRef<any>(null);

  return {
    ACCOUNTS,
    CUSTOMER,
    curUserInfo,
    searchParams,
    isAuditUser,
    dataType,
    setDataType,
    customerAssociation,
    setCustomerAssociation,
    pageItemData,
    setPageItemData,
    localeData,
    tabsRef
  }

}

export const useContractState = ({ itemData = {}, setItemData, getTableProps }: any, PAGE_TYPE: 'pool' | 'owned' | 'all' | 'shared' = 'all') => {
  const { ACCOUNTS, CUSTOMER, ENUMS } = useAppStore(useSelector(['ACCOUNTS', 'CUSTOMER', 'ENUMS']));
  const curUserInfo = useUserStore(state => state);

  const [curEditItem, setCurEditItem] = useState<null | Recordable>(null);
  const isCreate = useMemo(() => {
    return !curEditItem
  }, [curEditItem]);
  const isApplicant = useMemo(() => {
    return curUserInfo.id === curEditItem?.apply_id
  }, [curEditItem])
  const curEditItemStatus = useMemo(() => {
    return getStatusMap(curEditItem?.status)
  }, [curEditItem?.status]);
  const isAllowEdit = (curEditItemStatus.IS_DRAFT || isCreate || curEditItemStatus.IS_APPROVAL_REJECTED || curEditItemStatus.IS_OA_REJECTED);

  const { customerDetail, setCustomerDetail, customClickHandler } = useCustomerDetailData()


  const isApprovalProcess = getIsApproveProcess({
    statusMap: curEditItemStatus,
    curUserInfo,
    contractType: curEditItem?.contract_type,
    record: PAGE_TYPE === 'pool' ? curEditItem || {} : {}
  })

  const columns = useFlatTableColumns([
    {
      key: 'id',
      align: 'left',
      width: 60,
      index: 0,
      filter: {
        type: 'Input',
      },

    },
    {
      key: 'contract_code',
      align: 'left',
      width: 150,
      index: 1,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Input',
        index: 4,
        span: 6,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        // @ts-ignore
        tooltip: 'Map to DingTalk OA: 合同编号 Contract No.'
      },
      update: true,
    },
    {
      key: 'contract_type',
      align: 'left',
      width: 200,
      filter: {
        type: 'Select',
        options: CONTRACT_TYPE,
        mode: 'multiple',
      },
      create: {
        type: 'Select',
        options: CONTRACT_TYPE,
        index: 4,
        span: 6,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        // @ts-ignore
        dropdownMatchSelectWidth: 500,
        tooltip: 'Map to DingTalk OA: 合同类型 Contract Type'
      },
      update: true,
      index: 2,
      render(type, record) {
        const label = CONTRACT_TYPE.find(it => it.value === type)?.label;
        if (!label) { return '-' };
        return (
          <Tooltip title={label}>
            {/* 两行超出隐藏 */}
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}>{label}</div>
          </Tooltip>
        )
      }
    },


    {
      key: 'apply_id',
      align: 'left',
      width: 130,
      hidden: PAGE_TYPE === 'owned',
      filter: {
        type: 'Select',
        options: ACCOUNTS,
        mode: 'multiple',
        hidden: PAGE_TYPE === 'owned',
      },
      create: {
        type: 'Select',
        disabled: true,
        options: ACCOUNTS,
        index: 1,
        span: 6,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
      },
      update: true,
      index: 4,
      render(id) {
        return ACCOUNTS.find(it => it.value === id)?.label || '-'
      }
    },


    {
      key: 'submission_time',
      align: 'left',
      width: 150,
      index: 10,
      sorter: true,
      defaultSortOrder: 'descend',
      render(time) {
        if (!time) { return '-' }
        return moment(time).format('YYYY-MM-DD')
      }
    },

    {
      key: 'complete_time',
      align: 'left',
      width: 140,
      index: 11,
      sorter: true,
      render(time) {
        if (!time) { return '-' }
        return moment(time).format('YYYY-MM-DD')
      }
    },
    {
      key: 'dual_signed',
      align: 'left',
      width: 160,
      index: 13,
      render(info) {
        if (!info) { return 'Not synced' };
        const isImg = info.file_name.endsWith('png') || info.file_name.endsWith('jpg') || info.file_name.endsWith('jpeg');
        let content: any = null;
        if (isImg) {
          content = (
            <Button type='link' onClick={() => {
              fetch(info.src)
                .then(response => response.blob())
                .then(blob => {
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = info.file_name; // 下载的文件名
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  window.URL.revokeObjectURL(url);
                })
            }}>
              <DownloadOutlined style={{ fontSize: 16 }} />

            </Button>
          )
        } else {
          content = (
            <a href={info.src} target='_blank' download="abcd.png">
              <DownloadOutlined style={{ fontSize: 16 }} />
            </a>
          )
        }
        return (
          <Space>
            Synced
            <OperateAccess.Export>
              <Button type='link'>
                {content}
              </Button>
            </OperateAccess.Export>
          </Space>

        )
      }
    },
    {
      key: 'shared_user_ids',
      align: 'left',
      width: 160,
      index: 13,
      checked: true,
      hidden: !['shared', 'owned'].includes(PAGE_TYPE),
      render(ids) {
        if (!ids) { return '-' }
        const res = ids.split(',').map(id => ACCOUNTS.find(it => Number(it.value) === Number(id))?.label)
        return (
          <Tooltip title={res.map((it, idx) => (
            <div key={idx}>{it}</div>
          ))}>
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', width: 150, whiteSpace: 'nowrap' }}>{res.join(',')}</div>
          </Tooltip>
        )
      }
    },
    {
      key: 'status',
      align: 'center',
      width: 150,
      fixed: 'right',
      title: (
        <Space>
          Contract Status
          <Popover content={
            <div>
              <p>草稿状态：Draft</p>
              <p>财务BP审核中：Finance BP Approval</p>
              <p>财务审核中：Finance Approval</p>
              <p>税务审核中： Tax Approval</p>
              <p>法务审核中：Legal Approval</p>
              <p>待发起钉钉OA审批单：OA pending</p>
              <p>钉钉OA合同审核中：OA Approval</p>
              <p>钉钉双签合同确认中：Dual-signature review</p>
              <p>已完成：	Completed</p>
              <p>钉钉OA工单审批拒绝：OA rejected</p>
              <p>CRM工单拒绝：Approval  rejected</p>
            </div>
          } >
            <QuestionCircleOutlined />
          </Popover>
        </Space>
      ),
      filter: {
        type: 'Select',
        options: CONTRACT_STATUS,
        mode: 'multiple',
        title: 'Contract Status'
      },
      index: 14,
      render(type, record) {
        const statusItem: Record<string, any> = CONTRACT_STATUS.find(it => it.value === type) || {};
        const transmittorInfo = record.transmittor_info || {};
        const forwarded: Recordable = Object.values(transmittorInfo).find((it: Recordable) => it.transmittor === curUserInfo.id) as Recordable;
        return (
          <div>
            <div>
              {
                statusItem.isTag ? (
                  <Tag style={{ marginRight: 0 }} color={statusItem.color}>
                    {statusItem.label || ''}
                  </Tag>
                ) : (
                  <span style={{ color: statusItem.color }}>{statusItem.label || ''}</span>
                )
              }
            </div>
            {
              PAGE_TYPE === 'pool' && !!forwarded && (
                <div style={{ fontSize: 12, color: '#919eab' }}>
                  Transferred <Tooltip title={`"${ACCOUNTS.find(it => it.value === forwarded.origin)?.label}" transfers to you`}>
                    <QuestionCircleOutlined />
                  </Tooltip>

                </div>
              )
            }
          </div>
        )
      }
    },

    // 创建的额外字段
    {
      key: 'customers',
      // hidden: false,
      align: 'left',
      index: 12,
      width: 200,
      filter: {
        type: 'Select',
        options: CUSTOMER,
        mode: 'multiple',
        dropdownMatchSelectWidth: 400,
      },
      create: {
        type: 'Select',
        index: 3,
        span: 6,
        mode: 'multiple',
        options: CUSTOMER,
        disabled: true,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
        maxTagCount: 3,
      },
      render(_, record) {
        const clients = record.crm_clients
        if (!clients?.length) {
          return '-'
        };
        return (
          <div>
            {
              clients.map(client => (
                <div className='link-jump-text' onClick={() => {
                  customClickHandler(client, {
                    bd_id: client.crm_clues[0]?.bd_id,
                    assist_id: client.crm_clues[0]?.assist_id

                  })
                }} key={client.id}>{client.client_name}</div>
              ))
            }
          </div>
        )
      },
      update: true
    },
    {
      key: 'template',
      align: 'left',
      hidden: true,
      filter: {
        index: 101,
        type: 'Cascader',
        options: CONTRACT_TEMPLATE,
        multiple: true,
      },
      create: {
        type: 'Cascader',
        index: 6,
        span: 6,
        options: CONTRACT_TEMPLATE,
        group: 'Basic info',
        disabled: !isAllowEdit,
        formItemProps: {
          required: true
        }
      },
      update: true,
    },
    {
      key: 'bu',
      align: 'left',
      hidden: true,
      create: {
        type: 'Input',
        index: 3,
        span: 6,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
        disabled: true,
      },
      update: true,
    },
    {
      key: 'pay_type',
      align: 'left',
      hidden: true,
      filter: {
        index: 99,
        type: 'Select',
        options: ENUMS.pay_type
      },
      create: {
        type: 'Select',
        index: 4,
        span: 6,
        options: ENUMS.pay_type,
        group: 'Payment Info',
        disabled: !isAllowEdit,
        formItemProps: {
          required: true
        },
        onChange(val) {
          setItemData({
            ...itemData,
            pay_type: val
          })
        }
      },
      update: true,
    },
    {
      key: 'payment_terms',
      align: 'left',
      hidden: true,
      filter: {
        index: 100,
        type: 'AutoComplete',
        options: [
          { label: '30', value: '30' },
          { label: '45', value: '45' },
          { label: '60', value: '60' },
        ]
      },
      create: {
        type: 'AutoComplete',
        index: 4,
        span: 6,
        group: 'Payment Info',
        disabled: !isAllowEdit,
        tooltip: '账期([0,365])',
        formItemProps: {
          required: itemData.pay_type === 'Post-Payment'
        },
        options: [
          { label: '30', value: '30' },
          { label: '45', value: '45' },
          { label: '60', value: '60' },
        ],
        rules: [
          {
            validator(_, value) {
              if (!value) { return Promise.resolve() }
              return !isNumberReg.test(value) || Number(value) < 0 ? Promise.reject('The number is wrong. Decimals and negative numbers are not allowed.') : Promise.resolve()
            },
          }
        ],
      },
      update: true,
    },
    {
      key: 'is_exceed_amount',
      align: 'left',
      hidden: true,
      create: {
        type: 'Select',
        index: 7,
        span: 6,
        options: CONTRACT_IS_EXCEED_AMOUNT,
        group: 'Basic info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合同金额是否超￥10万Does the contract amount exceed ￥100K？'
      },
      update: true,
    },
    {
      key: 'currency',
      align: 'left',
      hidden: true,
      create: {
        type: 'Input',
        index: 8,
        span: 6,
        group: 'Payment Info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 币种 Currency'
      },
      update: true,
    },
    {
      key: 'amount',
      align: 'left',
      hidden: true,
      create: {
        type: 'InputNumber',
        index: 7,
        span: 6,
        group: 'Payment Info',
        precision: 3,
        min: 0,
        max: 99999999999,
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合同金额（元）Contract Amount'
      },
      update: true,
    },
    {
      key: 'our_company_name',
      align: 'left',
      width: 150,
      filter: {
        type: 'Input',
      },
      index: 5,
      create: {
        type: 'Select',
        index: 8,
        span: 6,
        options: CONTRACT_OUR_COMPANY_NAME,
        group: 'Our info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 我方签约主体 Our Company name'
      },
      update: true,
    },
    {
      key: 'our_contact_person',
      align: 'left',
      width: 150,
      filter: {
        type: 'Input',
      },
      index: 6,
      create: {
        type: 'Input',
        index: 9,
        span: 6,
        group: 'Our info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 我方对接人 Contact person from our side'
      },
      update: true,
    },
    {
      key: 'our_information',
      align: 'left',
      hidden: true,
      create: {
        type: 'Input',
        index: 10,
        span: 6,
        group: 'Our info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 我方联系方式 Contact information from our side'
      },
      update: true,
    },
    {
      key: 'partner_company_name',
      align: 'left',
      width: 220,
      filter: {
        type: 'Input',
      },
      index: 7,
      create: {
        type: 'Input',
        index: 11,
        span: 6,
        group: 'Partner info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合作方签约主体 Company Name of the Business Partner'
      },
      update: true,
    },
    {
      key: 'partner_contact_person',
      align: 'left',
      width: 180,
      filter: {
        type: 'Input',
      },
      index: 8,
      create: {
        type: 'Input',
        index: 12,
        span: 6,
        group: 'Partner info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合作方对接人 Contact person'
      },
      update: true,
    },
    {
      key: 'partner_information',
      align: 'left',
      hidden: true,
      create: {
        type: 'Input',
        index: 13,
        span: 6,
        group: 'Partner info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合作方联系方式 Contact Information'
      },
      update: true,
    },
    {
      key: 'bus_brand_name',
      align: 'left',
      hidden: true,
      create: {
        type: 'Input',
        index: 14,
        span: 6,
        group: 'Partner info',
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 合作方名称 Brand Name of the Business Partner'
      },
      update: true,
    },
    {
      key: 'effective_date',
      align: 'left',
      hidden: true,
      create: {
        type: 'DatePicker',
        index: 15,
        span: 6,
        showTime: false,
        formItemProps: {
          required: true
        },
        disabled: !isAllowEdit,
        group: 'Date info',
        tooltip: 'Map to DingTalk OA: 到期日期 Expiry Date'
      },
      update: true,
    },
    {
      key: 'expiry_date',
      align: 'left',
      hidden: true,
      create: {
        type: 'DatePicker',
        index: 16,
        span: 6,
        showTime: false,
        formItemProps: {
          required: true
        },
        group: 'Date info',
        disabled: !isAllowEdit,
        tooltip: 'Map to DingTalk OA: 到期日期 Effective Date'
      },
      update: true,
    },
    {
      key: 'is_auto_sync_client',
      hidden: true,
      create: {
        type: 'Checkbox',
        group: 'Date info',
        index: 17,
        span: 24,
        hidden: true
      },
      update: {
        type: 'Checkbox',
        group: 'Date info',
        index: 17,
        span: 24,
        hidden: true,
        // hidden: !curEditItem || curEditItem?.status !== CONTRACT_TYPE_ENUM.DRAFT,
      }
    },
    {
      key: 'contract_desc',
      align: 'left',
      width: 180,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'TextArea',
        span: 24,
        group: 'Date info',
        index: 18,
        formItemProps: {
          required: true,
        },
        disabled: !isAllowEdit,
        maxLength: 512,
        tooltip: '合同内容简述 Brief description of the contract',
      },
      update: true,
      index: 9,
      render(text) {
        return (
          <Tooltip title={text}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: 160 }}>
              {text}
            </div>
          </Tooltip>
        )
      }
    },
    {
      key: 'remark',
      hidden: true,
      create: {
        type: 'TextArea',
        span: 24,
        group: 'Date info',
        index: 19,
      },
      update: {
        type: 'TextArea',
        span: 24,
        group: 'Date info',
        index: 19,
        hidden: !isAllowEdit,
        maxLength: 256
      }
    },
    {
      key: 'contract_attachment',
      hidden: true,
      update: {
        hidden: !isApprovalProcess,
        type: 'Select',
        group: 'Approval Outcome',
        options: [
          { label: 'Use the previous file', value: 'previous' },
          { label: 'Upload a new file', value: 'upload' }
        ],
        onChange(newVal) {
          const tableProps = getTableProps();

          setItemData({
            ...tableProps.itemData,
            contract_attachment: newVal
          })
        },
        index: 1,
        formItemProps: {
          required: true
        }
      },
    },
    {
      key: 'attachment',
      align: 'left',
      hidden: true,
      create: {
        type: 'Component',
        span: 24,
        index: 25,
        labelCol: { span: 4 },
        group: 'Date info',
        formItemProps: {
          required: true,
        },
        render(form) {
          const tableProps = getTableProps();
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={tableProps.itemData}
              setItemData={tableProps.setItemData}
              isLargeMode={false}
              onUploadPercent={(percent) => {
                tableProps.setSubmitDisabled(percent !== 100)
              }}
              multiple
              maxCount={15}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg,.xlsx,.xls"
            />
          )
        }
      },
      update: {
        type: 'Component',
        span: 24,
        index: 25,
        disabled: isApprovalProcess && itemData.contract_attachment === 'previous',
        labelCol: { span: 4 },
        group: isApprovalProcess ? 'Approval Outcome' : 'Date info',
        formItemProps: {
          required: true,
        },
        render(form) {
          const tableProps = getTableProps();
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={tableProps.itemData}
              setItemData={tableProps.setItemData}
              isLargeMode={false}
              multiple
              maxCount={15}
              tipsRender={() => {
                if (itemData.contract_attachment === 'upload' && !itemData.is_update) {
                  return <div style={{ color: '#ff4d4f' }}>*Please upload new contract attachments</div>
                }
                return null
              }}
              onUploadPercent={(percent, isNotUpdate) => {
                const tableProps = getTableProps();
                tableProps.setSubmitDisabled(percent >= 0 && percent !== 100) // 删除的情况percent为undefined，所以要>=0

                if (percent === 100 && !isNotUpdate) {
                  setItemData({
                    ...itemData,
                    is_update: true
                  })
                }
              }}
              // 草稿或者是对应审核状态，不禁用
              disabled={!(curEditItemStatus.IS_DRAFT || (isApprovalProcess && itemData.contract_attachment === 'upload') || curEditItemStatus.IS_APPROVAL_REJECTED || curEditItemStatus.IS_OA_REJECTED)}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg,.xlsx"
            />
          )
        }
      },
    },
    {
      key: 'comments',
      hidden: true,
      update: {
        type: 'TextArea',
        span: 24,
        group: 'Approval Outcome',
        index: 26,
        hidden: !isApprovalProcess,
        formItemProps: {
          required: true
        }
      }
    },

  ],
    {
      Fields: {
        id: 'ID',
        status: 'Contract Status',
        apply_id: 'Applicant',
        contract_desc: 'Description of Contract',
        submission_time: 'Submission Time',
        complete_time: 'Complete Time',
        customers: 'Customer',
        is_auto_sync_client: 'Automatically save as contract template for customers',
        remark: 'Note',
        attachment: 'Attachment',
        contract_attachment: 'Contract attachment',
        comments: 'Approval Comments',
        dual_signed: 'Dual-Signed Contract',
        shared_user_ids: 'Shared',
        ...contractCommonFields
      }
    }
  );

  return {
    columns,
    curEditItem,
    curEditItemStatus,
    customerDetail,
    setCustomerDetail,
    isApplicant,
    isCreate,
    setCurEditItem

  }
}


export const useEditHandlerStrategy = ({
  tableProps,
  curUserInfo,
  contractType,
  curEditItem = {} as Recordable
}) => {


  const withdrawHandler = async (form: FormInstance, onFinish) => {
    const values: Recordable = {}
    values.status = CONTRACT_TYPE_ENUM.DRAFT; // 回到草稿
    await onFinish(values)
  };

  const rejectedHandler = async (form: FormInstance, type, onFinish) => {
    try {
      const values: Recordable = {}
      values.status = CONTRACT_TYPE_ENUM.APPROVAL_REJECTED // 回到草稿
      values.create_type = type;
      values.complete_time = moment().format('YYYY-MM-DD HH:mm:ss');
      await onFinish(values)
    } catch (err: any) {
      const names = err?.errorFields?.[0]?.name;
      if (!names) { return };
      form.scrollToField(names, {
        behavior: 'smooth',
        block: 'center'
      })
    }

  };


  const applicantEditHandlerStrategy = {
    [CONTRACT_TYPE_ENUM.DRAFT]: (form: FormInstance, onFinish: any, loading: boolean) => {
      return (
        <Space>
          <OperateAccess.Update>
            <Button type='primary' loading={loading} disabled={tableProps.submitDisabled} onClick={() => {
              onFinish()
            }}>Save as draft
            </Button>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to Submit the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                const values: Recordable = {
                  is_auto_sync_client: 1
                };
                values.create_type = CONTRACT_CREATE_TYPE.TO_AUDIT;
                const contractType = form.getFieldValue('contract_type');
                const notNeedAsset = notNeedAssetAudit(contractType); // 如果为true，则不需要资产组审核
                values.status = notNeedAsset ? CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL : CONTRACT_TYPE_ENUM.ASSET_APPROVAL
                if (!notNeedAsset) {
                  values.asset_team_audit_required = 1;
                }
                values.submission_time = moment().format('YYYY-MM-DD HH:mm:ss');
                await onFinish(values);
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled}>Submit for approval</Button>
            </PopConfirmBtn>

          </OperateAccess.Update>
        </Space>
      )
    },
    [CONTRACT_TYPE_ENUM.ASSET_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      return (
        <OperateAccess.Update>
          <PopConfirmBtn
            content="Confirm to withdraw the contract approval form?"
            onConfirm={async () => {
              await withdrawHandler(form, onFinish)
            }}
          >
            <Button danger>withdraw</Button>
          </PopConfirmBtn>
        </OperateAccess.Update>
      )
    },
    [CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      if (curEditItem.contract_type !== '2') {
        // 只有大媒体可以撤回
        return null;
      }
      return (
        <OperateAccess.Update>
          <PopConfirmBtn
            content="Confirm to withdraw the contract approval form?"
            onConfirm={async () => {
              await withdrawHandler(form, onFinish)
            }}
          >
            <Button danger>withdraw</Button>
          </PopConfirmBtn>
        </OperateAccess.Update>
      )
    },

    [CONTRACT_TYPE_ENUM.OA_PENDING]: (form: FormInstance, onFinish: any, loading: boolean) => {
      return (
        <Space>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="This operation will return the contract application to draft status and requires re-approval"
              onConfirm={async () => {
                await withdrawHandler(form, onFinish)
              }}
            >
              <Button danger >Return to draft and re-edit</Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm application for DingTalk contract approval ？"
              onConfirm={async () => {
                const values: Recordable = {}
                // 发起钉钉工单，状态为钉钉审核中
                values.status = CONTRACT_TYPE_ENUM.OA_APPROVAL;
                await onFinish(values)
              }}
            >
              <Button type='primary'>Apply for DingTalk approval</Button>

            </PopConfirmBtn>
          </OperateAccess.Update>
        </Space>
      )
    },

    [CONTRACT_TYPE_ENUM.OA_REJECTED]: (form: FormInstance, onFinish: any, loading: boolean) => {
      return (
        <>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to submit the contract?"
              onConfirm={async () => {
                const values: Recordable = {};
                values.create_type = CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT;
                const contractType = form.getFieldValue('contract_type');
                const notNeedAsset = notNeedAssetAudit(contractType)
                values.status = notNeedAsset ? CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL : CONTRACT_TYPE_ENUM.ASSET_APPROVAL
                if (!notNeedAsset) {
                  values.asset_team_audit_required = 1;
                }
                values.submission_time = moment().format('YYYY-MM-DD HH:mm:ss');
                await onFinish(values);
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled}>Resubmit</Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <Button type='primary' loading={loading} disabled={tableProps.submitDisabled} onClick={() => {
              const values: Recordable = {};
              values.status = CONTRACT_TYPE_ENUM.DRAFT; // 回到草稿状态
              onFinish(values)
            }}>Save as draft
            </Button>
          </OperateAccess.Update>
        </>
      )
    },
    [CONTRACT_TYPE_ENUM.APPROVAL_REJECTED]: (form: FormInstance, onFinish: any, loading: boolean) => {
      return (
        <>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to submit the contract?"
              onConfirm={async () => {
                const values: Recordable = {};
                values.create_type = CONTRACT_CREATE_TYPE.RESUBMIT_TO_AUDIT;
                const contractType = form.getFieldValue('contract_type');
                const notNeedAsset = notNeedAssetAudit(contractType)
                values.status = notNeedAsset ? CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL : CONTRACT_TYPE_ENUM.ASSET_APPROVAL
                if (!notNeedAsset) {
                  values.asset_team_audit_required = 1;
                }
                values.submission_time = moment().format('YYYY-MM-DD HH:mm:ss');
                await onFinish(values);
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled}>Resubmit</Button>
            </PopConfirmBtn>
          </OperateAccess.Update>

          <OperateAccess.Update>
            <Button type='primary' loading={loading} disabled={tableProps.submitDisabled} onClick={() => {
              const values: Recordable = {};
              values.status = CONTRACT_TYPE_ENUM.DRAFT; // 回到草稿状态
              onFinish(values)
            }}>Save as draft
            </Button>
          </OperateAccess.Update>
        </>


      )
    }
  };
  const userHasAuditRight = (curUserInfo.crmAuditType || []).includes(contractType);
  const isTransmittor = Object.values(curEditItem.transmittor_info || {}).some((it: Recordable) => !it.status && it.transmittor === curUserInfo.id)

  const approverEditHandlerStrategy = {
    [CONTRACT_TYPE_ENUM.ASSET_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      if (!(userIsAssetGroup(curUserInfo) && userHasAuditRight) && !isTransmittor) {
        return null;
      }
      return (
        <Space>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to approve the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                const values: Recordable = {}
                values.status = CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL;
                values.create_type = CONTRACT_CREATE_TYPE.ASSET_SUBMIT

                await onFinish(values)
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled} >
                Accept
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to reject the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                await rejectedHandler(form, CONTRACT_CREATE_TYPE.ASSET_REJECT, onFinish)
              }}
            >
              <Button danger disabled={tableProps.submitDisabled} >
                Reject
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
        </Space>
      )
    },
    [CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      if (!(userIsFinance(curUserInfo) && userHasAuditRight) && !isTransmittor) {
        return null;
      }
      return (
        <Space>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to approve the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                const values: Recordable = {}
                values.status = CONTRACT_TYPE_ENUM.TAX_APPROVAL;
                values.create_type = CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT

                await onFinish(values)
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled} >
                Accept
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              disabled={tableProps.submitDisabled}
              content="Confirm to reject the contract approval form?"
              onConfirm={async () => {
                await rejectedHandler(form, CONTRACT_CREATE_TYPE.FINANCIAL_REJECT, onFinish)
              }}
            >
              <Button danger disabled={tableProps.submitDisabled}>
                Reject
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
        </Space>
      )
    },
    [CONTRACT_TYPE_ENUM.TAX_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      if (!(userIsTax(curUserInfo) && userHasAuditRight) && !isTransmittor) {
        return null;
      }
      return (
        <Space>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to approve the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                const values: Recordable = {}
                values.status = CONTRACT_TYPE_ENUM.LEGAL_APPROVAL;
                values.create_type = CONTRACT_CREATE_TYPE.TAX_SUBMIT

                await onFinish(values)
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled} >
                Accept
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to reject the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                await rejectedHandler(form, CONTRACT_CREATE_TYPE.TAX_REJECT, onFinish)
              }}
            >
              <Button danger disabled={tableProps.submitDisabled}>
                Reject
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
        </Space>
      )
    },
    [CONTRACT_TYPE_ENUM.LEGAL_APPROVAL]: (form: FormInstance, onFinish: any, loading: boolean) => {
      if (!(userIsLegal(curUserInfo) && userHasAuditRight) && !isTransmittor) {
        return null;
      }
      return (
        <Space>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to approve the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                const values: Recordable = {}
                values.status = CONTRACT_TYPE_ENUM.OA_PENDING; // 钉钉待审核状态
                values.create_type = CONTRACT_CREATE_TYPE.LEGAL_SUBMIT
                await onFinish(values)
              }}
            >
              <Button type='primary' disabled={tableProps.submitDisabled} >
                Accept
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
          <OperateAccess.Update>
            <PopConfirmBtn
              content="Confirm to reject the contract approval form?"
              disabled={tableProps.submitDisabled}
              onConfirm={async () => {
                await rejectedHandler(form, CONTRACT_CREATE_TYPE.LEGAL_REJECT, onFinish)
              }}
            >
              <Button danger disabled={tableProps.submitDisabled}>
                Reject
              </Button>
            </PopConfirmBtn>
          </OperateAccess.Update>
        </Space>
      )
    }
  };

  return {
    approverEditHandlerStrategy,
    applicantEditHandlerStrategy
  }
}


export const beforeEditHandler = (record: Recordable) => {
  const toMomentDate = ['effective_date', 'expiry_date', 'submission_time', 'complete_time'];
  toMomentDate.forEach(key => {
    if (record[key]) {
      record[key] = moment(record[key])
    }
  });
  if (record.crm_clients) {
    record.customers = record.crm_clients.map(item => item.id)
  }
  if (record.attachment) {
    record.attachment = record.attachment.map(it => ({
      ...it,
      status: 'done'
    }))
  }
  if (record.template) {
    record.template = record.template.split(',')
  }
  record.contract_attachment = 'previous'
}
