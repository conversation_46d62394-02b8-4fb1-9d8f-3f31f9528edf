import useSelector from "@/hooks/useSelector";
import request from "@/modules/request";
import { createDefaultContractType } from "@/page/clueManage/hook/contact";
import { useCreateClueAndCustomer } from "@/page/customerManage/hook/useCreateClueAndCustomer";
import useUserStore from "@/store/user.store";
import { userIsBD } from "@@/lib/constant";
import { Button, Col, Form, FormInstance, message, Row, Select, Space } from "antd";
import moment from "moment";
import { useEffect, useRef, useState } from "react";


export const usePrevCreateState = (beforeCreateRef: Recordable, customers: ICommonRecord[], setPageItemData: (data: Recordable) => void) => {

  const [curSelectCustomers, setCurSelectCustomers] = useState<ICommonRecord[]>([]);
  const curUserInfo = useUserStore(state => state);
  const createTypeRef = useRef<any>(null);

  const userId = useUserStore(state => state.id)

  const [templateId, setTemplateId] = useState<number | undefined>(undefined);
  const [createType, setCreateType] = useState({
    visible: false,
    itemData: {}
  });

  const customerChange = async (vals: any, form: FormInstance) => {
    const result = vals.map(id => customers.find(it => it.value === id)!).filter(it => it);


    if (vals.length === 1) {
      setTemplateId(vals[0])
    } else if (!vals.includes(templateId) || vals.length > 1) {
      const { data: customerDetails } = await request.get('/getCustomers', { customer_ids: vals.join(',') })
      const findFirstInfoId = vals.find(id => customerDetails.find(it => it.id === id).our_company_name)
      if (findFirstInfoId) {
        setTemplateId(findFirstInfoId)
      } else {
        setTemplateId(undefined)
      }
    }
    setCurSelectCustomers(result);
  }

  // 以下是创建客户需要的字段
  const { customerLeadColumn, customerLeadModal, setCustomerLeadModal, handleCreateClueAndCustomer } = useCreateClueAndCustomer({
    onSuccess: async (result) => {
      const form = createTypeRef.current.getForm();
      const currentCustomers = form.getFieldValue('customers') || [];
      const newCustomerIds = [...currentCustomers, result.id]
      form.setFieldValue('customers', newCustomerIds)
    },
    type: 'contract'
  })

  const columns: Recordable[] = [
    {
      key: 'customers',
      dataIndex: 'customers',
      title: "Associated customers",
      hidden: true,
      required: true,
      create: {
        type: 'Component',
        pureComponent: true,
        render(form) {
          return (

            <Form.Item labelCol={{ span: 7 }} required label="Associated customers">
              <Row>
                <Col span={17}>
                  <Form.Item name="customers" rules={[{ required: true, message: 'Please select associated customers' }]}>
                    <Select
                      options={customers}
                      mode="multiple"
                      allowClear
                      showSearch
                      placeholder="Please select associated customers"
                      optionFilterProp="label"
                      showArrow
                      dropdownMatchSelectWidth={400}
                      onChange={async (vals) => {
                        customerChange(vals, form)
                      }}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={7} style={{ textAlign: 'right' }}>
                  <Button type="link" onClick={() => {
                    if (!userIsBD(curUserInfo)) {
                      message.warn('Only BD can create customers')
                      return;
                    }
                    setCustomerLeadModal({
                      visible: true,
                      itemData: {
                        contact_type: createDefaultContractType(),
                        client_status: 1,
                        bd_id: String(curUserInfo.id),
                        lead_type: 'create_new_lead'
                      }
                    })
                  }}>
                    Create Customer
                  </Button>
                </Col>
              </Row>
            </Form.Item>
          )
        },
        span: 24
      }
    },
    // {
    //   key: 'customer_template',
    //   dataIndex: 'customer_template',
    //   title: 'Contract Template',
    //   hidden: true,
    //   required: true,
    //   create: {
    //     type: 'Select',
    //     options: curSelectCustomers,
    //     span: 24,
    //     required: true,
    //     labelCol: {
    //       span: 7
    //     },
    //   }
    // }
  ];

  const handleCreate = async (values: Recordable) => {
    if (beforeCreateRef?.current) {
      const customerDetailRes = await request.get(`/customer/${templateId}`)
      const { crm_clues, ...detailData } = customerDetailRes.data
      setCreateType({
        itemData: {},
        visible: false,
      })
      const toMomentDate = ['effective_date', 'expiry_date'];
      toMomentDate.forEach(key => {
        if (detailData[key]) {
          detailData[key] = moment(detailData[key])
        }
      })
      if (!detailData.bu) {
        detailData.bu = `Flat Ads`
      }
      if (!detailData.contract_type) {
        if (['Affiliate', 'Adx'].includes(detailData.bus_line)) {
          detailData.contract_type = '1'
        } else if (detailData.coop_bus === 'Other,Mediabuy') {
          detailData.contract_type = '2'
        }
      }
      if (detailData.template) {
        detailData.template = detailData.template.split(',')
      }
      beforeCreateRef.current.beforeCreateFunc({
        ...(detailData || {}),
        customers: values.customers, // 组件默认值
        apply_id: userId,
        is_auto_sync_client: 1
      });
      setPageItemData({
        pay_type: detailData.pay_type
      })
      return
    };
    setCreateType({
      visible: false,
      itemData: {}
    })
  }

  useEffect(() => {
    if (!createTypeRef.current?.getForm) { return };
    const form = createTypeRef.current.getForm();
    if (!form) return;
    const currentCustomers = form.getFieldValue('customers') || [];
    const newCustomerIds = [...currentCustomers]
    customerChange(newCustomerIds, form)
  }, [customers])



  return {
    columns,
    createType,
    setCreateType,
    handleCreate,

    customerLeadColumn,
    customerLeadModal,
    setCustomerLeadModal,
    handleCreateClueAndCustomer,
    createTypeRef
  }
};

export const beforeSubmitHandler = (values) => {
  if (values.attachment) {
    values.attachment = values.attachment.map(it => ({
      url: it.url,
      dd_file_id: '',
      dd_file_type: '',
      name: it.name,
      size: it.size
    }));
  }
  if (values.template && Array.isArray(values.template)) {
    values.template = values.template.join(',')
  }
};
