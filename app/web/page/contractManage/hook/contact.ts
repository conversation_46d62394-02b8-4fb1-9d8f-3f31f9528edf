import { CONTRACT_TYPE_ENUM, NodeType, userIsAssetGroup, userIsFinance, userIsLegal, userIsTax } from "@@/lib/constant"

export interface ICommentParams {
  arraignment_id?: number
  contract_id: number
  tag?: string
  message: string
  type?: NodeType
  attachment?: string
}
/**
 * 是否不需要资产组审核，看合同类型
 * 条件1: 支付中台业务合同
 */
export const notNeedAssetAudit = (contractType: string) => {
  return ['5', '2'].includes(contractType);
}



export const getIsApproveProcess = ({
  statusMap,
  curUserInfo,
  contractType,
  record = {} as Recordable
}) => {
  if (Object.values(record.transmittor_info || {}).some((it: Recordable) => !it.status && it.transmittor === curUserInfo.id)) {
    return true
  }
  if (!curUserInfo.crmAuditType.includes(contractType)) {
    return false
  }
  return (statusMap.IS_ASSET_APPROVAL && userIsAssetGroup(curUserInfo))
    || (statusMap.IS_FINANCIAL_APPROVAL && userIsFinance(curUserInfo))
    || (statusMap.IS_TAX_APPROVAL && userIsTax(curUserInfo))
    || (statusMap.IS_LEGAL_APPROVAL && userIsLegal(curUserInfo))
}


export const getIsAuditUser = (curUserInfo) => {
  return userIsAssetGroup(curUserInfo) || userIsFinance(curUserInfo) || userIsTax(curUserInfo) || userIsLegal(curUserInfo)
}


export const getStatusMap = (status: string) => {
  return {
    IS_DRAFT: status === CONTRACT_TYPE_ENUM.DRAFT,
    IS_ASSET_APPROVAL: status === CONTRACT_TYPE_ENUM.ASSET_APPROVAL,
    IS_FINANCIAL_APPROVAL: status === CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL,
    IS_TAX_APPROVAL: status === CONTRACT_TYPE_ENUM.TAX_APPROVAL,
    IS_LEGAL_APPROVAL: status === CONTRACT_TYPE_ENUM.LEGAL_APPROVAL,
    IS_OA_PENDING: status === CONTRACT_TYPE_ENUM.OA_PENDING,
    IS_OA_PENDING_APPROVAL: status === CONTRACT_TYPE_ENUM.OA_APPROVAL,
    IS_OA_REJECTED: status === CONTRACT_TYPE_ENUM.OA_REJECTED,
    IS_OA_APPROVED: status === CONTRACT_TYPE_ENUM.DUAL_SIGNATURE_REVIEW,
    IS_COMPLETED: status === CONTRACT_TYPE_ENUM.COMPLETED,
    IS_APPROVAL_REJECTED: status === CONTRACT_TYPE_ENUM.APPROVAL_REJECTED,
  }
}

export const CONTRACT_OUR_COMPANY_NAME = [
  { label: 'FLAT MEDIA GLOBAL PTE. LTD', value: 'Flat Media Global Pte. Ltd.' },
  { label: 'Flat Ads Limited', value: 'Flat Ads Limited' },
  { label: 'AcmeMobi Technology Co., Limited', value: 'AcmeMobi Technology Co., Limited' },
  { label: 'PT. Flatmedia Indonesia', value: 'PT. Flatmedia Indonesia' },
  { label: '广州启亮信息科技有限公司', value: '广州启亮信息科技有限公司' },
  { label: 'FLAT MEDIA HONG KONG LIMITED', value: 'FLAT MEDIA HONG KONG LIMITED' },
  { label: 'New Insight China Investment Company Limited', value: 'New Insight China Investment Company Limited' }
]



