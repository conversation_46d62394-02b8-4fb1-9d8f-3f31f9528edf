import { Form, message, Modal, Select, Space, Tooltip } from 'antd'
import React, { useState } from 'react'
import './SharedModal.scss'
import useAppStore from '@/store/app.store';
import useSelector from '@/hooks/useSelector';
import useUserStore from '@/store/user.store';
import request from '@/modules/request';
import { getUniqueArray } from '@@/lib/tool';
import { QuestionCircleOutlined } from '@ant-design/icons';
export default function SharedModal({
  itemData,
  onCancel
}) {
  // Super Admin、 Admin、BD Leader、BD Assistant、Adv BD、Pub BD
  const { SUPER_ADMIN_USERS, ADMIN_USERS, BD_LEADER_USERS, BD_ASSISTANT_USERS, ADV_BD_USERS, PUB_BD_USERS } = useAppStore(useSelector(['SUPER_ADMIN_USERS', 'ADMIN_USERS', 'BD_LEADER_USERS', 'BD_ASSISTANT_USERS', 'ADV_BD_USERS', 'PUB_BD_USERS']));
  const userId = useUserStore(state => state.id)
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm()
  const selectUsers = getUniqueArray([...SUPER_ADMIN_USERS, ...ADMIN_USERS, ...BD_LEADER_USERS, ...BD_ASSISTANT_USERS, ...ADV_BD_USERS, ...PUB_BD_USERS].filter(it => Number(it.value) !== Number(userId)), 'value')
  const handleOk = async () => {
    const result = await form.validateFields()
    try {
      setConfirmLoading(true);

      await request.post('/contract/updateSharedUser', {
        shared_user_ids: result.shared_user_id.join(','),
        contract_id: itemData.id
      })
      message.success('update success')
      onCancel(result.shared_user_id.join(','));
    } finally {
      setConfirmLoading(false);
    }
  }
  return (
    <Modal
      open={true}
      title={(
        <Space>
          <span>Received contracts</span>
          <Tooltip title={
            <div>
              <p>1. Share the contract with other accounts, which can view it in "Received Contracts".</p>
              <p>2. After the DingTalk OA approval is completed, the system will share the contract information with these accounts through DingTalk.</p>
            </div>
          }>
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      )}
      width={600}
      wrapClassName='contract-shared-modal'
      maskStyle={{ zIndex: 1002 }}
      onCancel={() => onCancel()}
      okText="Save"
      confirmLoading={confirmLoading}
      onOk={handleOk}
    >
      <div>
        <Form form={form} initialValues={{ shared_user_id: itemData.shared_user_ids ? itemData.shared_user_ids.split(',') : [] }}>
          <Form.Item label="Account" name="shared_user_id" rules={[{ required: true, message: 'Please select an account' }]}>
            <Select mode='multiple' allowClear showSearch optionFilterProp='label' options={selectUsers} placeholder='Please Select Account' />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  )
}
