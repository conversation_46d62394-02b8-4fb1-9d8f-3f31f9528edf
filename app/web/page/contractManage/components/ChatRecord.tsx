import { Button, Mentions, message, Popconfirm, Space, Spin, Tag, Tooltip } from 'antd'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import './ChatRecord.scss'
import { PaperClipOutlined, CloseOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import useAppStore from '@/store/app.store';
import useSelector from '@/hooks/useSelector';
import UploadInput from '@/component/upload-file';
import moment from 'moment';
import request from '@/modules/request';
import { fileUrlToFileName } from '@@/lib/tool';
import { CONTRACT_TYPE_ENUM, NodeType } from '@@/lib/constant';

const creatorUserType = {
  'asset_submit': 'Finance BP',
  'asset_reject': 'Finance BP',
  'finance_submit': 'Finance',
  'finance_reject': 'Finance',
  'tax_submit': 'Tax',
  'tax_reject': 'Tax',
  'legal_submit': 'Legal',
  'legal_reject': 'Legal',
}

const currentLatestNode = (dataSource: Recordable[]) => {
  const isReject = dataSource.some(it => it.type.includes('reject'));
  if (isReject) {
    return 'reject'
  }
  const isLegalSubmit = dataSource.some(it => it.type.includes('legal_submit'));
  if (isLegalSubmit) {
    return 'legal_submit'
  }
  const isTaxSubmit = dataSource.some(it => it.type.includes('tax_submit'));
  if (isTaxSubmit) {
    return 'tax_submit'
  }
  const isFinanceSubmit = dataSource.some(it => it.type.includes('finance_submit'));
  if (isFinanceSubmit) {
    return 'finance_submit'
  }
  const isAssetSubmit = dataSource.some(it => it.type.includes('asset_submit'));
  if (isAssetSubmit) {
    return 'asset_submit'
  }
  return 'apply_submit'


}

export default function ChatRecord({
  contractId,
  contractType,
  applyId,
  contractStatus
}) {
  const { ACCOUNTS, ASSET_USERS, FIN_USERS, LEGAL_USERS, TAX_USERS, ALL_AUDIT_USERS } = useAppStore(useSelector(['ACCOUNTS', 'ASSET_USERS', 'FIN_USERS', 'LEGAL_USERS', 'TAX_USERS', 'ALL_AUDIT_USERS']));
  const [itemData, setItemData] = useState<Recordable>({});
  const [uploadLoading, setUploadLoading] = useState(false);
  const uploadInfo = useMemo(() => {
    return itemData?.attachment?.[0] || {}
  }, [itemData]);
  const [content, setContent] = useState('');
  const [dataSource, setDataSource] = useState<Recordable[]>([]);
  const [latestNodeType, setLatestNodeType] = useState('apply_submit');
  const [replyInfo, setReplyInfo] = useState<null | Recordable>(null);
  const mentionRef = useRef<any>();
  const [loading, setLoading] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);

  const nodeTypeTextRender = (type: NodeType, record: Recordable) => {
    if ([
      CONTRACT_TYPE_ENUM.COMPLETED
    ].includes(contractStatus)) { return };
    const isUpdateAttachment = record.is_update_attachment;
    let fileName = '';
    if (record.attachment) {
      fileName = record.attachment.split('/').pop();
    }

    // if (latestNodeType === 'apply_submit') {
    if (contractStatus === CONTRACT_TYPE_ENUM.ASSET_APPROVAL) {
      const approvers = type === 'apply_submit_to_asset' ? ASSET_USERS : FIN_USERS;
      return (
        <div style={{ wordBreak: 'break-all' }}>
          {/* <div>"{record.creator}" has submitted the "Contract Approval Form"</div> */}
          <div><span className='chat-record-node-type-text-label'>Next Current stage:</span> Finance {type === 'apply_submit_to_asset' ? 'BP' : ''} Approval</div>
          <div><span className='chat-record-node-type-text-label'>Approvers:</span> {approvers.filter(it => it.aud_type.includes(contractType)).map(it => `"${it.label}"`).join(',')}</div>
        </div>
      )
    }
    // if (latestNodeType === 'asset_submit') {
    if (contractStatus === CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL) {
      let title = ``
      return (
        <div style={{ wordBreak: 'break-all' }}>
          <div>{title}</div>
          <div><span className='chat-record-node-type-text-label'>Next Current stage:</span> Finance approval</div>
          <div><span className='chat-record-node-type-text-label'>Approvers: </span>{FIN_USERS.filter(it => it.aud_type.includes(contractType)).map(it => `"${it.label}"`).join(',')}</div>
        </div>
      )
    }
    // if (latestNodeType === 'finance_submit') {
    if (contractStatus === CONTRACT_TYPE_ENUM.TAX_APPROVAL) {
      let title = ``
      return (
        <div style={{ wordBreak: 'break-all' }}>
          <div>{title}</div>
          <div><span className='chat-record-node-type-text-label'>Next Current stage:</span> Tax Approval</div>
          <div><span className='chat-record-node-type-text-label'>Approvers: </span>{TAX_USERS.filter(it => it.aud_type.includes(contractType)).map(it => `"${it.label}"`).join(',')}</div>
        </div>
      )
    }
    // if (latestNodeType === 'tax_submit') {
    if (contractStatus === CONTRACT_TYPE_ENUM.LEGAL_APPROVAL) {
      let title = ``
      return (
        <div style={{ wordBreak: 'break-all' }}>
          <div>{title}</div>
          <div><span className='chat-record-node-type-text-label'>Next Current stage:</span> Legal Approval</div>
          <div><span className='chat-record-node-type-text-label'>Approvers: </span>{LEGAL_USERS.filter(it => it.aud_type.includes(contractType)).map(it => `"${it.label}"`).join(',')}</div>
        </div>
      )
    }
    // if (latestNodeType === 'legal_submit') {
    if (contractStatus === CONTRACT_TYPE_ENUM.OA_PENDING || latestNodeType === 'legal_submit') {
      let title = ``
      return (
        <div>
          <div>{title}</div>
          <div><span className='chat-record-node-type-text-label'>Next Current stage:</span> DingTalk OA</div>
          <div><span className='chat-record-node-type-text-label'>Approvers: </span>"Team leader", "linxiao","Lily"</div>
        </div>
      )
    }

    return null;
  }

  const sendComment = async () => {
    if (!content) {
      message.warn('Please input content');
      return;
    }
    const apiParams = {
      contract_id: contractId,
      message: content,
      arraignment_id: replyInfo?.id || null,
      attachment_new: Object.keys(uploadInfo).length ? [uploadInfo] : null
    };
    setSendLoading(true);
    try {
      const res = await request.post('/contractChat', apiParams);
      message.success(res.msg);
      setChatRecordDataSource();

      setContent('');
      setReplyInfo(null);
      setItemData({});
    } finally {
      setSendLoading(false);
    }

  }
  const deleteComment = async (id: number) => {
    const res = await request.delete(`/contractChat/${id}`);
    message.success(res.msg);
    setChatRecordDataSource();
  }
  const renderMessage = (message: string = '') => {
    if (!message) { return '' };
    const lines = message.split('\n');
    return lines.map((line, index) => <div key={index}>{line}</div>);
  }

  const setChatRecordDataSource = async () => {
    setLoading(true);
    try {
      const res = await request.get('/contractChat', { contract_id: contractId });
      setDataSource(res.data)
    } finally {
      setLoading(false);
    }
  }
  const handlerReply = (it: Recordable) => {
    setReplyInfo(it);
    mentionRef.current.focus();
  }

  useEffect(() => {
    if (contractId) {
      setChatRecordDataSource();
    } else {
      setDataSource([])
    }
  }, [contractId]);

  useEffect(() => {
    const latestNode = currentLatestNode(dataSource);
    setLatestNodeType(latestNode)
  }, [dataSource]);
  const findLastTagId = dataSource.findLast(it => it.tag)?.id;

  useEffect(() => {
    // 加载完成后，滚动到底部
    const chatRecordContainer = document.querySelector('.chat-record-container');
    if (chatRecordContainer) {
      chatRecordContainer.scrollTo({
        top: chatRecordContainer.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [dataSource])
  return (
    <Spin spinning={loading} wrapperClassName='chat-record-spin'>
      <div className='chat-record'>
        <div className='chat-record-container' >
          {
            dataSource.map((it, index) => {
              let tagColor = 'success';
              if (it.tag === 'Pending') {
                tagColor = 'processing';
              } else if (it.tag === 'Rejected' || it.tag === 'OA-Rejected' || it.tag === 'OA-Revocation') {
                tagColor = 'error';
              } else if (it.tag === 'Transferred') {
                tagColor = 'red'
              } else if (it.tag === 'Resubmit') {
                tagColor = 'lime'
              }
              const auditType = creatorUserType[it.type] || '';
              return (
                <div className='chat-container-item' key={it.id}>
                  <div className='chat-record-title'>
                    <div style={{ display: 'flex' }}>
                      <strong>{it.creator}</strong>
                      {
                        it.tag && (
                          <Tag color={tagColor}>{it.tag}</Tag>
                        )
                      }
                    </div>
                    <Space>
                      <div className='chat-record-reply-btn' onClick={() => {
                        handlerReply(it)
                      }}>Reply</div>
                      {
                        it.type === 'reply' && (
                          <Popconfirm title="Are you sure to delete this record?" onConfirm={() => {
                            return deleteComment(it.id)
                          }}>
                            <div className='chat-record-remove-btn'>
                              <DeleteOutlined />
                            </div>
                          </Popconfirm>
                        )
                      }
                    </Space>

                  </div>
                  <div className='chat-record-time'>{auditType ? `${auditType}, ` : ''} {moment(it.ctime).format('YYYY-MM-DD HH:mm')}</div>

                  <div style={{ wordBreak: 'break-word' }}>
                    {renderMessage(it.message)}
                  </div>
                  {
                    it.attachment_new ? (
                      it.attachment_new.map(it => {
                        return (
                          <div className='chat-record-file pl-8'>
                            <PaperClipOutlined />
                            <a style={{ wordBreak: 'break-word' }} href={it.url} target="_blank">{it.name}</a>
                          </div>
                        )
                      })
                    ) : (
                      it.attachment && (
                        <div className='chat-record-file pl-8'>
                          <PaperClipOutlined />
                          <a style={{ wordBreak: 'break-word' }} href={it.attachment} target="_blank">{fileUrlToFileName(it.attachment)}</a>
                        </div>
                      )
                    )
                  }
                  {
                    it.relatedChatRecord && (
                      <div className='chat-record-reply-content'>
                        <div>
                          <strong>{it.relatedChatRecord.creator}:</strong>
                          <div style={{ wordBreak: 'break-word' }}>
                            {renderMessage(it.relatedChatRecord.message)}
                          </div>
                          {
                            it.relatedChatRecord.attachment_new ? (
                              it.relatedChatRecord.attachment_new.map(it => {
                                return (
                                  <div className='chat-record-file'>
                                    <PaperClipOutlined />
                                    <a style={{ wordBreak: 'break-word' }} href={it.url} target="_blank">{it.name}</a>
                                  </div>
                                )
                              })
                            ) : (
                              it.relatedChatRecord.attachment && (
                                <div className='chat-record-file'>
                                  <PaperClipOutlined />
                                  <a style={{ wordBreak: 'break-word' }} href={it.relatedChatRecord.attachment} target="_blank">{fileUrlToFileName(it.relatedChatRecord.attachment)}</a>
                                </div>
                              )
                            )

                          }
                        </div>
                      </div>
                    )
                  }

                  {
                    it.id === findLastTagId && (
                      <div className='chat-record-tips-text'>
                        {nodeTypeTextRender(it.type, it)}
                      </div>
                    )
                  }

                </div>
              )
            })
          }
        </div>
        <div className='chat-record-mention'>
          {
            replyInfo && (
              <div className='chat-record-reply-container'>
                <div className='chat-record-reply-container-message'>
                  <strong>{replyInfo.creator}: </strong>
                  {replyInfo.message}
                </div>
                <CloseOutlined onClick={() => setReplyInfo(null)} />
              </div>
            )
          }
          <Mentions
            rows={6}
            autoSize={true}
            options={[...ACCOUNTS.filter(it => it.value === applyId), ...ALL_AUDIT_USERS].map(it => ({
              label: it.label,
              value: it.label
            }))}
            value={content}
            onChange={(value) => {
              setContent(value);
            }}
            placeholder='Please input content'
            ref={mentionRef}
          >

          </Mentions>
          <div className='chat-record-mention-btn'>
            <Space>
              <Tooltip title={`The attachments in the comment area are only used for communication. If you need to add them to the contract attachments, please use "Withdraw" or notify the approver (Finance/Legal/Tax) to upload them.`}>
                <QuestionCircleOutlined />
              </Tooltip>
              <UploadInput
                accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg"
                label="attachment"
                itemData={itemData}
                maxCount={1}
                setItemData={setItemData}
                showUploadList={false}
                isLargeMode={false}
                loading={uploadLoading}
                onUploadPercent={(percent) => {
                  const isComplete = percent === 100;
                  if (isComplete) {
                    // loading 不要瞬间结束，不然图片太小，会闪一下loading
                    setUploadLoading(false);

                    return;
                  };
                  setUploadLoading(true);
                }}
              >
                <Button loading={uploadLoading} icon={<PaperClipOutlined />}></Button>

              </UploadInput>
              <Button loading={sendLoading} onClick={sendComment}>Send</Button>
            </Space>
          </div>
          {
            uploadInfo.url && (
              <div className='chat-record-file pl-8'>
                <PaperClipOutlined />
                <a style={{ wordBreak: 'break-word' }} href={uploadInfo.url} target="_blank">{uploadInfo.name}</a>
              </div>
            )
          }

        </div>
      </div>
    </Spin>

  )
}
