import { Tabs } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import '../../../router/Notifications.scss'
import './ContractTabs.scss'
import { CONTRACT_DATA_TYPE, userIsAssetGroup, userIsFinance, userIsLegal, userIsTax } from '@@/lib/constant'
import useUserStore from '@/store/user.store'
import request from '@/modules/request'
import { getIsAuditUser } from '../hook/contact'
import useNotificationStore, { getContractPendingCount } from '@/store/notification.store'
import useSelector from '@/hooks/useSelector'

interface IProps {
  onChange: (value: string) => void
}

function ContractTabs({
  onChange,
  defaultActiveKey = CONTRACT_DATA_TYPE.PENDING,
}, ref) {
  const curUserInfo = useUserStore(state => state);
  const { contractPendingCount, setContractPendingCount } = useNotificationStore(useSelector(['contractPendingCount', 'setContractPendingCount']));

  const [currenTab, setCurrenTab] = useState(defaultActiveKey);

  const [processCount, setSetProcessCount] = useState(0);

  const items = [
    // {
    //   label: (
    //     <div>
    //       All contracts
    //     </div>
    //   ),
    //   key: CONTRACT_DATA_TYPE.ALL,
    // },
    {
      label: (
        <div>
          Pending contracts
          <span className='notifications-tab-tag red'>
            {contractPendingCount}
          </span>
        </div>
      ),
      key: CONTRACT_DATA_TYPE.PENDING,
    },
    {
      label: (
        <div>
          Processed contracts
          <span className='notifications-tab-tag archived'>
            {processCount}
          </span>
        </div>
      ),
      key: CONTRACT_DATA_TYPE.PROCESSED,
    }

  ];

  const fetchPendingCountHandler = async () => {
    const isAuditUser = getIsAuditUser(curUserInfo)
    if (!isAuditUser) { return };
    const processData = await request.get('/contract/getContractCount', { dataType: CONTRACT_DATA_TYPE.PROCESSED })
    getContractPendingCount().then((res) => {
      setContractPendingCount(res)
    })


    setSetProcessCount(processData.data?.total || 0);

  }

  useImperativeHandle(ref, () => ({
    fetchPendingCountHandler
  }))

  useEffect(() => {
    fetchPendingCountHandler();
  }, [])

  return (

    <div className='notifications-container contract-tabs-container'>
      <Tabs
        className='notifications-tabs'
        defaultActiveKey='unread'
        activeKey={currenTab}
        items={items}
        onChange={(e: any) => {
          setCurrenTab(e);
          onChange(e);
        }}
      />
    </div>

  )
}

export default forwardRef(ContractTabs)
