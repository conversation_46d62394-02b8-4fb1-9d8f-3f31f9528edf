.chat-record-spin {

  height: 100%;

  .ant-spin-container {
    height: 100%;
  }
}

.chat-record {
  display: flex;
  flex-direction: column;
  height: 100%;

  .chat-record-node-type-text-label {
    display: inline-block;
    font-weight: bold;
  }

  .chat-record-file {
    display: flex;
    align-items: center;
    // background-color: #f5f5f5;
    background-color: #F4F6F8;
    border-radius: 6px;
    margin-top: 4px;
    padding: 4px 8px 4px 0;
    font-size: 13px;

    &.pl-8 {
      padding-left: 8px;
    }

    .anticon {
      color: rgba(0, 0, 0, .45);
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .chat-record-mention {
    padding: 12px 16px;
    background-color: rgba(145 158 171 / 0.06);
    border-radius: 6px;

    .ant-mentions {
      min-height: 60px;

      textarea {
        min-height: 60px;
      }
    }

    .chat-record-mention-btn {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 4px;
    }
  }


  .chat-record-reply-container {
    padding: 12px;
    margin-top: 4px;
    margin-bottom: 4px;
    border-radius: 12px;
    color: #637381;
    background-color: #F4F6F8;
    display: flex;
    align-items: center;

    .chat-record-reply-container-message {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .chat-record-tips-text {
    margin-top: 20px;
    color: #6a6e71;
    font-size: 12px;
  }
}

.chat-record-container {
  color: #212b36;
  font-size: 14px;
  flex: 1;
  height: 100%;
  overflow-y: scroll;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;


  .chat-container-item {
    transition: background-color .15s cubic-bezier(.4, 0, .2, 1) 0ms;
    border-bottom: 1px dashed rgba(145, 158, 171, .2);
    padding: 12px 16px;
    border-radius: 6px;

    &:hover {
      background-color: rgba(145, 158, 171, .03);
      text-decoration: none;
    }

    .chat-record-reply-btn {
      color: #00B8D9;
      text-decoration: underline;
      font-size: 12px;
      cursor: pointer;
    }

    .chat-record-remove-btn {
      color: #ff4d4f;
      cursor: pointer;
      text-decoration: underline;
      font-size: 12px;
    }

    .chat-record-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .ant-tag {
        height: 22px;
        line-height: 22px;
        margin-left: 4px;
      }
    }

    .chat-record-time {
      color: #919eab;
      font-size: 12px;
    }



    .chat-record-reply-content {
      padding: 12px;
      margin-top: 4px;
      margin-bottom: 4px;
      border-radius: 12px;
      color: #637381;
      background-color: #F4F6F8;
    }
  }
}
