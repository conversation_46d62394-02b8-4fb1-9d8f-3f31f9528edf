import { CreateModal } from '@/component';
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import useAppStore from '@/store/app.store';
import useUserStore from '@/store/user.store';
import { message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  itemData: Record<string, any>
}

export default function (props: IProps) {
  const { ALL_AUDIT_USERS } = useAppStore(useSelector(['ENUMS', 'ALL_AUDIT_USERS']));
  const curUserInfo = useUserStore(state => state);
  const formRef = useRef<any>();
  const columns = useFlatTableColumns([
    {
      key: 'new_approver',
      hidden: true,
      create: {
        type: 'Select',
        index: 1,
        span: 23,
        labelCol: { span: 6 },
        options: ALL_AUDIT_USERS.filter(it => it.value !== curUserInfo.id),
        formItemProps: {
          required: true,
        }
      },
    },


  ], {
    Fields: {
      new_approver: 'New Approver',
    }
  })

  const handleCreate = async (options) => {
    const res = await request.post('/contract/contractTransfer', {
      ids: props.itemData.map(it => it.id),
      new_approver: options.new_approver,
    });
    if (res.isSuccess) {
      message.success('transfer success')
      props.setVisible(true)
    } else {
      message.error(res.msg)
    }

  }
  return (
    <CreateModal
      title={`Contract Transfer (Count: ${props.itemData?.length})`}
      columns={columns}
      ref={formRef}
      visible={props.visible}
      itemData={props.itemData}
      setVisible={(visible) => {
        props.setVisible(visible)
      }}
      onChange={handleCreate}
      width={600}
      style={{ top: '10px' }}
    />
  )
}
