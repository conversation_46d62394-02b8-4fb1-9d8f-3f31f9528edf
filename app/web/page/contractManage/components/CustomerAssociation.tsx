import { CreateModal } from '@/component'
import React, { useRef } from 'react'
import request from '@/modules/request';
import { message } from 'antd';

export default function CustomerAssociation({
  visible,
  onClose,
  customers,
  id
}) {
  const columns: Recordable[] = [
    {
      key: 'customers',
      dataIndex: 'customers',
      title: "Custormer",
      hidden: true,
      required: true,
      create: {
        type: 'Select',
        mode: 'multiple',
        options: customers,
        required: true,
        span: 24
      }
    }
  ];

  const onChange = async (values: Recordable) => {
    try {
      const res = await request.post(`/contract/updateRelevanceClients`, {
        ...values,
        id
      });
      if (res.isSuccess) {
        message.success('update success')
        onClose(true);
        return;
      }
      message.error(res.msg)
    } catch { }
  }

  return (
    <CreateModal
      columns={columns}
      title="Customer Association"
      visible={visible}
      width={660}
      style={{ top: '10px' }}
      itemData={{}}
      onChange={onChange}
      setVisible={() => {
        onClose()
      }}
    />
  )
}
