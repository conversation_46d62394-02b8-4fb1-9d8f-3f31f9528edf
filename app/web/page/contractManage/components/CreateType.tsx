import { CreateModal } from '@/component'
import React, { forwardRef, useImperativeHandle, useRef } from 'react'

function CreateType({
  columns,
  visible,
  onChange,
  onClose
}, ref) {
  const createModalRef = useRef<any>(null);
  useImperativeHandle(ref, () => ({
    setForm: newVal => {
      createModalRef.current?.setForm?.(newVal);
    },
    getForm: () => {
      return createModalRef.current?.form
    }
  }))
  return (
    <CreateModal
      columns={columns}
      title="Create Type"
      ref={createModalRef}
      visible={visible}
      width={660}

      style={{ top: '10px' }}
      itemData={{}}
      onChange={onChange}
      setVisible={() => {
        onClose()
      }}
    />
  )
}

export default forwardRef(CreateType)
