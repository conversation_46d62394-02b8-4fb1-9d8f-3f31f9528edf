import React, { useEffect, useRef, useState } from 'react';
import {
  FlatTableActionType,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import BasePageHoc from '@/component/base-page-hoc'
import { beforeSubmitHandler, usePrevCreateState } from '../hook/index'
import CreateType from '../components/CreateType';
import CustomerAssociation from '../components/CustomerAssociation';
import { COMMON_BEFORE_FILTER, CONTRACT_CREATE_TYPE, CONTRACT_DATA_TYPE, CONTRACT_TYPE_ENUM } from '@@/lib/constant';
import CustomerDetail from '@/page/clueManage/components/CustomerDetail';
import ChatRecord from '../components/ChatRecord';
import { getIsApproveProcess, getStatusMap, notNeedAssetAudit } from '../hook/contact'
import { debounce } from '@@/lib/tool';
import { clearUrlSearchEffect } from '@/hooks';
import ContractTabs from '../components/ContractTabs';
import './index.scss'
import { beforeEditHandler, useBasicState, useContractState, useEditHandlerStrategy } from '../hook/useCommonState';
import OperateAccess from '@/component/auth/operate-access';
import { Button, message } from 'antd';
import ApprovalTransfer from '../components/ApprovalTransfer';

let isNeedPageParamsOpen: null | boolean = false;
let searchParamsId: number | string | null = 0;


function ContractApproval() {
  const searchParams = new URLSearchParams(location.search);
  const [contractTransferInfo, setContractTransferInfo] = useState({
    visible: false,
    itemData: {},
  });
  const {
    CUSTOMER,
    isAuditUser,
    dataType,
    curUserInfo,
    setDataType,
    customerAssociation,
    setCustomerAssociation,
    pageItemData,
    setPageItemData,
    tabsRef
  } = useBasicState((searchParams.get('auditType') as CONTRACT_DATA_TYPE) || CONTRACT_DATA_TYPE.PENDING)

  const {
    columns,
    curEditItem,
    curEditItemStatus,
    customerDetail,
    setCustomerDetail,
    isApplicant,
    isCreate,
    setCurEditItem,

  } = useContractState({
    itemData: pageItemData, setItemData: setPageItemData,
    getTableProps: () => tableProps
  }, 'pool')


  const beforeCreateRef = useRef<any>()
  const tableProps = useFlatTable({
    title: 'Contract Approval',
    fetchUrl: '/contract',
    actionWidth: 110,
    pageSize: 20,
    columns,
    defaultParams: {
      id: searchParams.get('id') || undefined
    },
    allowRowSelect: true,
    renderEditText(_, record) {
      const statusMap = getStatusMap(record.status);
      const isApproveProcess = getIsApproveProcess({
        statusMap,
        curUserInfo,
        contractType: record.contract_type,
        record
      })
      return (isApproveProcess && dataType === CONTRACT_DATA_TYPE.PENDING) ? 'Approval' : 'Edit'
    },
    actions: [
      FlatTableActionType.Edit,
    ],
    btnGroup: [
      (selectRows) => (
        dataType === CONTRACT_DATA_TYPE.PENDING && (
          <OperateAccess.Update>
            <Button disabled={!selectRows.length} onClick={() => {
              for (const item of selectRows) {
                if (![CONTRACT_TYPE_ENUM.ASSET_APPROVAL, CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL, CONTRACT_TYPE_ENUM.TAX_APPROVAL, CONTRACT_TYPE_ENUM.LEGAL_APPROVAL].includes(item.status)) {
                  message.warn('Only the status of "Finance BP Approval" and "Finance Approval" and "Tax Approval" and "Legal Approval" can be transferred');
                  return;
                }
              }
              setContractTransferInfo({ visible: true, itemData: selectRows });
            }}>Contract approval transfer</Button>
          </OperateAccess.Update>
        )
      ),
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    beforeCreate() {
      setCurEditItem(null);
      return {}
    },
    async beforeEdit(record) {

      beforeEditHandler(record)
      setPageItemData({
        ...pageItemData,
        pay_type: record.pay_type,
        contract_attachment: 'previous'
      })
      setCurEditItem(record);
      return record;
    },
    isDisabledAction(type, record) {
      if (type === 'delete') {
        return record.status !== CONTRACT_TYPE_ENUM.DRAFT
      }
      return false;
    },
    async beforeHandleCreate(values) {
      if (values.create_type === CONTRACT_CREATE_TYPE.DRAFT) {
        values.status = CONTRACT_TYPE_ENUM.DRAFT // 草稿
      } else {
        const notNeedAsset = notNeedAssetAudit(values.contract_type);

        if (!notNeedAsset) {
          values.asset_team_audit_required = 1;
        }
        values.status = notNeedAsset ? CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL : CONTRACT_TYPE_ENUM.ASSET_APPROVAL
      }
      beforeSubmitHandler(values);
      return {
        isSuc: true,
        values: values
      }
    },
    async beforeHandleUpdate(values) {
      beforeSubmitHandler(values);

      return {
        isSuc: true,
        values
      }
    },
    graspBeforeCreate(beforeCreateFunc) {
      setCreateType({
        visible: true,
        itemData: {}
      })
      beforeCreateRef.current = {
        beforeCreateFunc,
      }
    },

    // filter
    beforeFilter(params) {
      const filters = COMMON_BEFORE_FILTER(params)
      return {
        ...filters,
        dataType
      };
    },

    afterCreate() {

      tabsRef.current?.fetchPendingCountHandler?.()
    },
    afterUpdate() {
      tabsRef.current?.fetchPendingCountHandler?.()
    },

    dataFetchAfter: debounce((res) => {
      const pageId = searchParamsId;
      if (pageId && res?.[0] && !isNeedPageParamsOpen) {
        isNeedPageParamsOpen = true;
        tableProps.onEdit(res[0], 0);
      }
    }, 500),
  });

  const { columns: prevCreateColumns, createType, setCreateType, handleCreate: createTypeHandleCreate } = usePrevCreateState(beforeCreateRef, CUSTOMER, (pageData) => {
    setPageItemData({
      ...pageItemData,
      ...pageData
    })
  })

  const {
    applicantEditHandlerStrategy,
    approverEditHandlerStrategy
  } = useEditHandlerStrategy({
    tableProps,
    curUserInfo,
    contractType: curEditItem?.contract_type,
    curEditItem: curEditItem || {}
  })

  useEffect(() => {

    return () => {
      isNeedPageParamsOpen = null;
      searchParamsId = null;
    }
  }, [])

  clearUrlSearchEffect()

  useEffect(() => {
    const id = searchParams.get('id');
    if (!id) { return };
    const auditType = searchParams.get('auditType');

    isNeedPageParamsOpen = false;
    searchParamsId = id;
    setDataType(auditType as CONTRACT_DATA_TYPE || CONTRACT_DATA_TYPE.PENDING);
    tableProps.setParams({
      ...tableProps.params,
      id
    })
  }, [searchParams.get('id'), searchParams.get('auditType')]);


  return <section className={`approval-list-wrap audit-user`}>
    <ContractTabs ref={tabsRef} defaultActiveKey={dataType} onChange={(e) => {
      setDataType(e);
      if (tableProps.pageIndex !== 1) {
        tableProps.setPageIndex(1);
      } else {
        tableProps.defaultFetchHangler({
          dataType: e,
        })
      }

    }} />
    <FlatTable {...tableProps}
      modalType="drawer"
      modalWidth={1300}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      confirmText="Submit"
      isHiddenConfirm={true} // 编辑禁用掉确认按钮，用customBtnCreate控制
      customBtnCreate={(type, form, onFinish) => {
        if (type === 'create' || dataType === CONTRACT_DATA_TYPE.PROCESSED) {
          return null
        }

        if (type === 'update') {
          if (isApplicant && applicantEditHandlerStrategy[curEditItem?.status]) {
            return applicantEditHandlerStrategy[curEditItem?.status](form, onFinish)
          }

          // 其他都属于审批人
          if (!approverEditHandlerStrategy[curEditItem?.status]) {
            return null;
          }
          return approverEditHandlerStrategy[curEditItem?.status](form, onFinish)
        }

        // 无其他情况
        return null
      }}
      createModalClose={() => {
        setPageItemData({});
      }}
      updateModalClose={() => {
        setPageItemData({});
      }}


      chattingRecordsRender={(isCreate || curEditItemStatus.IS_DRAFT) ? null : (type) => {
        return (
          <ChatRecord contractStatus={curEditItem!.status} contractType={curEditItem!.contract_type} contractId={curEditItem!.id} applyId={curEditItem!.apply_id} />
        )
      }}
    />
    <CreateType
      columns={prevCreateColumns}
      visible={createType.visible}
      onChange={createTypeHandleCreate}
      onClose={(isRefresh) => {
        setCreateType({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <CustomerAssociation
      customers={CUSTOMER}
      visible={customerAssociation.visible}
      id={customerAssociation.id}
      onClose={(isRefresh) => {
        setCustomerAssociation({
          visible: false,
          id: 0
        });
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <CustomerDetail visible={customerDetail.visible} onClose={() => {
      setCustomerDetail({
        visible: false,
        itemData: {}
      })
    }} customerData={customerDetail.itemData} />

    <ApprovalTransfer
      visible={contractTransferInfo.visible}
      itemData={contractTransferInfo.itemData}
      setVisible={(isRefresh) => {
        if (isRefresh) {
          tableProps.defaultFetchHangler();
          tabsRef.current?.fetchPendingCountHandler?.()
        }
        setContractTransferInfo({
          visible: false,
          itemData: {}
        })
      }}
    />
  </section>
}

export default BasePageHoc(ContractApproval)
