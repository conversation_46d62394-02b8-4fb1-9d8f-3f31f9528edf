@import '~@asset/style/base/index.scss';

.page-main-header {
  text-align: right;
  display: flex;
  flex-direction: column;
  padding: 0;

  // align-items: center;
  .header-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 0 20px;
    height: 56px;
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, 0 1px 2px rgb(0, 21, 41, 0.08);
    z-index: 97;
    flex-shrink: 0;
  }

  .header-tabs {
    width: 100%;
    background-color: #fff;
    height: 44px;
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, 0 1px 2px rgb(0, 21, 41, 0.08);
    z-index: 95;
    padding-left: 30px;
    flex-shrink: 0;
    scrollbar-width: none;
    overflow-x: scroll;
    overflow-y: hidden;

    .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before {
      box-shadow: none;
    }

    .ant-tabs {
      height: 100%;
    }

    .ant-tabs-nav {
      margin-bottom: 0;
      height: 100%;

      .ant-tabs-nav-wrap {
        overflow: initial;
      }

      .ant-tabs-tab {
        position: relative;
        background-color: transparent;
        border: none !important;
        border-left: none;
        padding: 6px 0;
        position: relative;

        .ant-tabs-tab-btn {
          color: var(--base-text-color);
        }

        &:not(.ant-tabs-tab-active) {
          &::after {
            position: relative;
            right: 0;
            top: 44%;
            transform: translateY(-50%);
            width: 1px;
            content: "";
            height: 16px;
            background-color: rgb(31, 34, 37, .2);
            z-index: -9999;
          }
        }
      }
    }

    .tabs-detail {
      position: relative;
      top: 5px;
      display: flex;
      align-items: center;
      z-index: 100;

      .tabs-detail-text {
        max-width: 240px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ant-tabs-tab-remove {
      position: relative;
      top: 5px;
      margin-left: 16px;
      margin-right: 12px;
      z-index: 100;

      .anticon-close {
        position: relative;

        &::after {
          content: "";
          opacity: 0;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          transition: opacity .3s var(--n-bezier);

          background-color: #9ca3af;
          width: 16px;
          height: 16px;
          border-radius: 50%;
        }
      }

      .anticon-close:hover {
        &::after {
          opacity: 1;
        }
      }

      .anticon {
        color: var(--base-text-color);
      }
    }

    .ant-tabs-tab:not(.ant-tabs-tab-active) {
      &:hover {
        .tabs-svg-container {

          .svg-item {
            color: #dee1e6;
            opacity: 1;
          }

        }
      }
    }

    .ant-tabs-tab-active {
      .tabs-svg-container {
        color: var(--soy-primary-color);

        .svg-item {
          opacity: 1;
        }

      }

      .anticon-close:hover {
        &::after {
          background-color: rgb(100 108 255 / 10%);
        }
      }

      .anticon {
        color: var(--soy-primary-color);
      }
    }

    .tabs-svg-container {
      margin-left: 12px;
      width: 100%;
      height: 100%;
      color: var(--base-text-color);

      .svg-item {
        opacity: 0;
        left: 50%;
        transform: translateX(-50%) translateY(0);
        width: 115%;
        height: 76%;
        overflow: auto;
        position: absolute;
        z-index: -1;
        color: #edeefe;
        transition: opacity .3s var(--n-bezier), color .3s var(--n-bezier);
        pointer-events: none;

      }



    }
  }

  .collapse-btn {
    cursor: pointer;
    font-size: 16px;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 26px;
  }

  .header-left-wrap {
    display: flex;
    justify-content: flex-start;
    position: relative;
    align-items: center;
    flex-shrink: 0;

    .ant-menu {
      margin-left: 30px;
      font-size: 16px;
      background-color: transparent;
      height: 43px;
      line-height: 44px;
      position: relative;
      top: 1px;
      width: 100%;
      max-width: 800px;
    }

    .ant-menu-item {
      a {
        color: #b8c0cd;
        font-weight: bold;
      }

      &.ant-menu-item-selected {
        a {
          color: #fff;
        }
      }
    }


  }

  .header-right-wrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .history-icon {
      margin-right: 5px;
      cursor: pointer;
    }

    // 切换主题按钮
    .fixed-widgets {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      cursor: pointer;
      margin-right: 5px;

      .ant-avatar {
        background-color: #fff;
        color: #000;
        -webkit-box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        -webkit-transition: color 0.3s;
        transition: color 0.3s;
      }

      .ant-avatar-sm {
        width: 21px;
        height: 21px;
      }

      .anticon {
        position: relative;
        left: 1px;
      }
    }

    .user-notice {
      cursor: pointer;
      margin-right: 5px;
      position: relative;
      height: 18px;
      line-height: 18px;
      top: -2px;

      .notice-bubbles {
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: red;
        top: -5px;
        right: 0px;
      }
    }

    .ant-btn-link {
      // color: #fff;
      height: auto;
    }
  }

  .user-area {
    .anticon-user {
      color: $themeColor;
    }

    .name {
      padding: 0 15px 0 5px;
    }
  }
}

.main-wrap {


  .ant-layout-content {
    position: relative;

    .page-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .trigger {
    font-size: 18px;
    line-height: 64px;
    padding: 0 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .trigger:hover {
    color: #1890ff;
  }
}

.ant-menu-title-content {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.left-menu a {
  display: inline;
  color: white;
  font-size: 16px;
}

.section-item {
  border-radius: 8px;

  &:last-child {
    margin-right: 0;
  }

  .ant-pagination {
    margin: 10px 0 0 0;
  }

  .ant-divider-horizontal {
    width: calc(100% + 16px);
    margin: 12px 0 8px -8px;
  }
}

// fix table columns width 不生效问题（强制不换行）
.table-cell-break-word {
  word-wrap: break-word;
  word-break: break-word;
}

// ... 文字过长缩略显示
.goal-text-overflow {
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}

// 自定义弹窗，通用创建、编辑弹层 样式
.common-edit-pop {
  .ant-modal-body {
    padding: 24px;
  }

  .col-title {
    text-align: right;
    font-weight: bold;

    &.is-required::before {
      content: '*';
      display: inline-block;
      color: red;
      padding-right: 2px;
    }
  }

  .col-title-left {
    text-align: left;
    margin-bottom: 2px;
  }

  .ant-form-item-label {
    line-height: 32px;
    text-align: right;
  }

  .col-input {
    text-align: left;
  }

  .col-select {
    width: 100%;
  }

  .col-date-picker {
    width: 100%;
  }

  .col-input-number {
    width: 100%;
  }

  .row-item {
    &+.row-item {
      margin-top: 15px;
    }

    text-align: right;
  }
}

// 用户抽屉弹窗
.user-info-pop {
  .info-item {
    text-align: left;
    margin: 20px auto;
    font-size: 14px;
  }

  .info-label {
    margin-right: 10px;
  }
}


.card-wrapper {
  border-radius: 8px;
  padding: 19px 24px 20px;
  height: 100%;
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;

}
