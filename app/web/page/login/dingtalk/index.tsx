import React, { useEffect, useState, useContext } from 'react';
import { Spin } from 'antd';
import { AppContext } from '@/store';
import request from 'request';
import './index.scss';

export default function Dingtalk({ history }) {
  const { publicKey, query, localeData } = useContext(AppContext);
  useEffect(() => {
    request
      .post(
        '/passport/local',
        {
          username: 'dingtalk',
          password: query.code,
          corp: query.corp
        },
        // @ts-ignore
        { 'X-showMessage': true }
      )
      .then((res: any) => {
        const searchParams = new URLSearchParams(location.search);
        const from = searchParams.get('from');
        searchParams.delete('from');
        if (from) {
          const search = searchParams.toString();
          location.href = `${from}${search ? `?${search}` : ''}`
          return;
        }
        location.href = '/';
      })
      .catch(err => {
        history.push('/login');
      });
  }, []);
  return (
    <div className="dingtalk-wrap">
      <Spin />
      <span className="tips">{localeData.Loading}...</span>
    </div>
  );
}
