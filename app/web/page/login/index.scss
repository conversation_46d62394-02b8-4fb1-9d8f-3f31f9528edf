.login-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: url('./images/login-bg-1.png') no-repeat 40vw 12vh, url('./images/login-bg-2.png') no-repeat right 50px, url('./images/login-bg-3.png') no-repeat 60vw 90vh, url('./images/login-bg-4.png') no-repeat 20vw bottom, url('./images/login-bg-4.png') no-repeat right 50vh, url('./images/login-bg-2.png') no-repeat 100px 40vh, url('./images/login-bg-3.png') no-repeat 6vw 6vh;
  .login-content {
    display: flex;
    justify-content: flex-start;
    position: relative;
    background-color: #fff;
    width: 960px;
    height: 500px;
    background: #fff;
    box-shadow: 0 0px 30px 0 rgba(59, 72, 89, 0.14);
    border-radius: $borderRadius;
    .login-left-area {
      flex: 3;
      border-right: 1px solid #f8f8f8;
    }
    .login-area {
      flex: 2;
      position: relative;
      width: 384px;
      overflow: hidden;
      .qr-wrap {
        width: 384px;
        min-height: 300px;
        text-align: center;
        @include transition($duration: 0.2s);
      }
      h1 {
        margin: 30px 0 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1.2;
        .logo {
          margin-right: 10px;
        }
      }
      p {
        text-align: center;
        color: #aaa;
      }
      .login-type-switcher {
        position: absolute;
        right: 0;
        border-width: 20px;
        border-style: solid;
        border-color: #333 #333 transparent transparent;
        width: 0;
        height: 0;
        border-top-right-radius: $borderRadius;
        span {
          position: absolute;
          cursor: pointer;
          width: 20px;
          height: 20px;
          right: -8px;
          top: -8px;
          font-size: 20px;
          color: white;
        }
        .anticon-user-switch,
        .anticon-qrcode {
          @include transition($duration: 0.2s);
        }
      }

      .login-form {
        margin-top: 40px;
        margin-left: 60px;
        width: 264px;
        @include transition();
        .submit-button {
          margin-top: 30px;
          width: 100%;
        }
      }

      .animation-scene {
        display: flex;
        width: 708px;
        @include transition;
      }

      &.login-type-account {
        .animation-scene {
          transform: translateX(-384px);
        }
        .qr-wrap,
        .anticon-user-switch {
          opacity: 0;
        }
      }
      &.login-type-ding {
        .login-form,
        .anticon-qrcode {
          opacity: 0;
        }
      }
    }
  }
  .copyright {
    position: absolute;
    bottom: 20px;
    color: #ccc;
    font-size: 12px;
  }
}
