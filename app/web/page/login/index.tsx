import React, { useEffect, useState, useContext } from 'react';
import { Form, Input, Button, message, notification } from 'antd';
import { loadScript } from '@/modules/asyncLoad';
import { UserSwitchOutlined, QrcodeOutlined } from '@ant-design/icons';
import { Copyright } from '@/component';
import { __ } from '@/modules/format';
import { isPasswordValid } from '@@/lib/tool';
import { AppContext } from '@/store';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min.js';
import request from 'request';
import './index.scss';

const Ding = 'ding';
const Account = 'account';

export default function Login({ history }) {
  const [loginType, setLoginType] = useState(Account);
  const [loading, setLoading] = useState(false);
  const [encrypt, setEncrypt] = useState<any>();
  const { publicKey, query, env, localeData, appId } = useContext(AppContext);
  const isRegister = query.hasOwnProperty('register');

  useEffect(() => {


    if (isRegister) {
      return;
    }
    // loadScript('https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js').then(() => {
    //   // @ts-ignore
    //   window.DTFrameLogin(
    //     {
    //       id: 'dingtalkLoginContainer',
    //       width: 300,
    //       height: 300
    //     },
    //     {
    //       redirect_uri: encodeURIComponent(`${window.location.protocol}//${window.location.host}/page/ddlogin`),
    //       client_id: appId,
    //       scope: 'openid',
    //       response_type: 'code',
    //       prompt: 'consent'
    //     },
    //     loginResult => {
    //       const { redirectUrl, authCode, state } = loginResult;
    //       window.location.href = `${redirectUrl}?code=${authCode}&corp=${query.corp}`;
    //     },
    //     errorMsg => {
    //       alert(`Login Error: ${errorMsg}`);
    //     }
    //   );
    // });
  }, []);

  useEffect(() => {
    const enc = new JSEncrypt();
    enc.setPublicKey(publicKey);
    setEncrypt(enc);
  }, []);

  const submitForm = values => {
    // if (!isPasswordValid(values.password)) {
    //   message.error(localeData.PasswordRule);
    //   return;
    // }
    const url = '/passport/local';
    values.password = encrypt.encrypt(values.password);
    request
      .post(url, values, { 'X-showMessage': true })
      .then((res: any) => {
        const searchParams = new URLSearchParams(location.search);
        const from = searchParams.get('from');
        searchParams.delete('from');
        if (from) {
          const search = searchParams.toString();
          location.href = `${from}${search ? `?${search}` : ''}`
          return;
        }
        location.href = '/';
      })
      .catch(console.error);
  };

  function toggleLogin() {
    setLoginType(loginType === Ding ? Account : Ding);
  }

  return (
    <section className="login-wrap">
      <div className="login-content">
        <div className="login-left-area">
          <img src={require('./images/fengmian.jpg')} alt="ad"></img>
        </div>
        <div className={`login-area login-type-${isRegister ? 'account' : loginType}`}>
          {/* {!isRegister && (
            <div className="login-type-switcher" onClick={toggleLogin}>
              <span>
                <UserSwitchOutlined />
                <QrcodeOutlined />
              </span>
            </div>
          )} */}
          <h1>
            <img className="logo" src={require('@/asset/images/logo.png')} width={40} alt="logo" />
            {localeData.AppName}
          </h1>
          {localeData.AppSlogan && <p>{localeData.AppSlogan}</p>}
          <div className={`animation-scene`}>
            <div>
              <div className="qr-wrap" id="dingtalkLoginContainer"></div>
              <p>{localeData.ScanWithDingTalk}</p>
            </div>

            <Form name="normal_login" className="login-form" initialValues={{ remember: true }} onFinish={submitForm}>
              <Form.Item name="username" rules={[{ required: true, message: __('Please', 'Input', 'Account') }]}>
                <div className="custom-input" aria-required="true" id="normal_login_username">
                  <Input placeholder={localeData.Account} autoComplete='username' style={{ height: '32px' }} />
                </div>
              </Form.Item>
              <Form.Item name="password" rules={[{ required: true, message: __('Please', 'Input', 'Password') }]}>
                <div className="custom-input" aria-required="true" id="normal_login_password">
                  <Input type="password" placeholder={localeData.Password} style={{ height: '32px' }} autoComplete='password' />
                </div>
              </Form.Item>
              <Form.Item>
                {isRegister ? (
                  <Button type="primary" size="large" htmlType="submit" className="submit-button" loading={loading}>
                    {localeData.Register}
                  </Button>
                ) : (
                  <Button type="primary" size="large" htmlType="submit" className="submit-button" loading={loading}>
                    {localeData.Login}
                  </Button>
                )}
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
      <Copyright />
    </section>
  );
}
