import React, { useEffect, useContext } from 'react';
import { Alert } from 'antd';
import { useAuth } from '@/hooks';
import { AppContext } from '@/store';
import './index.scss';

export default function Home(props) {
  const { isNotAuth } = useAuth();
  const { localeData, lang } = useContext(AppContext);

  return (
    <section className="home-wrap">
      <article>
        {lang === 'en' ? <h1>Welcome to {localeData.AppName}</h1> : <h1>欢迎来到{localeData.AppName} - 管理后台</h1>}

        <p>{localeData.AppSlogan}</p>

        {isNotAuth && <Alert className="mt-30" message={localeData.PermissionTips} description={localeData.PermissionTipsText} type="error" closable />}

      </article>
    </section>
  );
}
