import OperateAccess from '@/component/auth/operate-access';
import request from '@/modules/request';
import { urlsToAntdUploadList } from '@/modules/utils';
import { AppContext } from '@/store';
import useUserStore from '@/store/user.store';
import { clientRelatedField<PERSON>andler, userIsBD } from '@@/lib/constant';
import { isNumber } from '@@/lib/tool';
import { Button, message, Modal } from 'antd';
import moment from 'moment'
import React, { useContext, useState } from 'react';
import { createDefaultContractType } from './contact';
export const beforeEditTransform = (record: Record<string, any> = {}) => {
  const newRecord = { ...record }
  if (newRecord.contact_type) {
    newRecord.contact_type = Object.entries(newRecord.contact_type).map(([k, v]) => ({ key: k, value: v }))
  }
  if (newRecord.active_id) {
    newRecord.clue_from = Number(newRecord.clue_from)
  }
  if (newRecord.industry && !Array.isArray(newRecord.industry)) {
    newRecord.industry = newRecord.industry.split(',')
  }
  if (newRecord.crm_client) {
    newRecord.client_name = newRecord.crm_client.client_name
    newRecord.client_ctime = newRecord.crm_client.ctime ? moment(newRecord.crm_client.ctime).format('YYYY-MM-DD') : null
  }

  if (newRecord.attachment) {
    newRecord.attachment = urlsToAntdUploadList(newRecord.attachment);
  }
  if (!newRecord.industry) {
    newRecord.industry = undefined;
  }
  newRecord.ctime = moment(newRecord.ctime);
  if (newRecord.assignment_time) {
    newRecord.assignment_time = moment(newRecord.assignment_time);
  }
  if (newRecord.conversion_time) {
    newRecord.conversion_time = moment(newRecord.conversion_time);
  }
  if (newRecord.previously_bd && !Array.isArray(newRecord.previously_bd)) {
    newRecord.previously_bd = newRecord.previously_bd.split(',')
  }

  if (!newRecord.bd_id) {
    newRecord.bd_id = undefined
  }
  if (!newRecord.assist_id) {
    newRecord.assist_id = undefined
  }
  return newRecord;
}
export const beforeSubmitHandler = (values, curUserInfo) => {


  if (values.contact_type) {
    values.contact_type = values.contact_type.filter(it => it.key && it.value).reduce((obj, { key, value }) => {
      obj[key] = value;
      return obj
    }, {})
    if (Object.values(values.contact_type).length === 0) {
      values.contact_type = null;
    }
  }
  if (values.attachment) {
    values.attachment = values.attachment.map(it => it.url).join(',')
  }
  const clientRelatedField = clientRelatedFieldHandler(values, curUserInfo);
  if (!clientRelatedField) { // bd是自己的，才可以修改
    if (!values.contact_type && !values.attachment) {
      message.warn('Please enter content or upload attachment')
      return {
        isSuc: false,
        values
      };
    }
  }


  if (isNumber(values.clue_from)) {
    values.active_id = values.clue_from
  } else {
    values.active_id = null // 清空
  }
  for (const key in values) {
    if (!Object.prototype.hasOwnProperty.call(values, key)) { continue };
    if (key === 'contact_type') { continue };
    if (Array.isArray(values[key])) {
      values[key] = values[key].join(',')
    }
  }
  return {
    isSuc: true,
    values
  };
};

export const createCommonTableProps = ({ curUserInfo }) => {
  const USER_IS_BD = userIsBD(curUserInfo);

  return {
    actionWidth: 273,
    allowRowSelect: true,
    async beforeHandleCreate(values) {
      if (values.bd_id) {
        // 如果选了bd，状态是已分配
        values.clue_status = 'assigned'
      }
      return beforeSubmitHandler(values, curUserInfo);
    },
    async beforeHandleUpdate(values) {
      return beforeSubmitHandler(values, curUserInfo);
    },
    async onBatchDelete(selectRows) {
      for (const item of selectRows) {
        if (!['unassigned'].includes(item.clue_status)) {
          message.warn('Only the status of "Unassigned" can be deleted');
          return;
        }
      }
      const ids = selectRows.map(it => it.id);
      const result = await request.post('/clue/batchDestory', {
        ids
      });
      if (result.isSuccess) {
        message.success('Delete Success');
      } else {
        message.error(result.message);
      }
      return result.isSuccess;
    },
    beforeCreate() {
      const values: Record<string, any> = {
        contact_type: createDefaultContractType(),
      };
      if (USER_IS_BD) {
        values.bd_id = String(curUserInfo.id) || null
      }
      return values
    },
    beforeFilter(filters) {
      for (const key in filters) {
        if (!Object.prototype.hasOwnProperty.call(filters, key)) { continue };
        if (Array.isArray(filters[key])) {
          if (key === 'conversion_time') {
            filters.conversion_time = filters.conversion_time.map((it, index) => moment(it).format('YYYY-MM-DD HH:mm:ss')).join(',')
          } else if (key === 'ctime') {
            filters.ctime = filters.ctime.map((it, index) => moment(it).format('YYYY-MM-DD HH:mm:ss')).join(',')
          } else if (key === 'client_ctime') {
            filters.client_ctime = filters.client_ctime.map((it, index) => moment(it).format('YYYY-MM-DD HH:mm:ss')).join(',')
          } else {
            filters[key] = filters[key].join(',')

          }
        }
      }
      return filters
    }
  }
}


export const useCommonState = ({
  tableProps,
  tableRef
}) => {
  const curUserInfo = useUserStore(state => state);
  const { localeData } = useContext(AppContext);
  const [showLeadAssignment, setShowLeadAssignment] = useState({
    visible: false,
    ids: [] as number[],
    itemData: {} as Record<string, any>
  });
  const [conversionCustomer, setConversionCustomer] = useState({
    visible: false,
    itemData: {}
  });
  const [conversionFailed, setConversionFailed] = useState({
    visible: false,
    itemData: {}
  });
  const [markingInvalid, setMarkingInvalid] = useState({
    visible: false,
    itemData: {}
  });
  const [markingMismatch, setMarkingMismatch] = useState({
    visible: false,
    itemData: {}
  });
  const [markingOther, setMarkingOther] = useState({
    visible: false,
    itemData: {}
  });
  const [showFollowUpRecord, setShowFollowUpRecord] = useState({
    visible: false,
    itemData: {}
  });
  const [showUploadModal, setShowUploadModal] = useState({
    visible: false,
    itemData: {}
  });
  const [markingContacted, setMarkingContacted] = useState({
    visible: false,
    itemData: {}
  });
  const [curDropdownRecord, setCurDropdownRecord] = useState<Record<string, any>>({});
  const [curEditRecordId, setCurEditRecordId] = useState<null | number>(null); // 用来记录编辑的页面ID，如果在里面修改状态, 用来更新数据

  const USER_IS_BD = userIsBD(curUserInfo);

  const editStatusChangeHandler = () => {
    if (!curEditRecordId) { return };
    // 证明在编辑页面修改了状态，需要重新设置编辑页面的值
    const curEditRecord = tableProps.listData.find(it => it.id === curEditRecordId);
    if (!curEditRecord) { return };
    setCurDropdownRecord(curEditRecord);

    const newRecord = beforeEditTransform(curEditRecord);
    tableProps.setItemData({
      ...tableProps.itemData,
      ...newRecord
    })
    tableRef.current.updateForm.setFieldsValue({
      ...newRecord
    })
  }

  const commonStatus: any = { width: '100%', height: '100%', textAlign: 'left' }

  const statusMenu = [
    {
      key: '1',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['invalid', 'other'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            request.post('/clude/changeToUnassigned', { clue_id: curDropdownRecord.id }).then(res => {
              if (res.isSuccess) {
                message.success('Update success');
                tableProps.defaultFetchHangler();
              }
            })
          }}>Change to "Unassigned"</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '2',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['unassigned', 'conversion_failed', 'invalid', 'mismatch', 'contacted'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            const itemData: Record<string, any> = {};
            if (USER_IS_BD) {
              itemData.bd_id = String(curUserInfo.id) || null
            }
            setShowLeadAssignment({
              visible: true,
              ids: [curDropdownRecord.id] as number[],
              itemData
            })
          }}>Change to "Assigned"</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '10',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['assigned'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            setMarkingContacted({
              visible: true,
              itemData: {
                ids: [curDropdownRecord.id]
              }
            })
          }}>Marking as 'Contacted'</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '3',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['assigned', 'contacted'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            setConversionFailed({
              visible: true,
              itemData: {
                ids: [curDropdownRecord.id]
              }
            })
          }}>Marking as 'Conversion Failed'</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '4',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['assigned', 'contacted'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            setMarkingInvalid({
              visible: true,
              itemData: {
                ids: [curDropdownRecord.id]
              }
            })
          }}>Marking as 'Contact Failed'</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '5',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['assigned', 'contacted'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            setMarkingMismatch({
              visible: true,
              itemData: {
                ids: [curDropdownRecord.id]
              }
            })
          }}>Marking as 'Mismatched'</Button>
        </OperateAccess.Update>
      )
    },
    {
      key: '7',
      label: (
        <OperateAccess.Update>
          <Button type="link" style={commonStatus} disabled={
            !(['unassigned', 'assigned'].includes(curDropdownRecord.clue_status))
          } onClick={() => {
            setMarkingOther({
              visible: true,
              itemData: {
                ids: [curDropdownRecord.id]
              }
            })
          }}>Marking as 'Other'</Button>
        </OperateAccess.Update>
      )
    },
    // {
    //   key: '6',
    //   label: (
    //     <OperateAccess.Update>
    //       <Button type="link" style={commonStatus} onClick={() => {
    //         setShowFollowUpRecord({
    //           visible: true,
    //           itemData: {
    //             ...curDropdownRecord
    //           }
    //         })
    //       }}>Follow Up Records</Button>
    //     </OperateAccess.Update>
    //   )
    // },

  ];

  const editModalStatusMenu = [...statusMenu]
  editModalStatusMenu.splice(5, 0, {
    key: '8',
    label: (
      <OperateAccess.Create>
        <Button type='link' style={commonStatus} disabled={curDropdownRecord.crm_client || (curDropdownRecord.clue_status !== 'assigned' && curDropdownRecord.clue_status !== 'contacted')} onClick={() => {
          setConversionCustomer({
            visible: true,
            itemData: {
              ...curDropdownRecord,
              is_transformer: 'not_transformer'
            }
          })
        }}>
          {localeData.ConvertToCustomer}
        </Button>
      </OperateAccess.Create>
    )
  })

  return {
    showLeadAssignment,
    conversionCustomer,
    conversionFailed,
    markingInvalid,
    markingMismatch,
    markingOther,
    showFollowUpRecord,
    showUploadModal,
    curDropdownRecord,
    statusMenu,
    setCurDropdownRecord,
    setCurEditRecordId,
    setConversionCustomer,
    curEditRecordId,
    editStatusChangeHandler,
    setShowLeadAssignment,
    setConversionFailed,
    setMarkingInvalid,
    setMarkingMismatch,
    setMarkingOther,
    setShowFollowUpRecord,
    setShowUploadModal,
    editModalStatusMenu,

    markingContacted,
    setMarkingContacted
  }
}



export const useCustomerDetailData = () => {
  const [customerDetail, setCustomerDetail] = useState({
    visible: false,
    itemData: {}
  });
  const customClickHandler = (clientInfo, leadInfo) => {

    const ctime = moment(clientInfo.ctime);
    const effectiveDate = clientInfo.effective_date ? moment(clientInfo.effective_date) : null;
    const expiryDate = clientInfo.expiry_date ? moment(clientInfo.expiry_date) : null;

    setCustomerDetail({
      visible: true,
      itemData: {
        ...clientInfo,

        ctime,
        effective_date: effectiveDate,
        expiry_date: expiryDate,
        ori_company_name: clientInfo.company_name,
        ...beforeEditTransform(leadInfo),
      }
    })
  }

  return {
    customerDetail,
    setCustomerDetail,
    customClickHandler
  }
}
