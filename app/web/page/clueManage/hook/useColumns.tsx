import { Divider, FormInstance, Input, Popover, Space, Tag, Tooltip } from "antd";
import useFlatTableColumns from "../../../component/flat-table/useFlatTableColumns";
import ContactInfomation from '../components/ContactInfomation';
import UploadInput from "@/component/upload-file";
import useAppStore from "@/store/app.store";
import useSelector from "@/hooks/useSelector";
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import './useColumns.scss';
import { createLeadSource, LEAD_STATUS_COLOR_MAP, LEAD_STATUS_MAP } from './contact'
import { useEffect, useRef, useState } from "react";
import LoadingButton from "@/component/loading-button";
import { clientRelatedFieldHandler, CUSTOMER_STATUS_MAP, GET_COOP_BUS_STR, userIsBD, userIsBDAssistant, userIsBDLeader, userISDemoAccount, userIsMarketer, userIsOP } from "@@/lib/constant";
import useUserStore from "@/store/user.store";
import { CUSTOMER_STATUS_COLOR_MAP } from "@/page/customerManage/hook/contact";
import moment from "moment";


export const renderContactDetail = (item: string[], isTooltip = false) => {
  const [key, value] = item;
  return (
    <div style={{ display: 'flex', alignItems: 'center', lineHeight: '22px' }}>
      <span className="description-text">{key}：</span>
      <span style={isTooltip ? {} : { width: 180, display: 'inline-block', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', textAlign: 'left' }}>
        {value}
      </span>
    </div>
  )
}

export const useLeadColumns = ({ itemData = {}, setItemData }: any, tableRef: any, PAGE_TYPE: 'pool' | 'owned' = 'pool', customerClick) => {
  const { BD_USERS, BD_ASSISTANT_USERS, ACTIVITYS, ENUMS, ACCOUNTS } = useAppStore(useSelector(['BD_USERS', 'BD_ASSISTANT_USERS', 'ACTIVITYS', 'ENUMS', 'ACCOUNTS']));
  const inputRef = useRef(null);
  const [activityName, setActivityName] = useState('');
  const [leadSourceOptions, setLeadSourceOptions] = useState<any[]>([]);
  const curUserInfo = useUserStore(state => state);
  const isDeleteed = itemData?.clue_status === 'deleted';
  useEffect(() => {
    let leadSourceOptions = createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || [];;

    if (userISDemoAccount(curUserInfo as any)) {
      leadSourceOptions = createLeadSource([], ACTIVITYS.filter(it => it.value === 11))
    }
    setLeadSourceOptions(leadSourceOptions)

  }, [ENUMS, ACTIVITYS]);

  let clientRelatedFieldHidden = false;
  if (itemData?.id) {
    // 证明是编辑
    const clientRelatedField = clientRelatedFieldHandler(itemData, curUserInfo);
    if (!clientRelatedField) {
      clientRelatedFieldHidden = true;
    }
  }
  const columns = useFlatTableColumns([
    {
      key: 'id',
      width: 80,
      align: 'left',
      // @ts-ignore
      index: 0,
      filter: {
        type: 'Input',
      }
    },
    {
      key: 'company_name',
      align: 'left',
      index: 1,
      width: 240,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Input',
        index: 1,
        span: 6,
        group: 'Basic Infomation',
        disabled: isDeleteed,
        formItemProps: {
          required: true,
        },
      },
      update: true,
      ellipsis: true,
      render(text) {
        if (!text) {
          return '-'
        }
        return (
          <Tooltip title={text}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '240px' }}>{text}</div>
          </Tooltip>
        )
      }
    },
    {
      key: 'connect_name',
      hidden: true,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Input',
        index: 2,
        span: 6,
        hidden: clientRelatedFieldHidden,
        group: 'Basic Infomation',
        disabled: isDeleteed,
        formItemProps: {
          required: true,
        },
      },
      update: true,
    },
    {
      key: 'contact_type',
      title: (
        <Space>
          Contact Detail
          <Tooltip title="Please enter the contact name, contact information, Job title">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      ),
      align: 'center',
      index: 3,
      width: 240,
      filter: {
        type: 'Input',
        title: 'Contact Detail'
      },
      create: {
        type: 'Component',
        pureComponent: true,
        index: 7,
        span: 24,
        hidden: clientRelatedFieldHidden,
        group: 'Basic Infomation',
        render(form: FormInstance) {
          return (
            <ContactInfomation form={form} disabled={isDeleteed} />
          )
        }
      },
      update: true,
      render(json, record) {
        if (!json || !Object.values(json)?.length) { return '-' };
        const isShow = clientRelatedFieldHandler(record, curUserInfo);
        if (!isShow) { return '-' }
        const entriesData = Object.entries(json);
        const displayData = entriesData.slice(0, 2);
        const jobTitle = record.position || '-';
        const jobTitleText = clientRelatedFieldHandler(record, curUserInfo) ? jobTitle : '-';
        const connectName = `${record.connect_name} (Title: ${jobTitleText})`;
        const result = (
          <Popover content={
            <div>
              {
                [['Name', connectName], ...entriesData].map(it => (
                  renderContactDetail(it as string[], true)
                ))
              }
            </div>
          }>

            <>
              {
                [['Name', connectName], ...displayData].map(it => (
                  renderContactDetail(it as string[])
                ))
              }
              {
                entriesData.length > 2 && <div style={{ textAlign: 'left', lineHeight: '10px', marginLeft: '26px' }}>...</div>
              }
            </>


          </Popover>
        )

        return result;
      },
      export: {
        title: 'Contact Detail'
      }
    },
    {
      key: 'clue_content',
      align: 'left',
      index: 4,
      width: 200,
      ellipsis: true,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'TextArea',
        index: 6,
        span: 24,
        group: 'Basic Infomation',
        disabled: isDeleteed,
        labelCol: { span: 4 },
      },
      update: true,
      render(text) {
        if (!text) {
          return '-'
        }
        return (
          <Tooltip title={text}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '180px' }}>
              {text}
            </div>
          </Tooltip>

        )
      }
    },
    {
      key: 'customer_status',
      align: 'left',
      index: 5,
      width: 130,
      checked: false,
      filter: {
        type: 'Select',
        options: Object.entries(CUSTOMER_STATUS_MAP).map(([key, value]) => ({
          label: value,
          value: key
        }))
      },
      render(_, record) {
        if (!record.crm_client) {
          return '-'
        }
        const color = CUSTOMER_STATUS_COLOR_MAP[record.crm_client?.client_status] || '';
        return (
          <Tag color={color}>
            {CUSTOMER_STATUS_MAP[record.crm_client?.client_status] || '-'}
          </Tag>
        )
      },
    },
    {
      key: 'client_merge_field',
      align: 'center',
      index: 6,
      width: 240,
      filter: {
        type: 'Input',
        index: 2,
        placeholder: 'Customer Name / Customer ID',
        title: 'Customer ID/Name'
      },
      render(_, record) {
        if (!record.crm_client?.client_name) {
          return record.customer_name || '-'
        }
        const clientName = record.crm_client?.client_name || '-';
        return (
          <div style={{ textAlign: 'left' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ width: '100px', textAlign: 'right', display: 'inline-block' }}>Name：</span>
              <Tooltip title={clientName}>
                <span style={{ display: 'inline-block', width: '110px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }} className="link-jump-text" onClick={() => {
                  customerClick(record.crm_client, record);
                }}>{record.crm_client?.client_name || '-'}</span>
              </Tooltip>
            </div>
            <div><span style={{ width: '100px', textAlign: 'right', display: 'inline-block' }}>Customer ID： </span>{GET_COOP_BUS_STR(record.crm_client || {}) || '-'}</div>
          </div>
        )
      }
    },
    {
      key: 'client_name',
      hidden: true,
      create: {
        type: 'Input',
        disabled: true,
        group: 'BD Info',
        index: 4,
        span: 6,
      },
      update: true
    },

    {
      key: 'clue_from',
      align: 'left',
      index: 8,
      width: 170,
      filter: {
        type: 'Select',
        options: leadSourceOptions,
        mode: 'multiple',
        // @ts-ignore
        maxTagCount: 3,
      },
      create: {
        type: 'Select',
        index: 5,
        group: 'Basic Infomation',
        options: leadSourceOptions,
        disabled: isDeleteed,
        dropdownRender: (menu) => (
          <>
            {menu}
            <Divider style={{ margin: '8px 0' }} />
            <Space className="custom-use-columns-space">
              <Input
                style={{ width: '100%' }}
                placeholder="Please enter lead source name"
                ref={inputRef}
                value={activityName}
                maxLength={30}
                showCount
                onChange={(e) => {
                  setActivityName(e.target.value)
                }}
              />
              <LoadingButton icon={<PlusOutlined />} type="" api="/commonFilter/createEnum" method="post" params={{
                enum_type: 'lead_source',
                enum_code: activityName,
              }} onOk={(e: any = {}) => {
                const code = e.enum_code || '';
                leadSourceOptions[0].options.unshift({ label: activityName, value: activityName });
                setLeadSourceOptions([...leadSourceOptions])
                setActivityName('');
                if (tableRef.current?.createForm?.setFieldValue) {
                  tableRef.current.createForm.setFieldValue('clue_from', code)
                }
                // setLeadSource(code);
                setTimeout(() => {
                  // @ts-ignore
                  inputRef.current?.focus();
                }, 0);
              }}  >
                Add Lead Source
              </LoadingButton>
            </Space>
          </>
        ),
        formItemProps: {
          required: true,
        },
      },
      update: true,
      export: {
        mapping: leadSourceOptions.reduce((obj, item) => {
          item.options.forEach((opt) => {
            obj[opt.value] = opt.label;
          })
          return obj;
        }, {})
      },
      render(leadSource: string, record) {

        return ACTIVITYS.find(item => item.value === Number(record.active_id))?.label || leadSource

      }
    },
    {
      key: 'customer_name',
      hidden: true,
      create: {
        type: 'Input',
        index: 5,
        group: 'Basic Infomation',
        disabled: isDeleteed || itemData.crm_client?.client_name, // 转化客户就禁用了
      },
      update: true
    },

    {
      key: 'contact_title',
      hidden: true,
      create: {
        type: 'Title',
        index: 6,
        span: 24,
        hidden: clientRelatedFieldHidden,
        group: 'Basic Infomation',
        disabled: isDeleteed,
        formItemProps: {
          required: true,
        },
      },
      update: true
    },

    {
      key: 'position',
      hidden: true,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Input',
        index: 3,
        span: 6,
        group: 'Basic Infomation',
        hidden: clientRelatedFieldHidden,
        disabled: isDeleteed,
      },
      update: true
    },
    {
      key: 'industry',
      align: 'left',
      index: 10,
      width: 200,
      checked: false,
      filter: {
        type: 'Select',
        options: ENUMS.lead_industry
      },
      create: {
        type: 'Select',
        index: 4,
        span: 6,
        mode: 'multiple',
        options: ENUMS.lead_industry,
        group: 'Basic Infomation',
        disabled: isDeleteed,
      },
      update: true
    },

    {
      key: 'bd_id',
      align: 'left',
      index: 11,
      width: 150,
      filter: {
        type: 'Select',
        hidden: PAGE_TYPE === 'owned',
        options: BD_USERS,
      },
      create: {
        type: 'Select',
        group: 'BD Info',
        index: 1,
        span: 6,
        options: BD_USERS,
        disabled: true,
      },
      update: true,
      export: {
        mapping: BD_USERS.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
      render(bdId) {
        return BD_USERS.find(it => it.value === bdId)?.label || '-'
      }
    },
    {
      key: 'assist_id',
      align: 'left',
      checked: false,
      index: 12,
      width: 150,
      filter: {
        type: 'Select',
        hidden: PAGE_TYPE === 'owned' && userIsBDAssistant(curUserInfo),
        options: BD_ASSISTANT_USERS,
      },
      create: {
        type: 'Select',
        group: 'BD Info',
        index: 2,
        span: 6,
        options: BD_ASSISTANT_USERS,
        disabled: true
      },
      update: true,
      render(asId) {
        return BD_ASSISTANT_USERS.find(it => it.value === asId)?.label || '-'
      },
      export: {
        mapping: BD_ASSISTANT_USERS.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
    },
    {
      key: 'previously_bd',
      align: 'left',
      checked: false,
      index: 13,
      width: 150,
      filter: {
        type: 'Select',
        options: BD_USERS,
      },
      render(prevBdId = '') {
        if (!prevBdId) { return '-' }
        const prevBds = prevBdId.split(',').filter(it => it);
        const bdNames = prevBds.map(it => {
          return BD_USERS.find(bd => bd.value === it)?.label || it;
        })
        return bdNames.join(',');
        // return BD_ASSISTANT_USERS.find(it => it.value === asId)?.label || '-'
      },
      create: {
        type: 'Select',
        mode: 'multiple',
        options: BD_USERS,
        disabled: true,
        index: 3,
        span: 6,
        group: 'BD Info',
      },
      update: true
    },
    {
      key: 'leads_time',
      align: 'center',
      index: 14,
      width: 240,
      render(_, record) {

        return (
          <div style={{ textAlign: 'left' }}>
            <div>
              <span style={{ width: '130px', textAlign: 'right', display: 'inline-block' }}>Creation Time：</span>{record.ctime ? moment(record.ctime).format('YYYY-MM-DD') : '-'}
            </div>
            <div>
              <span style={{ width: '130px', textAlign: 'right', display: 'inline-block' }}>Converstion Time：</span>{record.conversion_time ? moment(record.conversion_time).format('YYYY-MM-DD') : '-'}
            </div>

          </div>
        );
      }
    },
    {
      key: 'other_reason',
      align: 'left',
      index: 16,
      width: 150,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'AutoComplete',
        options: [],
        group: 'BD Info',
        span: 6,
        index: 10,
        disabled: true,
      },
      update: true,
      render(text, record) {
        if (record.clue_status === 'conversion_failed') {
          return record.convert_fail || '-';
        }
        if (record.clue_status === 'mismatch') {
          return record.mismatch || '-';
        }
        if (record.clue_status === 'invalid') {
          return record.invalid_clue || '-';
        }
        if (record.clue_status === 'other') {
          return record.other_reason || '-';
        }
        return '-';
      }
    },
    {
      key: 'creator_id',
      align: 'left',
      index: 16,
      width: 130,
      filter: {
        type: 'Select',
        options: ACCOUNTS,
        mode: 'multiple',
      },
      render(id) {
        if (!id) {
          return '-'
        }
        return ACCOUNTS.find(it => it.value === id)?.label || '-'
      }
    },
    {
      key: 'clue_status',
      align: 'center',
      index: 17,
      fixed: 'right',
      width: 160,
      render(status) {
        const color = LEAD_STATUS_COLOR_MAP[status];

        return (
          <Tag style={{ marginRight: 0 }} color={color}>
            {LEAD_STATUS_MAP[status] || ''}
          </Tag>
        )
      },
      filter: {
        type: 'Select',
        options: Object.entries(LEAD_STATUS_MAP).map(([k, v]) => ({ label: v, value: k })),
      },
      export: {
        mapping: LEAD_STATUS_MAP
      }
    },
    {
      key: 'attachment',
      align: 'left',
      hidden: true,
      create: {
        type: 'Component',
        span: 24,
        labelCol: { span: 4 },
        hidden: clientRelatedFieldHidden,
        group: 'Basic Infomation',
        render(form) {
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={itemData}
              setItemData={setItemData}
              isLargeMode={false}
              disabled={isDeleteed}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg,.xlsx,.xls"
            />
          )
        }
      },
      update: true
    },
    {
      key: 'conversion_time',
      hidden: true,
      filter: {
        type: 'RangePicker',
        index: 98,
        title: 'Conversion Time'
      },
      create: {
        type: 'DatePicker',
        group: 'BD Info',
        disabled: true,
        span: 6,
        index: 5,
      },
      update: true
    },
    {
      key: 'assignment_time',
      hidden: true,
      create: {
        type: 'DatePicker',
        group: 'BD Info',
        disabled: true,
        span: 6,
        index: 6
      },
      update: true
    },
    {
      key: 'ctime',
      hidden: true,

      filter: {
        type: 'RangePicker',
        index: 99,
        title: 'Creation Time'
      },
      create: {
        type: 'DatePicker',
        group: 'BD Info',
        disabled: true,
        span: 6,
        index: 7
      },
      update: true
    },
    {
      key: 'convert_fail',
      hidden: true,
      create: {
        type: 'AutoComplete',
        options: ENUMS.lead_conversion_failure,
        group: 'BD Info',
        span: 6,
        index: 9,
        disabled: true,
      },
      update: true
    },
    {
      key: 'mismatch',
      hidden: true,
      create: {
        type: 'AutoComplete',
        options: ENUMS.lead_mismatch,
        group: 'BD Info',
        span: 6,
        index: 11,
        disabled: true,
      },
      update: true
    },
    {
      key: 'invalid_clue',
      hidden: true,
      create: {
        type: 'AutoComplete',
        options: ENUMS.lead_invalid_clue,
        span: 6,
        group: 'BD Info',
        index: 12,
        disabled: true,
      },
      update: true
    },
    {
      key: 'client_ctime',
      align: 'left',
      width: 180,
      index: 15,
      checked: false,
      create: {
        type: 'Input',
        disabled: true,
        group: 'BD Info',
        span: 6,
        index: 8,
      },
      update: true,
      filter: {
        type: 'RangePicker',
        index: 100,
      },
      render(text, record) {
        if (!record.crm_client?.ctime) {
          return '-';
        }
        return moment(record.crm_client?.ctime).format('YYYY-MM-DD');
      }
    }
  ], {
    Fields: {
      id: 'Leads ID',
      company_name: 'Company',
      clue_from: 'Lead Source',
      customer_name: 'Customer Name',
      clue_content: 'Lead Content',
      connect_name: 'Contact Name',
      contact_type: 'Contact Detail',
      position: 'Job Title',
      industry: 'Industry',
      clue_status: 'Lead Status',
      bd_id: 'BD',
      assist_id: 'BD Assistant',
      client_name: 'Customer Name',
      client_ass_id: 'Customer ID',
      attachment: 'Contact attachment',
      conversion_time: 'Lead Converstion Time',
      assignment_time: 'Assignment Time',
      ctime: 'Lead Creation Time',
      convert_fail: 'Conversion Failure Reason',
      other_reason: 'Other Leads Reason',
      mismatch: 'Mismatched Reason',
      invalid_clue: 'Invalid Lead Reason',
      previously_bd: 'Previously BD',
      leads_time: 'Leads Time',
      contact_title: 'Contact infomation (Fill in at least one of the following)',
      customer_status: 'Customer Status',
      creator_id: 'Lead Creator',
      client_merge_field: 'Customer',
      client_ctime: 'Create Customer Time'
    }
  });
  return columns
}
