import React, { useState } from 'react';

import useFlatTableColumns from "@/component/flat-table/useFlatTableColumns"
import useSelector from "@/hooks/useSelector";
import useAppStore from "@/store/app.store";
import { CUSTOMER_COOP_TYPE, CUSTOMER_STATUS } from '../../customerManage/hook/contact';
import { clientRelatedFieldHandler, CONTRACT_IS_EXCEED_AMOUNT, CONTRACT_TEMPLATE, CONTRACT_TYPE } from '@@/lib/constant';
import { Col, Form, FormInstance, Row, Select, Tooltip } from 'antd';
import ContactInfomation from '../components/ContactInfomation';
import UploadInput from '@/component/upload-file';
import { CONTRACT_OUR_COMPANY_NAME } from '@/page/contractManage/hook/contact';
import { createLeadSource } from './contact';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { getClientNameExtra } from '@/page/customerManage/hook/commonFunc';
import { isNumberReg } from '@@/lib/tool';


// export const createcCustomerCommonColumns = (group = 'Contract information template') => [
//   {
//     key: 'contract_code',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 1,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合同编号 Contract No.'
//     },
//     update: true
//   },
//   {
//     key: 'contract_type',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Select',
//       options: CONTRACT_TYPE,
//       index: 2,
//       span: 6,
//       group: group,
//       // @ts-ignore
//       dropdownMatchSelectWidth: 500,
//       tooltip: 'Map to DingTalk OA: 合同类型 Contract Type'
//     },
//     update: true
//   },
//   {
//     key: 'template',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Cascader',
//       index: 3,
//       span: 6,
//       options: CONTRACT_TEMPLATE,
//       group: group,
//     },
//     update: true
//   },
//   {
//     key: 'bu',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 3,
//       span: 6,
//       group: group,
//     },
//     update: true
//   },
//   {
//     key: 'payment_terms',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'AutoComplete',
//       index: 6,
//       span: 6,
//       group: 'Customer Information',
//       tooltip: '账期([0,365])',
//       options: [
//         { label: '30', value: '30' },
//         { label: '45', value: '45' },
//         { label: '60', value: '60' },
//       ]
//     },
//     update: true
//   },
//   {
//     key: 'is_exceed_amount',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Select',
//       index: 5,
//       span: 6,
//       options: CONTRACT_IS_EXCEED_AMOUNT,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合同金额是否超￥10万Does the contract amount exceed ￥100K？'
//     },
//     update: true
//   },
//   {
//     key: 'currency',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 6,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 币种 Currency'
//     },
//     update: true
//   },
//   {
//     key: 'amount',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'InputNumber',
//       index: 7,
//       span: 6,
//       group: group,
//       precision: 3,
//       min: 0,
//       max: 99999999999,
//       tooltip: 'Map to DingTalk OA: 合同金额（元）Contract Amount'
//     },
//     update: true
//   },
//   {
//     key: 'our_company_name',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Select',
//       index: 8,
//       span: 6,
//       group: group,
//       options: CONTRACT_OUR_COMPANY_NAME,
//       tooltip: 'Map to DingTalk OA: 我方签约主体 Our Company name'
//     },
//     update: true
//   },
//   {
//     key: 'our_contact_person',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 9,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 我方对接人 Contact person from our side'
//     },
//     update: true
//   },
//   {
//     key: 'our_information',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 10,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 我方联系方式 Contact information from our side'
//     },
//     update: true
//   },
//   {
//     key: 'partner_company_name',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 11,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合作方签约主体 Company Name of the Business Partner'
//     },
//     update: true
//   },
//   {
//     key: 'partner_contact_person',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 12,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合作方对接人 Contact person'
//     },
//     update: true
//   },
//   {
//     key: 'partner_information',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 13,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合作方联系方式 Contact Information'
//     },
//     update: true
//   },
//   {
//     key: 'bus_brand_name',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'Input',
//       index: 14,
//       span: 6,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 合作方名称 Brand Name of the Business Partner'
//     },
//     update: true
//   },
//   {
//     key: 'effective_date',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'DatePicker',
//       index: 15,
//       span: 6,
//       showTime: false,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 签约日期 Effective Date'
//     },
//     update: true
//   },
//   {
//     key: 'expiry_date',
//     align: 'center',
//     hidden: true,
//     create: {
//       type: 'DatePicker',
//       index: 16,
//       span: 6,
//       showTime: false,
//       group: group,
//       tooltip: 'Map to DingTalk OA: 到期日期 Expiry Date'
//     },
//     update: true
//   }
// ]

export const createcCustomerCommonColumns = ({
  itemData,
  setItemData,
  group = 'Payment Information',
  isDisabledClue = true,
  isCutomerClueCreate = false
}) => {
  const { ENUMS, COUNTRYS } = useAppStore(useSelector(['ENUMS', 'COUNTRYS']));
  return [

    {
      key: 'pay_type',
      align: 'center',
      create: {
        type: 'Select',
        index: 1,
        span: 6,
        group,
        options: ENUMS.pay_type,
        formItemProps: {
          required: itemData.coop_bus?.includes?.('Affiliate') && itemData.coop_bus?.includes?.('Advertiser Group'),
        },
        onChange(value, form) {
          setItemData({
            ...itemData,
            pay_type: value
          })
        }
      },
      update: {
        type: 'Select',
        index: 1,
        span: 6,
        disabled: true,
        group,
        options: ENUMS.pay_type,
        formItemProps: {
          required: itemData.coop_bus?.includes?.('Affiliate') && itemData.coop_bus?.includes?.('Advertiser Group'),
        }
      },
    },

    {
      key: 'payment_terms',
      align: 'center',
      hidden: true,
      create: {
        type: 'AutoComplete',
        index: 2,
        span: 6,
        group,
        min: 1,
        placeholder: 'Payment Period',
        precision: 0,
        tooltip: '账期([0,365])',
        options: [
          { label: '30', value: '30' },
          { label: '45', value: '45' },
          { label: '60', value: '60' },
        ],
        rules: [
          {
            validator(_, value) {
              if (!value) { return Promise.resolve() }
              return !isNumberReg.test(value) || Number(value) < 0 ? Promise.reject('The number is wrong. Decimals and negative numbers are not allowed.') : Promise.resolve()
            },
          }
        ],
      },
      update: {
        type: 'AutoComplete',
        index: 2,
        span: 6,
        group,
        tooltip: '账期([0,365])',
        options: [
          { label: '30', value: '30' },
          { label: '45', value: '45' },
          { label: '60', value: '60' },
        ],
        rules: [
          {
            validator(_, value) {
              if (!value) { return Promise.resolve() }
              return !isNumberReg.test(value) || Number(value) < 0 ? Promise.reject('The number is wrong. Decimals and negative numbers are not allowed.') : Promise.resolve()
            },
          }
        ],

      }
    },

    {
      key: 'payment_legal_name',
      align: 'center',
      hidden: true,
      create: {
        type: 'Input',
        index: 3,
        group,
        span: 6,
        tooltip: 'Used for generating invoice',
      }
    },
    {
      key: 'company_name',
      align: 'center',
      create: {
        type: 'Input',
        index: 4,
        group,
        span: 6,
        tooltip: 'Used for generating invoice',
        formItemProps: {
          required: isCutomerClueCreate || itemData.coop_bus?.includes?.('Advertiser Group') || itemData.coop_bus?.includes?.('Demand Group') || itemData.coop_bus?.includes?.('Interative Demand Group') || itemData.coop_bus?.includes?.('H5 Advertiser') || !isDisabledClue,
        }
      }
    },
    {
      key: 'comp_address',
      align: 'center',
      checked: false,
      create: {
        type: 'Input',
        index: 5,
        group,
        span: 6,
        tooltip: 'Used for generating invoice',
        formItemProps: {
          required: itemData.coop_bus?.includes?.('Advertiser Group') || itemData.coop_bus?.includes?.('Demand Group') || itemData.coop_bus?.includes?.('Interative Demand Group') || itemData.coop_bus?.includes?.('H5 Advertiser'),
        }
      }
    },
    {
      key: 'country',
      align: 'center',
      hidden: true,
      create: {
        type: 'Select',
        index: 6,
        span: 6,
        options: COUNTRYS,
        tooltip: 'Used for generating invoice',
        group,
        formItemProps: {
          required: itemData.coop_bus?.includes?.('Advertiser Group') || itemData.coop_bus?.includes?.('Demand Group') || itemData.coop_bus?.includes?.('Interative Demand Group') || itemData.coop_bus?.includes?.('H5 Advertiser'),
        }
      }
    },
    {
      key: 'tag_reg_no',
      hidden: true,
      create: {
        type: 'Input',
        index: 7,
        span: 6,
        tooltip: 'Used for generating invoice',
        group
      }
    },
    {
      key: 'usdt_account_id',
      hidden: true,
      create: {
        type: 'Input',
        index: 8,
        span: 6,
        group,
      },
    },
    {
      key: 'other_payment_terms',
      hidden: true,
      create: {
        type: 'TextArea',
        index: 9,
        span: 12,
        group
      },
    }

    // {
    //   key: 'our_company_name',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Select',
    //     index: 8,
    //     span: 6,
    //     group: group,
    //     options: CONTRACT_OUR_COMPANY_NAME,
    //     tooltip: 'Map to DingTalk OA: 我方签约主体 Our Company name'
    //   },
    //   update: true
    // },
    // {
    //   key: 'our_contact_person',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 9,
    //     span: 6,
    //     group: group,
    //     tooltip: 'Map to DingTalk OA: 我方对接人 Contact person from our side'
    //   },
    //   update: true
    // },
    // {
    //   key: 'our_information',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 10,
    //     span: 6,
    //     group: group,
    //     tooltip: 'Map to DingTalk OA: 我方联系方式 Contact information from our side'
    //   },
    //   update: true
    // },
    // {
    //   key: 'partner_company_name',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 11,
    //     span: 6,
    //     group: group,
    //     tooltip: 'Map to DingTalk OA: 合作方签约主体 Company Name of the Business Partner'
    //   },
    //   update: true
    // },
    // {
    //   key: 'partner_contact_person',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 12,
    //     span: 6,
    //     group: group,
    //     tooltip: 'Map to DingTalk OA: 合作方对接人 Contact person'
    //   },
    //   update: true
    // },
    // {
    //   key: 'partner_information',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 13,
    //     span: 6,
    //     group: group, 
    //     tooltip: 'Map to DingTalk OA: 合作方联系方式 Contact Information'
    //   },
    //   update: true
    // },
    // { 
    //   key: 'bus_brand_name',
    //   align: 'center',
    //   hidden: true,
    //   create: {
    //     type: 'Input',
    //     index: 14,
    //     span: 6,
    //     group: group,
    //     tooltip: 'Map to DingTalk OA: 合作方名称 Brand Name of the Business Partner'
    //   },
    //   update: true
    // },

  ]
}

export const contractCommonFields = {
  contract_code: 'Contract No',
  contract_type: 'Contract Type',
  template: 'File template',
  bu: 'BU',
  pay_type: 'Payment Type',
  payment_terms: 'Payment Period',
  is_exceed_amount: 'Is amount exceed ￥100K？',
  currency: 'Currency',
  amount: 'Amount',
  payment_legal_name: 'Payment Legal Name',
  our_company_name: 'Our Company Name',
  our_contact_person: 'Our Contact Person',
  our_information: 'Our Contact Information',
  partner_company_name: 'Company Legal Name',
  partner_contact_person: 'Partner Contact Person',
  partner_information: 'Partner Contact Information',
  bus_brand_name: 'Partner Brand Name',
  effective_date: 'Effective Date',
  expiry_date: 'Expiry Date',
}

export const customerFields = {
  client_name: 'Customer Name',
  coop_bus: 'Cooperation Type',
  bus_line: 'Department',
  company_name: 'Company Legal Name',
  comp_address: 'Company Address',
  country: 'Country',
  tag_reg_no: 'Tag Reg No',
  client_status: 'Customer Status',
  bd_id: 'BD',
  assist_id: 'BD Assistant',
  remark: 'Note',

  ...contractCommonFields,

  position: 'Job Title',
  ctime: 'Creation Time',
  connect_name: 'Contact Name',
  contact_type: 'Contact Information',

  id: 'ID',

  // 如果是在线索转换客户，会有线索字段
  ori_company_name: 'Company',
  industry: 'Industry',
  customer_name: 'Customer Name',
  clue_content: 'Lead Content',
  contact_title: 'Contact infomation (Fill in at least one of the following)',
  attachment: 'Contact attachment',
  clue_from: 'Lead Source',

  usdt_account_id: 'USDT Account ID',
  other_payment_terms: 'Special Payment Terms',
}

export const createClueCustomerColumns = ({
  itemData,
  setItemData,
  isDisabledClue = true,
  isCutomerClueCreate = false,
  leadList = [] as Record<string, any>[],
  type = ''
}) => {
  const { BD_USERS, BD_ASSISTANT_USERS, ACTIVITYS, ENUMS, COUNTRYS } = useAppStore(useSelector(['BD_USERS', 'BD_ASSISTANT_USERS', 'ACTIVITYS', 'ENUMS', 'COUNTRYS']));
  // 选择ADX 上游或者下游的时候 触发检查名称，是否包含“DSP”
  let extra = getClientNameExtra(itemData)
  return [
    {
      key: 'client_name',
      align: 'center',
      create: {
        type: 'Input',
        index: 1,
        span: 6,
        group: 'Customer Information',
        onChange(e, form) {
          setItemData({
            ...itemData,
            client_name: e.target.value || ''
          })
        },
        formItemProps: {
          required: true,
          // 选择adx上游的时候，触发名称检查
          extra
        },
        rules: [
          {
            validator: (_, value) => {
              if (!value) {
                return Promise.resolve();
              }
              if (value && value[0] === value[0].toUpperCase()) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('The first letter must be capitalized'));
            }
          }
        ]
      },
      update: {
        type: 'Input',
        index: 1,
        span: 6,
        group: 'Customer Information',
        formItemProps: {
          required: true,
        },
      },
    },
    {
      key: 'coop_bus',
      align: 'center',
      create: {
        type: 'Cascader',
        index: 2,
        options: CUSTOMER_COOP_TYPE,
        span: 6,
        group: 'Customer Information',
        formItemProps: {
          required: true,
        },
        onChange(value, form) {
          setItemData({
            ...itemData,
            coop_bus: value
          })
          form.validateFields(['pay_type', 'country']);
          const busLine = value?.[0] || '';
          form.setFieldValue('bus_line', busLine)
        }
      },
      update: {
        type: 'Cascader',
        index: 2,
        options: CUSTOMER_COOP_TYPE.map(it => ({
          ...it, disabled: true, children: it.children.map(child => ({
            ...child, disabled: true, children: !child.children ? undefined : child.children.map(it => ({
              ...it,
              disabled: false
            }))
          }))
        })),
        span: 6,
        group: 'Customer Information',
        disabled: false,
        allowClear: false,
        formItemProps: {
          required: true,
        },
        onChange(value, form) {
          setItemData({
            ...itemData,
            coop_bus: value
          })
          form.validateFields(['pay_type'])
          const busLine = value?.[0] || '';
          form.setFieldValue('bus_line', busLine)
        }
      }
    },
    {
      key: 'bus_line',
      align: 'center',
      checked: false,
      create: {
        type: 'Select',
        options: ENUMS.customer_department,
        group: 'Customer Information',

        index: 12,
        span: 6,
        disabled: true,
      }
    },



    {
      key: 'client_status',
      align: 'center',
      create: {
        type: 'Select',
        index: 3,
        options: CUSTOMER_STATUS,
        group: 'Customer Information',
        formItemProps: {
          required: true,
        },
        span: 6
      }
    },
    {
      key: 'bd_id',
      align: 'center',
      create: {
        type: 'Select',
        index: 13,
        options: BD_USERS,
        group: 'Customer Information',
        span: 6,
        disabled: true
      }
    },
    {
      key: 'assist_id',
      align: 'center',
      checked: false,
      create: {
        type: 'Select',
        index: 14,
        options: BD_ASSISTANT_USERS,
        group: 'Customer Information',
        span: 6,
        disabled: true
      }
    },

    {
      key: 'remark',
      align: 'center',
      create: {
        type: 'TextArea',
        index: 15,
        span: 24,
        group: 'Customer Information',
        labelCol: { span: 4 },
      }
    },
    ...createcCustomerCommonColumns({ itemData, setItemData, isCutomerClueCreate }),

    {
      key: 'lead_type',
      align: 'center',
      hidden: true,
      create: {
        type: 'Radio',
        optionType: 'button',
        options: [
          { label: 'Create New Lead', value: 'create_new_lead' },
          { label: 'Select from the lead Pool', value: 'select_from_the_lead_pool' }
        ],
        index: -2,
        span: 24,
        formItemProps: {
          required: true,
        },
        hidden: !isCutomerClueCreate,
        onChange(e) {
          setItemData({
            ...itemData,
            lead_type: e.target.value
          })
        },
        group: 'Lead Infomation',
      }
    },

    {
      key: 'lead_id',
      hidden: true,
      create: type === 'contract' ? {
        type: 'Component',
        index: -1,
        span: 24,
        hidden: !isCutomerClueCreate || itemData.lead_type === 'create_new_lead',
        group: 'Lead Infomation',
        tooltip: 'Only leads with the status of "Assigned" can be selected. The BD information of the newly created customer will be based on the lead BD.',
        options: leadList,
        pureComponent: true,
        render(form, text) {
          return (
            <Form.Item >
              <Row align="middle" gutter={[10, 10]}>
                <Col span={12}>
                  <Form.Item label={(
                    <div >
                      Leads&nbsp;
                      <Tooltip title="Only leads with the status of 'Assigned' can be selected. The BD information of the newly created customer will be based on the lead BD.">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </div>

                  )} name="lead_id" rules={[{ required: true, message: 'Please select a lead' }]}>
                    <Select placeholder="Please select a lead" allowClear optionFilterProp='label' showSearch options={leadList} onChange={(e) => {
                      const leadInfo = leadList.find(it => it.value === e);
                      if (leadInfo) {
                        form.setFieldValue('bd_id', leadInfo.bd_id);
                      }
                      setItemData({
                        ...itemData,
                        bd_id: leadInfo?.bd_id
                      })
                    }} />
                  </Form.Item>
                </Col>
                {
                  itemData.bd_id && (
                    <Col span={12} style={{ paddingTop: '26px' }}>
                      BD: {BD_USERS.find(it => it.value === itemData.bd_id)?.label || '-'}
                    </Col>
                  )
                }
              </Row>

            </Form.Item>
          )
        }
      } : {
        type: 'Select',
        index: -1,
        span: 12,
        hidden: !isCutomerClueCreate || itemData.lead_type === 'create_new_lead',
        group: 'Lead Infomation',
        options: leadList,
        formItemProps: {
          required: true,
        },
      }
    },


    {
      key: 'ori_company_name',
      hidden: true,
      create: {
        type: 'Input',
        index: 1,
        span: 6,
        hidden: !isDisabledClue || itemData.lead_type === 'select_from_the_lead_pool',
        group: 'Lead Infomation',
        disabled: true,
      },
      update: true
    },
    {
      key: 'connect_name',
      hidden: true,
      create: {
        type: 'Input',
        index: 2,
        span: 6,
        group: 'Lead Infomation',
        disabled: isDisabledClue,
        formItemProps: {
          required: !isDisabledClue,
        },
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
      },
      update: true
    },
    {
      key: 'position',
      hidden: true,
      create: {
        type: 'Input',
        index: 3,
        span: 6,
        group: 'Lead Infomation',
        disabled: isDisabledClue,
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
      },
      update: true
    },
    {
      key: 'industry',
      hidden: true,
      create: {
        type: 'Select',
        index: 4,
        span: 6,
        options: ENUMS.lead_industry,
        mode: 'multiple',
        group: 'Lead Infomation',
        disabled: isDisabledClue,
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
      },
      update: true
    },
    {
      key: 'clue_from',
      hidden: true,
      create: {
        type: 'Select',
        index: 2,
        span: 6,
        hidden: isDisabledClue || itemData.lead_type === 'select_from_the_lead_pool',
        options: createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || [],
        group: 'Lead Infomation',
        formItemProps: {
          required: !isDisabledClue,
        }
      },
    },
    {
      key: 'customer_name',
      hidden: true,
      create: {
        type: 'Input',
        index: 5,
        group: 'Lead Infomation',
        hidden: !isDisabledClue || itemData.lead_type === 'select_from_the_lead_pool',
        disabled: true
      },
      update: true
    },

    {
      key: 'clue_content',
      hidden: true,
      create: {
        type: 'TextArea',
        index: 6,
        span: 24,
        group: 'Lead Infomation',
        disabled: isDisabledClue,
        hidden: itemData.lead_type === 'select_from_the_lead_pool'
      },
      update: true
    },

    {
      key: 'contact_title',
      hidden: true,
      create: {
        type: 'Title',
        index: 7,
        span: 24,
        group: 'Lead Infomation',
        disabled: true,
        formItemProps: {
          required: !isDisabledClue,
        },
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
      },
      update: true
    },
    {
      key: 'contact_type',
      hidden: true,
      create: {
        type: 'Component',
        pureComponent: true,
        index: 8,
        span: 24,
        group: 'Lead Infomation',
        render(form: FormInstance) {
          return (
            <ContactInfomation form={form} disabled={isDisabledClue} />
          )
        },
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
      },
      update: true,
    },
    {
      key: 'attachment',
      hidden: true,
      create: {
        type: 'Component',
        span: 24,
        index: 9,
        group: 'Lead Infomation',
        hidden: itemData.lead_type === 'select_from_the_lead_pool',
        render(form) {
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={itemData}
              setItemData={setItemData}
              isLargeMode={false}
              disabled={isDisabledClue}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg,.xlsx,.xls"
            />
          )
        }
      },
      update: true
    },

  ]
}

export const useCustomerClueColumns = ({ itemData, setItemData, isDisabledClue = true, isCutomerClueCreate = false, leadList = [], type = '' }: Record<string, any>) => {

  const cls: any[] = createClueCustomerColumns({ itemData, setItemData, isDisabledClue, isCutomerClueCreate, leadList, type })
  const columns = useFlatTableColumns(cls, {
    Fields: {
      ...customerFields,
      lead_type: 'Lead Type',
      lead_id: 'Leads',
    }
  })

  return columns;

}
