export const createLeadSource = (leadSource: ICommonRecord[], activitys: ICommonRecord[]) => {
  return [
    {
      label: 'Lead Source',
      options: leadSource
    },
    {
      label: 'Marketing activity',
      options: activitys
    }
  ]
}

export const LEAD_STATUS_MAP = {
  'unassigned': 'Unassigned',
  'assigned': 'Assigned',
  'conversion_failed': 'Conversion failed',
  'converted': 'Conversion Successful',
  'invalid': 'Contact Failed',
  'mismatch': 'Mismatch',
  'other': 'Other',
  'contacted': 'Contacted',
  'deleted': 'Deleted'
}

export const LEAD_STATUS_COLOR_MAP = {
  'unassigned': '',
  'assigned': 'green',
  'converted': 'geekblue',
  'conversion_failed': 'volcano',
  'invalid': 'volcano',
  'mismatch': 'volcano',
  'other': 'blue',
  'deleted': 'error',
  'contacted': 'green'
}

export const createDefaultContractType = () => {
  return [
    { key: 'Email', value: '' },
    { key: 'Phone', value: '' },
    { key: 'Wechat', value: '' }
  ]
}
