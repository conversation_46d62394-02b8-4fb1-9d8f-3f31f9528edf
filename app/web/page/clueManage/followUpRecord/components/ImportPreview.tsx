import { CUSTOMER_STATUS_MAP, FOLLOW_UP_RECORD_TYPE, GET_COOP_BUS_TO_SIMPLIFY } from '@@/lib/constant'
import { Button, Divider, Form, message, Modal, Popover, Select, Table, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { EditOutlined } from '@ant-design/icons';
import request from '@/modules/request';
import { beforeHandleCreate } from '../hook';
import { createCommonTableProps, useCustomerDetailData } from '../../hook/commonFunc';
import { useLeadColumns } from '../../hook/useColumns';
import CreateModal from '@/component/createModal';
import useUserStore from '@/store/user.store';
import { useFlatTable } from '@/component/flat-table/useFlatTable';
import './index.scss';
import { createDefaultContractType } from '../../hook/contact';

let companyNameBeforeValue = '';
let creatorBeforeValue = '';
let curCreateIndex = 0;
let curClickCreator = '';

export default function ImportPreview({
  onCancel,
  dataSource,
  clueList,
  creatorList,
}) {
  const [listData, setListData] = useState<Recordable[]>([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();
  const curUserInfo = useUserStore(state => state);


  const { customClickHandler } = useCustomerDetailData()
  const tableProps = useFlatTable({
    title: 'Leads Pool',
    fetchUrl: '/clue',
    pageSize: 20,
    defaultParams: {},
    columns: (data) => useLeadColumns(data, null, 'pool', customClickHandler),

    ...createCommonTableProps({ curUserInfo }),
    beforeCreate: () => {
      const values: Record<string, any> = {
        contact_type: createDefaultContractType(),
      };
      const creatorId = creatorList.find(it => it.label === curClickCreator)?.id;

      values.bd_id = creatorId
      return values
    },
    afterCreate: (result, submitData) => {
      if (result.isSuccess) {
        setListData(listData.map((item, idx) => {
          if (curCreateIndex === idx) {
            item.company_name = `${result.data.id}:${result.data.company_name}`;
            item.company_is_correct = true;
            item.company_is_msg = '';
          }
          return item
        }))
      }
    }
  });
  const columns = [
    {
      dataIndex: 'company_name',
      title: 'Company',
      width: 300,
      render(text, record, index) {
        if (record.company_is_correct) {
          return (
            <Tooltip title={text}>
              {text}
            </Tooltip>
          );
        }
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {
              record.company_is_edit ? (
                <Select
                  style={{ width: 250 }}
                  value={text}
                  options={clueList.map(it => ({
                    ...it,
                    searchLabel: `${it.id}:${it.company_name}:${it.crm_client ? CUSTOMER_STATUS_MAP[it.crm_client?.client_status] : ''}:${it.crm_client ? GET_COOP_BUS_TO_SIMPLIFY(it.crm_client?.coop_bus) : ''}`,
                    label: (
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ width: 300 }}>{it.id}:{it.company_name}</span>
                        <span style={{ width: 200 }}>{it.crm_client ? CUSTOMER_STATUS_MAP[it.crm_client?.client_status] : ''}</span>
                        <span style={{ width: 200 }}>{it.crm_client ? GET_COOP_BUS_TO_SIMPLIFY(it.crm_client?.coop_bus) : ''}</span>
                      </div>
                    )
                  }))}
                  showSearch
                  optionFilterProp='searchLabel'
                  dropdownMatchSelectWidth={760}
                  onChange={(value, option: any) => {
                    setListData(listData.map((item, idx) => {
                      if (record.id === item.id) {
                        item.company_name = value;
                        item.client_status = CUSTOMER_STATUS_MAP[option.crm_client?.client_status] || '';
                        item.coop_bus = GET_COOP_BUS_TO_SIMPLIFY(option.crm_client?.coop_bus || '') || '';
                      }
                      return item
                    }))
                  }}
                />
              ) : (
                <Tooltip title={record.company_is_msg}>
                  <div style={{ color: '#f5222d' }}>{text}</div>
                </Tooltip>
              )
            }
            {
              record.company_is_edit ? (
                <>
                  <Button style={{ padding: '0 3px' }} type="link" onClick={async () => {

                    setListData(listData.map((item, idx) => {
                      if (record.id === item.id) {
                        item.company_is_edit = false;
                        if (item.company_name !== 'Match Failed') {
                          item.company_is_correct = true;
                          item.company_is_msg = '';
                        }
                      }
                      return item
                    }))
                  }}>Save</Button>

                </>
              ) : (
                <>
                  <Button type='link' style={{ marginLeft: 3 }} onClick={() => {
                    setListData(listData.map((item, idx) => {
                      if (record.id === item.id) {
                        item.company_is_edit = true;
                      }
                      return item
                    }))
                  }}>Search</Button>
                  {
                    record.company_name === 'Match Failed' && (
                      <Button type='link' onClick={() => {
                        curCreateIndex = index;
                        curClickCreator = record.creator;
                        if (!record.creator_is_correct) {
                          message.error('Creator is not matched, Please re-select the creator from system.')
                          return;
                        }
                        tableProps.onCreate({
                          company_name: record.ori_company_name,
                        })
                      }}>Create</Button>
                    )
                  }
                </>


              )
            }
          </div>
        )
      }
    },
    {
      dataIndex: 'client_status',
      title: 'Customer Status',
      width: 150,
      render(text) {
        return text;
      }
    },
    {
      dataIndex: 'coop_bus',
      title: 'Cooperation Type',
      render(text) {
        return text
      }
    },
    {
      dataIndex: 'content',
      title: 'Content',
      ellipsis: true,
      render(content) {
        return (
          <Popover placement="topLeft" content={<div dangerouslySetInnerHTML={{ __html: content }}></div>}>
            {content}
          </Popover>
        )
      }
    },
    {
      dataIndex: 'creator',
      title: 'Creator',
      width: 300,
      render(text, record) {
        if (record.creator_is_correct) {
          return (
            <Tooltip title={text}>
              {text}
            </Tooltip>
          );
        }
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {
              record.creator_is_edit ? (
                <Select
                  style={{ width: 250 }}
                  value={text}
                  options={creatorList}
                  showSearch
                  optionFilterProp='label'
                  dropdownMatchSelectWidth={200}
                  onChange={(value, option: any) => {
                    setListData(listData.map((item, idx) => {
                      if (record.id === item.id) {
                        item.creator = option.label;
                      }
                      return item
                    }))
                  }}
                />
              ) : (
                <Tooltip title="Creator Name format is not matched, Please re-select the name from system.">
                  <div style={{ color: '#f5222d' }}>{text}</div>
                </Tooltip>
              )
            }
            {
              record.creator_is_edit ? (
                <>
                  <Button style={{ padding: '0 3px' }} type="link" onClick={async () => {

                    setListData(listData.map((item, idx) => {
                      if (record.id === item.id) {
                        item.creator_is_edit = false;
                        if (item.creator !== creatorBeforeValue) {
                          item.creator_is_correct = true;
                          item.creator_is_msg = '';
                        }
                      }
                      return item
                    }))
                  }}>Save</Button>

                </>
              ) : (
                <EditOutlined style={{ cursor: 'pointer', marginLeft: 8 }} onClick={() => {
                  creatorBeforeValue = text;
                  setListData(listData.map((item, idx) => {
                    if (record.id === item.id) {
                      item.creator_is_edit = true;
                    }
                    return item
                  }))
                }} />
              )
            }
          </div>
        )
      }
    }
  ]

  const onOk = async () => {

    const isCorrect = listData.every(item => item.company_is_correct && item.creator_is_correct);
    if (!isCorrect) {
      const modalValidate = () => {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: 'Warning',
            content: 'Are you sure to save the records? the unmatched record will be ingored.',
            className: "follow-up-record-import-preview-modal",
            onOk: () => resolve(true),
            onCancel: () => reject(false)
          })
        })
      }
      const result = await modalValidate();
      if (!result) {
        return;
      }
    }

    setConfirmLoading(true);

    const resultList = listData.filter(it => it.company_is_correct && it.creator_is_correct);
    if (resultList.length === 0) {
      message.error('No valid data to import')
      setConfirmLoading(false);
      return;
    }
    const formType = form.getFieldValue('type');
    const { isSuc, values } = beforeHandleCreate({
      list: resultList.map(it => ({
        company_name: it.company_name,
        content: it.content,
        data_creator: it.creator,
        type: formType
      })),
      clueList
    })
    if (!isSuc) {
      setConfirmLoading(false);
      return;
    }
    try {
      await request.post('/followUpRecord', values)
      message.success('Import successfully')
      onCancel(true);
    } finally {
      setConfirmLoading(false);
    }

  }

  useEffect(() => {
    setListData(dataSource)
  }, [])
  return (
    <Modal
      title="Import Content Preview"
      width={1200}
      onCancel={onCancel}
      open={true}
      confirmLoading={confirmLoading}
      onOk={onOk}
      maskClosable={false}
    >
      <Form form={form} initialValues={{ type: 1 }}>
        <Form.Item label="Type" name="type">
          <Select options={FOLLOW_UP_RECORD_TYPE} style={{ width: 220 }} showSearch optionFilterProp='label' />
        </Form.Item>
      </Form>
      <Table
        scroll={{ x: '100%', scrollToFirstRowOnChange: true }}
        columns={columns}
        dataSource={listData}
        pagination={false}
      />

      <CreateModal
        modalType='drawer'
        title="Create"
        columns={tableProps.columns}
        itemData={tableProps.itemData}
        visible={tableProps.createModalVisible}
        setVisible={tableProps.setCreateModalVisible}
        onChange={(values) => {
          const creatorId = creatorList.find(it => it.label === curClickCreator)?.id;
          return tableProps.handleCreate({
            ...values,
            creator: curClickCreator,
            creator_id: creatorId
          })
        }}
        width={940}
        layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
        style={{ top: '10px' }}
        isHiddenConfirm={tableProps.isHiddenConfirm}
        customBtnCreate={tableProps.customBtnCreate}
        confirmText={'Save'}
        customRenderFormContent={tableProps.customRenderFormContent}
        maskStyle={tableProps.maskStyle}
        extra={tableProps.extra}
        formListConfig={tableProps.formListConfig}

      />
    </Modal>
  )
}
