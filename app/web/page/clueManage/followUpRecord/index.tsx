import React, { useEffect, useState } from 'react';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import moment from 'moment';
import request from '@/modules/request';
import { Button, Checkbox, Col, Form, Input, message, Modal, Popover, Row, Select, Space, Table, Tag, Tooltip, Upload } from 'antd';
import BasePageHoc from '@/component/base-page-hoc'
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import useAppStore, { getActivity } from '@/store/app.store';
import { UploadOutlined } from '@ant-design/icons'

import { CUSTOMER_STATUS_MAP, FOLLOW_UP_KEY_INFO, FOLLOW_UP_RECORD_TYPE, GET_COOP_BUS_TO_SIMPLIFY, OPTION_SEPARATOR, userISDemoAccount, userIsMarketer } from '@@/lib/constant';
import { Editor } from '@/component';
import UploadInput from '@/component/upload-file';
import { FlatTableActionType } from '@flat-design/components-pc';
import { CUSTOMER_COOP_TYPE } from '@/page/customerManage/hook/contact';
import useUserStore from '@/store/user.store';
import OperateAccess from '@/component/auth/operate-access';
import { useUploadProps } from '@/hooks';
import ImportPreview from './components/ImportPreview';
import { beforeHandleCreate } from './hook';
import { deduplicateArray } from '@@/lib/tool';
function ActivityRecord() {
  const { ADV_BD_USERS, ADV_OP_USERS, PUB_BD_USERS, PUB_OP_USERS } = useAppStore(useSelector(['ADV_BD_USERS', 'PUB_BD_USERS', 'ADV_OP_USERS', 'PUB_OP_USERS']));
  const curUserInfo = useUserStore(state => state);
  const followUpUsers = deduplicateArray([
    ...ADV_BD_USERS,
    ...ADV_OP_USERS,
    ...PUB_BD_USERS,
    ...PUB_OP_USERS
  ], 'label').map(it => ({
    label: it.label,
    value: it.label,
    id: it.value
  }))
  const userIsMKT = userIsMarketer(curUserInfo);
  const [uploadLoading, setUploadLoading] = useState(false);

  const [clueList, setClueList] = useState<Record<string, any>[]>([]);
  const [companyClientInfo, setCompanyClientInfo] = useState({
    client_status: '',
    coop_bus: ''
  });
  const [importPreviewInfo, setImportPreviewInfo] = useState({
    visible: false,
    itemData: {}
  });
  const columns = useFlatTableColumns([
    {
      key: 'date',
      hidden: true,
      filter: {
        type: 'RangePicker',
        allowClear: false,
        picker: "date",
        title: 'Update Time'
      },
      // @ts-ignore
      mapToData: ['startDate', 'endDate', 'YYYY-MM-DD'],
      update: true,
      checked: false
    },

    {
      key: 'id',
      width: 60,
      index: 1,
    },
    {
      key: 'company_name',
      index: 2,
      width: 250,
      filter: {
        type: 'Select',
        options: clueList,
        mode: 'multiple',
        maxTagCount: 3,
        // options: clueList.map((item) => ({
      },
      create: {
        type: 'Component',
        pureComponent: true,
        span: 24,
        render(form, value, itemName, isListIndex) {
          return (
            <Form.Item label="Company" required labelCol={{ span: 3 }}>
              <Row gutter={[16, 16]}>
                <Col span={18}>
                  <Form.Item name={itemName} rules={[{ required: true, message: 'Please enter the company name' }]}>
                    <Select options={clueList} showSearch optionFilterProp="label" placeholder="Please select company" onChange={(value, option: any) => {
                      const crmClient = option.crm_client;

                      setCompanyClientInfo({
                        client_status: CUSTOMER_STATUS_MAP[crmClient?.client_status] || '',
                        coop_bus: GET_COOP_BUS_TO_SIMPLIFY(crmClient?.coop_bus || '') || ''
                      })
                    }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={[isListIndex || 0, 'key_info']} valuePropName="checked">
                    <Checkbox >
                      Key Info
                    </Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ marginTop: 4 }}>
                <Col span={8}>
                  {companyClientInfo.client_status ? `Customer Status: ${companyClientInfo.client_status}` : ''}
                </Col>
                <Col span={8} style={{ whiteSpace: 'nowrap' }}>
                  {companyClientInfo.coop_bus ? `Cooperation Type: ${companyClientInfo.coop_bus}` : ''}
                </Col>
              </Row>
            </Form.Item>
          )
        }
      },
      update: {
        type: 'Component',
        pureComponent: true,
        span: 24,
        render(form, value) {
          return (
            <Form.Item label="Company" required labelCol={{ span: 3 }}>
              <Row gutter={[16, 16]}>
                <Col span={18}>
                  <Form.Item name={'company_name'} rules={[{ required: true, message: 'Please enter the company name' }]}>
                    <Select options={clueList} showSearch optionFilterProp="label" placeholder="Please select company" onChange={(value, option: any) => {
                      const crmClient = option.crm_client;

                      setCompanyClientInfo({
                        client_status: CUSTOMER_STATUS_MAP[crmClient?.client_status] || '',
                        coop_bus: GET_COOP_BUS_TO_SIMPLIFY(crmClient?.coop_bus || '') || ''
                      })
                    }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={'key_info'} valuePropName="checked">
                    <Checkbox >
                      Key Info
                    </Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ marginTop: 4 }}>
                <Col span={8}>
                  {companyClientInfo.client_status ? `Customer Status: ${companyClientInfo.client_status}` : ''}
                </Col>
                <Col span={8} style={{ whiteSpace: 'nowrap' }}>
                  {companyClientInfo.coop_bus ? `Cooperation Type: ${companyClientInfo.coop_bus}` : ''}
                </Col>
              </Row>
            </Form.Item>
          )
        }
      },
      render(value, record) {
        return `${record.clue_id}:${value}`
      }
    },
    {
      key: 'type',
      hidden: true,
      filter: {
        type: 'Select',
        options: FOLLOW_UP_RECORD_TYPE
      },
      create: {
        span: 24,
        labelCol: { span: 3 },
        type: 'Select',
        options: FOLLOW_UP_RECORD_TYPE,
        formItemProps: {
          required: true,
        }
      },
      update: true
    },

    {
      key: 'content',
      index: 3,
      width: 600,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Component',
        span: 24,
        labelCol: { span: 3 },
        formItemProps: {
          required: true,
        },
        render(form, value, itemName) {
          return <Editor value={value} onChange={(val) => {
            form.setFieldValue(itemName, val)
          }} />
        }
      },
      update: true,
      ellipsis: true,
      render(content, record) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        const contentFormat = doc.body.textContent
        return (
          <>
            {record.key_info === 1 ? <Tag color="red">Key</Tag> : null}
            <Popover placement="topLeft" content={<div dangerouslySetInnerHTML={{ __html: content }}></div>}>
              {contentFormat}
            </Popover>
          </>
        )
      }
    },
    {
      key: 'key_info',
      hidden: true,
      filter: {
        type: 'Select',
        options: FOLLOW_UP_KEY_INFO
      }
    },
    {
      key: 'creator',
      index: 4,
      width: 160,
      filter: {
        type: 'Select',
        options: followUpUsers,
        mode: 'multiple',
        maxTagCount: 3
      }
    },
    {
      key: 'utime',
      index: 5,
      width: 120,
      render(time) {
        return moment(time).format('YYYY-MM-DD')
      }
    },

    {
      key: 'client_status',
      width: 170,
      index: 6,
      filter: {
        type: 'Select',
        options: Object.entries(CUSTOMER_STATUS_MAP).map(([key, value]) => ({
          label: value,
          value: key
        }))
      },
      render(text, record) {
        if (record.record_type !== 'client') { return };
        return CUSTOMER_STATUS_MAP[record.client?.client_status]
      }
    },
    {
      key: 'client_type',
      index: 7,
      width: 180,
      filter: {
        hidden: userIsMKT,
        type: 'Cascader',
        multiple: true,
        options: CUSTOMER_COOP_TYPE,
        maxTagCount: 3
      },
      render(text, record) {
        if (record.record_type !== 'client') { return };
        return GET_COOP_BUS_TO_SIMPLIFY(record.client?.coop_bus || '')
      }
    },
    {
      key: 'attachment',
      index: 8,
      width: 200,
      ellipsis: true,
      create: {
        type: 'Component',
        span: 24,
        labelCol: { span: 3 },
        render(form, value, itemName) {
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={tableProps.itemData}
              setItemData={tableProps.setItemData}
              isLargeMode={false}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg"
              onChange={(fileList) => {
                form.setFieldValue(itemName, fileList)
              }}
            />
          )
        }
      },
      update: true,
      render(attachment) {
        if (!attachment) { return null };
        const urls = attachment.split(',')
        if (urls.length === 1) {
          const name = urls[0].split('/').pop();
          return (
            <a href={urls[0]} target="_blank">
              <Tooltip title={name}>
                {name}
              </Tooltip>
            </a>
          )
        }

        return (
          <Popover content={urls.map(it => <a style={{ display: 'block' }} href={it} target="_blank">{it.split('/').pop()}</a>)}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '180px' }}>
              {urls.map(it => it.split('/').pop()).join(',')}
            </div>
          </Popover>
        )
      }
    }

  ],
    {
      Fields: {
        id: 'ID',
        data: 'Update Time',
        company_name: 'Company',
        content: 'Content',
        creator: 'Creator',
        utime: 'Update Time',
        client_status: 'Customer Status',
        client_type: 'Cooperation Type',
        attachment: 'Attachment',

        type: 'Type',
        key_info: 'Key Info'
      }
    }
  );

  const handlerEditData = (record: Recordable) => {
    const newRecord = { ...record }
    newRecord.key_info = !!newRecord.key_info;
    const oriCompanyName = newRecord.company_name;
    const clueDetail = clueList.find(item => item.company_name === oriCompanyName);
    newRecord.company_name = `${clueDetail?.id}:${oriCompanyName}`;
    if (newRecord.attachment) {
      newRecord.attachment = newRecord.attachment.split(',').map((src, idx) => {
        const name = src.split('/').pop();
        return {
          url: src,
          name: name,
          uid: idx,
          status: 'done'
        }
      })
    }

    setCompanyClientInfo({
      client_status: CUSTOMER_STATUS_MAP[clueDetail?.crm_client?.client_status] || '',
      coop_bus: GET_COOP_BUS_TO_SIMPLIFY(clueDetail?.crm_client?.coop_bus || '') || ''
    })

    return newRecord
  }

  const uploadProps = {
    ...useUploadProps('/api/followUpRecord/importLeadDataAndVerify'),
    beforeUpload(file) {
      setUploadLoading(true);

      return true
    },
    onChange(info: Recordable) {
      if (!info.file?.response) { return }
      if (!info.file?.response?.isSuccess) {
        message.error(info.file?.response?.msg || 'Upload failed');
        setUploadLoading(false);
        return
      }

      setUploadLoading(false);
      const { result } = info.file.response.data;


      setImportPreviewInfo({
        visible: true,
        itemData: result.map((it, idx) => {

          const findCompany = clueList.filter(item => item.company_name.toLowerCase().includes(it.Company.toLowerCase()));
          const findCreator = followUpUsers.find(item => item.label.toLowerCase() === it.Creator.toLowerCase())
          const result: Recordable = {
            company_name: it.Company,
            ori_company_name: it.Company,
            creator: findCreator?.label || it.Creator,
            content: it.Content,
            id: idx,
            company_is_edit: false,
            creator_is_edit: false,
            company_is_correct: findCompany.length === 1,
            creator_is_correct: !!findCreator,
          }
          if (findCompany.length >= 1) {
            const firstCompany = findCompany[0];
            result.company_name = `${firstCompany.id}:${firstCompany.company_name}`;
            if (findCompany.length > 1) {
              result.company_is_msg = 'Multil Company matched, Please re-select the company from system.'
            }
            result.client_status = CUSTOMER_STATUS_MAP[firstCompany.crm_client?.client_status] || '';
            result.coop_bus = GET_COOP_BUS_TO_SIMPLIFY(firstCompany.crm_client?.coop_bus || '') || '';
          } else if (!findCompany.length) {
            result.company_is_msg = 'No company matched, Please re-select the company from system.'
            result.company_name = 'Match Failed'
          }
          return result;
        })
      })
    }
  }

  const tableProps = useFlatTable({
    title: 'Follow Up List',
    fetchUrl: '/followUpRecord',
    pageSize: 20,
    columns,
    actionWidth: 200,
    defaultParams: {
      // 默认日期， 最近30天
      date: [moment().subtract(1, 'months'), moment()]
    },
    filterBeforeReset: () => {
      return {
        date: [moment().subtract(1, 'months'), moment()]

      }
    },
    // historyModalConfig: ({ itemData, historyModalVisible }) => ({
    //   customColumns: useHistoryModalColumns({ visible: historyModalVisible, id: itemData.id, pathname: '/operation/user-manage/user' }),
    //   fetchApi: {
    //     listData: '/log?pathname=/operation/user-manage/user',
    //   },
    // }),
    actions: [
      FlatTableActionType.Edit,
      FlatTableActionType.Copy,
      FlatTableActionType.Delete,
    ],
    btnGroup: [
      BtnGroupType.Create,
      <OperateAccess.Create>
        <Upload {...uploadProps}>
          <Button type='primary' loading={uploadLoading} icon={<UploadOutlined />} onClick={() => {
            // setShowUploadModal({ visible: true, itemData: {} })
          }}>
            Upload
          </Button>
        </Upload>

      </OperateAccess.Create>,
      BtnGroupType.Export,
      BtnGroupType.CustomColumn
    ],
    beforeFilter(filters) {
      for (const key in filters) {
        if (!Object.prototype.hasOwnProperty.call(filters, key)) { continue };
        if (Array.isArray(filters[key])) {
          if (key === 'conversion_time') {
            filters.conversion_time = filters.conversion_time.map((it, index) => moment(it).format('YYYY-MM-DD HH:mm:ss')).join(OPTION_SEPARATOR)
          } else if (key === 'ctime') {
            filters.ctime = filters.ctime.map((it, index) => moment(it).format('YYYY-MM-DD HH:mm:ss')).join(OPTION_SEPARATOR)
          } else {
            filters[key] = filters[key].join(OPTION_SEPARATOR)

          }
        }
      }
      return filters
    },
    beforeCreate() {
      return {
        list: [{
          content: '',
        }]
      }
    },
    async beforeCopy(record) {
      const newRecord = handlerEditData(record);
      const res: any = {
        list: [{
          company_name: newRecord.company_name,
          type: newRecord.type,
          content: newRecord.content,
          key_info: newRecord.key_info,
          attachment: newRecord.attachment
        }]
      }
      res.attachment = newRecord.attachment
      return res;
    },
    async beforeEdit(record) {

      const newRecord = handlerEditData(record)
      return newRecord
    },
    async beforeHandleCreate(params) {
      return beforeHandleCreate({
        list: params.list,
        clueList
      })
    },
    async beforeHandleUpdate(params) {
      if (!params.content) {
        message.error('content is empty');
        return {
          isSuc: false,
          values: params
        }
      }
      const [clueId, companyName] = params.company_name.split(':');
      const clueDetail = clueList.find(item => Number(item.id) === Number(clueId))!;
      if (params.attachment) {
        params.attachment = params.attachment.map(it => it.url).join(',')
      }
      params.clue_id = clueId;
      params.record_type = clueDetail.crm_client ? 'client' : 'clue'
      params.relation_id = clueDetail.crm_client ? clueDetail.crm_client.id : clueId
      params.company_name = companyName;
      return {
        isSuc: true,
        values: params
      }
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/followUpRecord/export', {
          ...params,
          sheetName: 'Follow Up Record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'follow_up_record.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
  });

  const getClueCompany = async () => {
    const res = await request.get('/clue/getClueCompany');
    const clueList = res.data || [];
    let result = clueList.map((item) => ({
      ...item,
      label: `${item.id}:${item.company_name}`,
      value: `${item.id}:${item.company_name}`,
    }));
    if (userISDemoAccount(curUserInfo as any)) {
      result = result.filter(it => it.company_name === "DEMO_DSP")
    }
    setClueList(result);
  }

  useEffect(() => {
    getClueCompany();
  }, []);

  return <section className="business-list-wrap">
    <FlatTable {...tableProps} formListConfig={{
      addBtnName: 'Add More',
      beforeCreate: () => {
        tableProps.setItemData({
          ...tableProps.itemData,
          attachment: []
        })
      }
    }}
    />
    {
      importPreviewInfo.visible && <ImportPreview
        onCancel={(isRefresh) => {
          setImportPreviewInfo({
            visible: false,
            itemData: {}
          })
          if (isRefresh) {
            tableProps.defaultFetchHangler();
          }
        }}
        clueList={clueList}
        dataSource={importPreviewInfo.itemData || []}
        creatorList={followUpUsers}
      />
    }


  </section>
}

export default BasePageHoc(ActivityRecord)
