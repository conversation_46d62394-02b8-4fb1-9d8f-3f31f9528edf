import { message } from "antd";

export const beforeHandleCreate = ({
  list,
  clueList
}) => {
  for (const item of list) {
    if (item.attachment) {
      item.attachment = item.attachment.map(it => it.url).join(',')
    }
    const [clueId, companyName] = item.company_name.split(':');
    item.clue_id = clueId;
    const clueDetail = clueList.find(item => Number(item.id) === Number(clueId))!;
    item.record_type = clueDetail?.crm_client ? 'client' : 'clue'
    item.relation_id = clueDetail?.crm_client ? clueDetail.crm_client.id : clueId
    item.company_name = companyName;
  }
  return {
    isSuc: true,
    values: list
  }
}
