import { CreateModal } from '@/component';
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import useAppStore from '@/store/app.store';
import { message } from 'antd';
import React from 'react';
import './index.scss'
interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  ids: number[]
  itemData: Record<string, any>
}

export default function (props: IProps) {
  const { BD_USERS, BD_ASSISTANT_USERS } = useAppStore(useSelector(['BD_USERS', 'BD_ASSISTANT_USERS', 'ACTIVITYS', 'ENUMS']));
  const columns = useFlatTableColumns([
    {
      key: 'bd_id',
      hidden: true,
      create: {
        type: 'Select',
        index: 1,
        options: BD_USERS,
        disabled: props.itemData.type === 'myself',
        formItemProps: {
          required: true,
        },
      },
    },
    {
      key: 'assist_id',
      create: {
        type: 'Select',
        index: 2,
        options: BD_ASSISTANT_USERS,
      },
    },
    {
      key: 'note',
      create: {
        type: 'TextArea',
        span: 24,
        labelCol: {
          span: 3
        }
      }
    }
  ], {
    Fields: {
      bd_id: 'BD',
      assist_id: 'BD Assistant',
      note: 'Note'
    }
  })

  const handleCreate = async (options) => {
    const res = await request.post('/clue/batchLeadAssignment', {
      ...options,
      ids: props.ids,
      type: props.itemData?.type
    });
    if (res.isSuccess) {
      message.success('Lead assigned successfully')
      props.setVisible(true)
    } else {
      message.error(res.msg)
    }

  }

  return (
    <CreateModal
      title={`Lead ${props.itemData?.type === 'transfer' ? 'Transfer' : props.itemData?.type === 'allot' ? 'Assignment' : 'To Myself'}（Lead Count: ${props.ids.length}）`}
      columns={columns}
      visible={props.visible}
      itemData={props.itemData}
      setVisible={(visible) => {
        props.setVisible(visible)
      }}
      onChange={handleCreate}
      width={910}
      style={{ top: '10px' }}
      modelWrapClassName='lead-pool-component-model-wrap'
      maskStyle={{ zIndex: 1001 }}
    />
  )
}
