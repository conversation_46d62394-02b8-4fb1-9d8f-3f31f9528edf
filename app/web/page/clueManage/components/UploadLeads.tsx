import { AppContext } from "@/store";
import { Button, Divider, Input, message, Modal, Select, Space, Spin, Table, UploadProps } from "antd";
import <PERSON>agger from "antd/lib/upload/Dragger";
import React, { useContext, useEffect, useRef, useState } from "react";
import { InboxOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons'
import XLSX from 'xlsx'
import useSelector from "@/hooks/useSelector";
import useAppStore from "@/store/app.store";
import { createLeadSource } from "../hook/contact";
import LoadingButton from "@/component/loading-button";
import { isNumber } from "@@/lib/tool";
import './UploadLeads.scss';

interface IProps {
  visible: boolean
  onClose: (isRefresh?: boolean) => void
}

const TEMPLATE_URL = 'https://static.flat-ads.com/flat-ads-crm-oms/crm_lead_upload_template_new-1733283403240.xls'

function UploadDissatisfyModal(props: IProps & { itemData: Record<string, any> }) {
  const dissatisflyData = props.itemData.dissatisflyData || [];
  const headers = props.itemData.headers || [];
  // const 
  const tableProps = {
    columns: headers.map(it => {
      const column: Record<string, any> = {
        title: it,
        dataIndex: it,
        key: it,
        align: 'center'
      }
      if (it === 'Verify Note') {
        column.render = (text) => {
          return <div style={{ color: '#ff6785' }}>{text}</div>
        }
      }
      return column;
    }),
    dataSource: dissatisflyData,
  }
  return (
    <Modal
      title="Failed to import data"
      width={1100}
      open={props.visible}
      onCancel={() => {
        props.onClose();
      }}
      onOk={() => {
        const newDissatisflyData = dissatisflyData.map(({ contact_type, ...otherData }) => {
          return {
            ...otherData
          }
        })
        const ws = XLSX.utils.json_to_sheet(newDissatisflyData, {
          header: [...headers],
        });
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        XLSX.writeFile(wb, "dissatisfyData.xlsx");
      }}
      okText="Download Failed Data"
    >
      <section className="section-item flat-table-main-header mb-0">
        <div className="operation-bar">
          <h2>Import the failed data list</h2>
        </div>
        <Table {...tableProps} />

      </section>
    </Modal>
  )
}

export default (props: IProps) => {
  const { ACTIVITYS, ENUMS } = useAppStore(useSelector(['ACTIVITYS', 'ENUMS']));

  const { csrf } = useContext(AppContext);
  const [leadSourceOptions, setLeadSourceOptions] = useState<any[]>([]);
  const inputRef = useRef(null);
  const [activityName, setActivityName] = useState('');
  const [leadSource, setLeadSource] = useState<undefined | string>(undefined);
  useEffect(() => {
    setLeadSourceOptions(createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || [])
  }, [ENUMS, ACTIVITYS])
  const [uploadLoading, setUploadLoading] = useState(false);
  const [dissatisfyModal, setDissatisfyModal] = useState({
    visible: false,
    itemData: {}
  });
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/clue/importLeadDataAndVerify', // 校验Excel路由
    accept: '.xlsx, .xls',
    headers: {
      'X-XSRF-TOKEN': csrf
    },
    data: {
      clue_from: leadSource,
      active_id: isNumber(leadSource) ? leadSource : null,
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('Image must smaller than 2MB!');
      }
      const valid = isLt2M;
      if (valid) {
        setUploadLoading(true);
      }
      return valid
    },
    onChange(info) {
      if (!info.file?.response) { return }
      if (!info.file?.response?.isSuccess) {
        message.error(info.file?.response?.msg || 'Upload failed');
        setUploadLoading(false);
        return
      }

      setUploadLoading(false);
      const result = info.file.response.data;
      if (!result.dissatisfyData?.length) {
        // 成功了
        props.onClose(true);
        message.success('Upload success');
        return
      }

      // 总数：xx, 成功的：xx，失败的：xx
      message.success(`Upload success. total: ${result.total}, success: ${result.success}, failed: ${result.dissatisfyData?.length}`, 6);
      // 如果有失败的，显示失败列表弹窗
      setDissatisfyModal({
        visible: true,
        itemData: {
          dissatisflyData: result.dissatisfyData,
          headers: result.headers
        }
      });


    },
    itemRender: () => null,
  }
  return (
    <Modal width={700} footer={null} open={props.visible} title="Upload Leads" onCancel={() => {
      props.onClose()
    }}>
      <Spin spinning={uploadLoading}>

        <div style={{ marginBottom: 12 }}>
          <div>Please Note: </div>
          <div>1. Supports xls and xlsx format files within 2MB. It is recommended that you use standard templates</div>
          <div>2. The data in the file cannot exceed 5000 rows and 50 columns.</div>
        </div>
        <Button type="primary" icon={<DownloadOutlined />}>
          <a style={{ marginLeft: 4 }} target="_self" href={TEMPLATE_URL} download="crm_lead_upload_template">
            Download Standard Templates
          </a>
        </Button>
        <Divider />

        <div>
          <div style={{ marginBottom: 12 }}>
            <span className="lead-source-required-icon">*</span>
            <span style={{ marginRight: 6 }}>Lead Source</span>
            <Select
              style={{ width: '400px' }}
              options={leadSourceOptions}
              value={leadSource}
              placeholder="Please select lead source"
              showSearch
              optionFilterProp="label"
              onChange={(e) => {
                setLeadSource(e)
              }}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <Space className="custom-use-columns-space">
                    <Input
                      style={{ width: '100%' }}
                      placeholder="Please enter lead source name"
                      ref={inputRef}
                      value={activityName}
                      maxLength={30}
                      showCount
                      onChange={(e) => {
                        setActivityName(e.target.value)
                      }}
                    />
                    <LoadingButton icon={<PlusOutlined />} type="" api="/commonFilter/createEnum" method="post" params={{
                      enum_type: 'lead_source',
                      enum_code: activityName,
                    }} onOk={(e: any = {}) => {
                      const code = e.enum_code || '';
                      leadSourceOptions[0].options.push({ label: activityName, value: activityName });
                      setLeadSourceOptions([...leadSourceOptions])
                      setActivityName('');
                      setLeadSource(code);
                      setTimeout(() => {
                        // @ts-ignore
                        inputRef.current?.focus();
                      }, 0);
                    }}  >
                      Add Lead Source
                    </LoadingButton>
                  </Space>
                </>
              )}
            ></Select>
          </div>
          <Dragger {...uploadProps} disabled={!leadSource}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{
              !leadSource ? 'Please select first lead source' : 'Please click or drag the file here to upload.'
            }</p>
          </Dragger>
        </div>
      </Spin>

      <UploadDissatisfyModal
        visible={dissatisfyModal.visible}
        itemData={dissatisfyModal.itemData}
        onClose={() => {
          setDissatisfyModal({ visible: false, itemData: {} })
          setTimeout(() => {
            props.onClose(true)
          })
        }}
      />
    </Modal>
  )
}
