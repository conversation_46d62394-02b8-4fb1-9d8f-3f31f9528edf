import { CreateModal } from '@/component';
import React, { useEffect, useState } from 'react'
import { useCustomerClueColumns } from '../hook/useCustomerColumns';

export default function CustomerDetail({ visible, onClose, customerData }) {
  const [itemData, setItemData] = useState<Record<string, any>>({});

  const columns = useCustomerClueColumns({ itemData, setItemData })

  useEffect(() => {
    setItemData({
      attachment: customerData.attachment,
    });
  }, [customerData.attachment])

  return (
    <CreateModal
      modalType={'drawer'}
      title={`Customer Detail: ${customerData?.client_name}`}
      columns={columns}
      disabled={true}
      itemData={customerData}
      visible={visible}
      setVisible={() => {
        onClose()
      }}
      modalWidth={940}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
    />
  )
}
