import { Button, Col, Form, FormInstance, Input, Row, Select, Space } from "antd";
import React, { useEffect } from "react";
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'
interface IProps {
  form: FormInstance
  disabled?: boolean
  onlyKey?: string
  defaultValue?: null | { key: string, value: any }[]
}

const CONTACT_OPTIONS = [
  { label: 'Phone', value: 'Phone' }, { label: 'Email', value: 'Email' },
  { label: 'Wechat', value: 'Wechat' }, { label: 'Facebook', value: 'Facebook' },
  { label: 'Skype', value: 'Skype' }, { label: 'LinkedIn', value: 'LinkedIn' },
  { label: 'Whatsapp', value: 'Whatsapp' }, { label: 'Telegram', value: 'Telegram' },
  { label: 'LINE', value: 'LINE' }, { label: 'Viber', value: 'Viber' }
]

const KEY_PLACEMENT = {
  'Email': 'eg: <EMAIL>',
  'Phone': 'eg: 16019138159',
  'Wechat': 'eg: wechat1234',
}

export default function ContactInfomation({ form, disabled = false, onlyKey = '', defaultValue = null }: IProps) {
  useEffect(() => {
    if (!defaultValue) { return };
    const key = `contact_type${onlyKey ? `_${onlyKey}` : ''}`;
    form.setFieldsValue({ [key]: defaultValue });
  }, [defaultValue])
  const name = `contact_type${onlyKey ? `_${onlyKey}` : ''}`
  const detailWatch = Form.useWatch(name, form) || [];

  return (
    <Form.Item label="Contact Detail" labelCol={{ span: 4 }} >
      <Form.List name={`contact_type${onlyKey ? `_${onlyKey}` : ''}`} rules={[{
        async validator(rule, value = [], callback) {
          if (disabled) {
            return Promise.resolve();
          }
          for (let i = 0; i < value.length; i++) {
            const item = value[i];
            if (!item) { continue }

            if (!item.key && item.value) {
              return Promise.reject('Please input contact way');
            }

          }

          return Promise.resolve();
        },

      }]}>
        {(fields, { add, remove }, { errors }) => (
          <>
            {fields.map((key, name) => {
              const keyName = detailWatch[name]?.key || '';
              return (
                <React.Fragment>
                  <Row style={{ marginBottom: 10 }} gutter={12}>
                    <Col span={6}>
                      <Form.Item name={[name, 'key']} >
                        <Select placeholder="Contact Way" disabled={disabled} showSearch options={CONTACT_OPTIONS}></Select>

                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name={[name, 'value']} >
                        <Input placeholder={KEY_PLACEMENT[keyName] || 'input value'} disabled={disabled} onBlur={() => {
                          form.validateFields(['contact_type'])
                        }} />
                      </Form.Item>
                    </Col>
                    {
                      !disabled && (
                        <Col span={6}>
                          <Space>
                            <Button className="form-list-ant-btn" icon={<PlusOutlined />} onClick={() => {
                              add({
                                key: '',
                                value: ''
                              });
                            }}></Button>
                            <Button className="form-list-ant-btn" icon={<MinusOutlined />} onClick={() => remove(name)}></Button>

                          </Space>
                        </Col>
                      )
                    }

                  </Row>
                </React.Fragment>
              )
            })}
            {fields.length === 0 && !disabled && (
              <Button icon={<PlusOutlined />} onClick={() => add({
                key: '',
                value: ''
              })}>Create</Button>
            )}
            {
              !disabled && (
                <Form.ErrorList errors={errors} />
              )
            }
          </>
        )}
      </Form.List>
    </Form.Item>
  )
}
