import { CreateModal } from '@/component';
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import useAppStore from '@/store/app.store';
import { message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss'

interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  itemData: Record<string, any>
}

export default function (props: IProps) {
  const { ENUMS } = useAppStore(useSelector(['ENUMS']));
  const [state, setState] = useState<Record<string, any>>({});
  const formRef = useRef<any>();
  const columns = useFlatTableColumns([
    {
      key: 'reason',
      hidden: true,
      create: {
        type: 'Select',
        index: 1,
        span: 23,
        labelCol: { span: 6 },
        options: [...ENUMS.lead_conversion_failure, { label: 'Others (fill in the reason manually)', value: 'Other' }],
        formItemProps: {
          required: true,
        },
        onChange(value) {
          if (value === 'Other') {
            formRef.current.setForm({
              other: 'Others'
            })
          }
          setState({
            ...state,
            reason: value
          })
        }
      },
    },
    {
      key: 'other',
      create: {
        type: 'TextArea',
        index: 2,
        span: 23,
        hidden: state.reason !== 'Other',
        labelCol: { span: 6 },
        formItemProps: {
          required: state.reason !== 'Other',
        }
      }
    }
  ], {
    Fields: {
      reason: 'Failed Reason',
      other: ''
    }
  })

  const handleCreate = async (options) => {
    const { reason, other } = options
    const res = await request.post('/clue/clueFailureHandler', {
      clue_ids: props.itemData.ids,
      type: 'conversion_failed',
      reason: reason === 'Other' ? other : reason
    });
    if (res.isSuccess) {
      message.success('update success')
      props.setVisible(true)
      setState({})
    } else {
      message.error(res.msg)
    }

  }
  useEffect(() => {
    if (!props.visible) {
      setState({})
    }
  }, [props.visible])
  return (
    <CreateModal
      title={`Marking as 'Conversion Failed'`}
      columns={columns}
      ref={formRef}
      visible={props.visible}
      itemData={props.itemData}
      setVisible={(visible) => {
        props.setVisible(visible)
      }}
      onChange={handleCreate}
      width={600}
      style={{ top: '10px' }}
      modelWrapClassName='lead-pool-component-model-wrap'
      maskStyle={{ zIndex: 1001 }}
    />
  )
}
