import { CreateModal } from '@/component';
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import useAppStore from '@/store/app.store';
import { Form, FormInstance, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useCustomerClueColumns } from '../hook/useCustomerColumns';
import { beforeEditTransform } from '../hook/commonFunc';
import { getClientName } from '@/page/customerManage/hook/commonFunc';
import './index.scss'
interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  leadInfo: Record<string, any>
  onOk: () => void
}

function ConversionCustomer(props: IProps, ref) {
  const { CUSTOMER, REFRESH_DATA } = useAppStore(useSelector(['CUSTOMER', 'REFRESH_DATA']));
  const [customerModal, setCustomerModal] = useState({
    visible: false,
    itemData: {} as Record<string, any>
  });
  const cRef = useRef<{ form: FormInstance }>();
  const [typeModalItemData, setTypeModalItemData] = useState({
    type: 'create_new'
  });

  const [itemData, setItemData] = useState<Record<string, any>>({});

  const customerColumn = useCustomerClueColumns({ itemData, setItemData });

  const typeColumns = useFlatTableColumns([
    {
      key: 'type',
      create: {
        type: 'Radio',
        span: 24,
        labelCol: {
          span: 4
        },
        optionType: 'button',
        formItemProps: {
          required: true,
        },
        options: [
          { label: 'Create new customer', value: 'create_new' },
          { label: 'Associate existing customers', value: 'associate' },
        ],
        // @ts-ignore
        onChange(e) {
          const value = e.target.value;
          setTypeModalItemData({
            ...typeModalItemData,
            type: value
          })
        }
      }
    },
    {
      key: 'customer',
      create: {
        type: 'Select',
        index: 2,
        span: 24,
        options: CUSTOMER,
        hidden: typeModalItemData.type === 'create_new',
        labelCol: {
          span: 4
        },
        formItemProps: {
          required: true,
        }
      }
    }
  ], {
    Fields: {
      type: 'Operation Type',
      customer: 'Customer'
    }
  })

  const handleTypeModal = async (values) => {
    if (values.type === 'create_new') {
      let leadInfo = {};
      if (props.leadInfo.is_transformer === 'not_transformer') {
        leadInfo = props.leadInfo
      } else {
        leadInfo = beforeEditTransform(props.leadInfo);
      }
      setItemData(leadInfo)
      setCustomerModal({
        visible: true,
        itemData: {
          ...leadInfo,
          bd_id: props.leadInfo.bd_id || null,
          assist_id: props.leadInfo.assist_id || null,
          clue_id: props.leadInfo.id,
          client_status: 1,
          bu: 'Flat Ads',
          client_name: props.leadInfo.customer_name,
          company_name: props.leadInfo.company_name,

          // 线索的其他字段
          ori_company_name: props.leadInfo.company_name,
        }
      })
      props.setVisible(false);
      return
    }
    // 关联客户成功
    const result = await request.post('/clue/associatedCustomer', {
      clue_id: props.leadInfo.id,
      client_id: values.customer
    })
    if (result.isSuccess) {
      message.success('Convert to customer success')
      props.setVisible(false);
      props.onOk();
    }
  }

  const handleCreate = async (values) => {
    const newClientName = getClientName(values)
    values.client_name = newClientName;
    for (const key in values) {
      if (!Object.prototype.hasOwnProperty.call(values, key)) { continue };
      if (Array.isArray(values[key])) {
        values[key] = values[key].join(',')
      }
    }
    const result = await request.post('/clue/conversionCustomer', {
      clue_id: customerModal.itemData.clue_id,
      values
    });
    if (result.isSuccess) {
      setCustomerModal({
        visible: false,
        itemData: {}
      })
      props.onOk();
      message.success('Convert to customer success')
      REFRESH_DATA('customer')
    }
  };

  useImperativeHandle(ref, () => ({
    // changeVal 就是暴露给父组件的方法
    handleTypeModal
  }));

  useEffect(() => {
    if (props.visible) { return };
    setTypeModalItemData({
      type: 'create_new'
    })
  }, [props.visible])
  return (
    <>

      <CreateModal
        title="Create Customer"
        columns={typeColumns}
        visible={props.visible}
        itemData={{
          type: 'create_new'
        }}

        ref={cRef}
        watchKeys={['type']}
        setVisible={(visible) => {
          props.setVisible(visible)
        }}
        onChange={handleTypeModal}
        modelWrapClassName='lead-pool-component-model-wrap'
        maskStyle={{ zIndex: 1001 }}
      />

      <CreateModal
        modalType="drawer"
        title={`Convert to customer`}
        columns={customerColumn}

        visible={customerModal.visible}
        setVisible={() => {
          // props.setVisible(visible)
          setCustomerModal({
            visible: false,
            itemData: {}
          })
        }}
        itemData={customerModal.itemData}
        onChange={handleCreate}
        width={1000}
        layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      />
    </>
  )
}


export default forwardRef(ConversionCustomer);
