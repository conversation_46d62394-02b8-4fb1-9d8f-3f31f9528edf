import { CreateModal } from '@/component';
import request from '@/modules/request';
import { message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss'

interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  itemData: Record<string, any>
}

export default function (props: IProps) {
  const formRef = useRef<any>();

  const handleCreate = async () => {

    const res = await request.post('/clue/clueStatusToContacted', {
      clue_id: props.itemData.ids,
    })
    if (res.isSuccess) {
      message.success('Update success');
      props.setVisible(true)
    } else {
      message.error(res.msg)
    }

  }
  return (
    <CreateModal
      title={`Marking as "Contacted"`}
      columns={[]}
      visible={props.visible}
      ref={formRef}

      itemData={props.itemData}
      setVisible={(visible) => {
        props.setVisible(visible)
      }}
      onChange={handleCreate}
      width={600}
      style={{ top: '10px' }}
      modelWrapClassName='lead-pool-component-model-wrap'
      maskStyle={{ zIndex: 1001 }}
    />
  )
}
