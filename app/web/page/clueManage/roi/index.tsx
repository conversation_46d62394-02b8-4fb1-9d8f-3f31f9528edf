import React from 'react';
import {
  FlatTableActionType,
  FlatTableBtnGroupType,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import moment from 'moment';
import request from '@/modules/request';
import { message, Space, Tooltip } from 'antd';
import BasePageHoc from '@/component/base-page-hoc'
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import useAppStore, { getActivity } from '@/store/app.store';
import { createLeadSource } from '../hook/contact';
import { render$StringValue } from '@@/lib/tool';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { userISDemoAccount } from '@@/lib/constant';
import useUserStore from '@/store/user.store';
function ActivityRecord() {
  const { SET_ACTIVITYS, ACTIVITYS, ENUMS } = useAppStore(useSelector(['SET_ACTIVITYS', 'ACTIVITYS', 'ENUMS']));
  const curUserInfo = useUserStore(state => state);
  let leadSourceOptions = createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || [];;
  if (userISDemoAccount(curUserInfo as any)) {
    leadSourceOptions = createLeadSource([], ACTIVITYS.filter(it => it.value === 11))
  }
  const columns = useFlatTableColumns([
    {
      key: 'lead_source',
      align: 'center',
      width: 160,
      index: 1,
      fixed: 'left',
      filter: {
        type: 'Select',
        options: leadSourceOptions,
      },
      render(_, record: Recordable) {
        return ACTIVITYS.find(item => item.value === Number(record.lead_source))?.label || record.lead_source
      },
      export: {
        mapping: (leadSourceOptions).reduce((obj, item) => {
          item.options.forEach((opt) => {
            obj[opt.value] = opt.label;
          })
          return obj;
        }, {})
      }
    },

    {
      key: 'data',
      align: 'center',
      width: 200,
      index: 2,

      filter: {
        type: 'RangePicker',
        allowClear: false,
        picker: "month",
        title: 'Cooperation Amount Date'
      },
      // @ts-ignore
      mapToData: ['startDate', 'endDate', 'YYYY-MM'],
      update: true,
      checked: false
    },

    {
      key: 'leads_num',
      align: 'center',
      width: 130,
      index: 3,
    },
    {
      key: 'available_leads',
      align: 'center',
      width: 130,
      index: 4,
    },
    {
      key: 'available_rate',
      align: 'center',
      width: 130,
      index: 5,
    },
    {
      key: 'contact_successful',
      align: 'center',
      width: 150,
      index: 6,
    },
    {
      key: 'contact_success_rate',
      align: 'center',
      width: 130,
      index: 7,
    },
    // {
    //   key: 'contact_failed',
    //   align: 'center',
    //   width: 130,
    //   index: 8,
    // },
    // {
    //   key: 'failed_rate',
    //   align: 'center',
    //   width: 130,
    //   index: 9,
    // },
    // {
    //   key: 'business_matching',
    //   align: 'center',
    //   width: 150,
    //   index: 10,
    // },
    // {
    //   key: 'match_rate',
    //   align: 'center',
    //   width: 130,
    //   index: 11,
    // },
    {
      key: 'cover_to_customer',
      align: 'center',
      width: 170,
      index: 12,
      title: (
        <Space>
          Cover to customer
          <Tooltip title="Only new customers are counted, old customers associated with new leads will not be counted">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      ),
      export: {
        title: 'Cover to customer'
      }
    },
    {
      key: 'customer_rate',
      align: 'center',
      width: 130,
      index: 13,
    },
    {
      key: 'successful_cooperation',
      align: 'center',
      width: 200,
      index: 14,
      title: (
        <Space>
          Successful Cooperation
          <Tooltip title="Only new customers are counted, old customers associated with new leads will not be counted">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      ),
      export: {
        title: 'Successful Cooperation'
      }
    },
    {
      key: 'cooperation_rate',
      align: 'center',
      width: 140,
      index: 15,
    },

    {
      key: 'cooperation_amount',
      align: 'center',
      width: 160,
      index: 19,
      render: (text) => render$StringValue(text)
    },

    {
      key: 'leads_no',
      align: 'center',
      width: 210,
      index: 17,
      render(jsonString) {
        if (!jsonString) { return '-' };
        const json = JSON.parse(jsonString);
        const advGroup = (json.aff_adv || 0) + (json.adx_dem || 0);
        const pubGroup = (json.aff_pub || 0) + (json.adx_pub || 0);

        return `Adv_group: ${advGroup}  /  Pub_group: ${pubGroup}`
      }
    },
    {
      key: 'margin',
      align: 'center',
      width: 130,
      index: 18,
      checked: false,
      render: (text) => render$StringValue(text)
    },

    {
      key: 'activity_cost',
      align: 'center',
      width: 130,
      index: 19,
      render: (text, record) => {
        if (record.data === ` ~ `) {
          return '-'
        }
        return render$StringValue(text);
      }
    },

    {
      key: 'clue_roi',
      align: 'center',
      width: 130,
      index: 20,
      fixed: 'right',
      render(text, record) {
        if (record.data === ` ~ `) {
          return '-'
        }
        return text
      }
    },

  ],
    {
      Fields: {
        data: 'Activity Time',
        lead_source: 'Lead Source',
        leads_num: 'Leads Num',
        available_leads: 'Available Leads',
        available_rate: 'Available Rate',
        contact_successful: 'Followed Up',
        contact_success_rate: 'Follow-up Rate',
        contact_failed: 'Contact Failed',
        failed_rate: 'Failed Rate',
        business_matching: 'Business Matching',
        match_rate: 'Match Rate',
        cover_to_customer: 'Cover to customer',
        customer_rate: 'Customer Rate',
        successful_cooperation: 'Successful Cooperation',
        cooperation_rate: 'Cooperation Rate',
        cooperation_amount: 'Cooperation Amount',
        leads_no: 'Customer Num',
        margin: 'Margin',
        activity_cost: 'Activity Cost',
        clue_roi: 'Leads ROI',
      }
    }
  );

  const tableProps = useFlatTable({
    title: 'ROI Analysis',
    fetchUrl: '/clue-roi',
    pageSize: 20,
    columns,
    defaultParams: {
      // 默认日期， 最近30天
      data: [moment('2024-07'), moment().subtract(1, 'months')]
    },
    filterBeforeReset: () => {
      return {
        data: [moment('2024-07'), moment().subtract(1, 'months')]
      }
    },
    // historyModalConfig: ({ itemData, historyModalVisible }) => ({
    //   customColumns: useHistoryModalColumns({ visible: historyModalVisible, id: itemData.id, pathname: '/operation/user-manage/user' }),
    //   fetchApi: {
    //     listData: '/log?pathname=/operation/user-manage/user',
    //   },
    // }),
    actions: [],
    btnGroup: [
      BtnGroupType.Export,
      BtnGroupType.CustomColumn
    ],
    beforeFilter(params) {
      const { lead_source, ...otherParams } = params
      return {
        ...otherParams,
        clueFrom: lead_source
      };
    },
    async beforeEdit(record) {
      record.time_range = [moment(record.start_time), moment(record.end_time)];
      return record;
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/clue-roi/export', {
          ...params,
          sheetName: 'ROI Analysis',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'roi_analysis.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    async afterCreate() {
      const activitys = await getActivity();
      SET_ACTIVITYS(activitys);
    }
  });

  return <section className="business-list-wrap">
    <FlatTable {...tableProps} filterColumnNum={3} />
  </section>
}

export default BasePageHoc(ActivityRecord)
