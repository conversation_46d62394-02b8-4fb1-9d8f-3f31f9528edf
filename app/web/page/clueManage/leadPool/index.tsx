import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  FlatTableActionType,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import request from '@/modules/request';
import { Button, Dropdown, message } from 'antd';
import BasePageHoc from '@/component/base-page-hoc';
import { useLeadColumns } from '../hook/useColumns'
import { MoreOutlined, UploadOutlined, DownOutlined } from '@ant-design/icons'
import { AppContext } from '@/store';
import LeadAssignment from '../components/LeadAssignment';
import OperateAccess from '@/component/auth/operate-access'
import ConversionCustomer from '../components/ConversionCustomer';
import ConversionFailed from '../components/ConversionFailed';
import MarkingInvalid from '../components/MarkingInvalid';
import MarkingMismatch from '../components/MarkingMismatch';
import { userIsBD } from '@@/lib/constant';
import useUserStore from '@/store/user.store';
import FollowUpRecord from '@/component/follow-up-record'
import UploadLeads from '../components/UploadLeads';
import { beforeEditTransform, beforeSubmitHandler, createCommonTableProps, useCommonState, useCustomerDetailData } from '../hook/commonFunc'
import CustomerDetail from '../components/CustomerDetail';
import MarkingOther from '../components/MarkingOther';
import { clearUrlSearchEffect } from '@/hooks';
import MarkingContacted from '../components/MarkingContacted';

function LeadPool() {
  const { localeData } = useContext(AppContext);
  const searchParams = new URLSearchParams(location.search);
  const curUserInfo = useUserStore(state => state);
  const tableRef = useRef<any>(null)
  const USER_IS_BD = userIsBD(curUserInfo);
  const convertToCustomerRef = useRef<any>();

  const { customerDetail, setCustomerDetail, customClickHandler } = useCustomerDetailData()

  const tableProps = useFlatTable({
    title: 'Leads Pool',
    fetchUrl: '/clue',
    pageSize: 20,
    defaultParams: {
      clue_status: searchParams.get('status') || undefined,
      id: searchParams.get('clue_id') || undefined
    },
    columns: (data) => useLeadColumns(data, tableRef, 'pool', customClickHandler),
    actions: [
      FlatTableActionType.Edit,
      (record) => (
        <OperateAccess.Update>
          <Button type='primary' disabled={record.crm_client || (record.clue_status !== 'assigned' && record.clue_status !== 'contacted')} onClick={() => {
            setConversionCustomer({
              visible: true,
              itemData: {
                ...record
              }
            })
          }}>
            {localeData.ConvertToCustomer}
          </Button>
        </OperateAccess.Update>
      ),
      (record) => (
        <Dropdown onOpenChange={(open) => {
          if (open) {
            setCurDropdownRecord(record)
          }
        }} menu={{ items: statusMenu }}>
          <Button style={{ width: 'auto' }} icon={<MoreOutlined />}></Button>
        </Dropdown>
      )
    ],
    btnGroup: [
      BtnGroupType.Create,

      <OperateAccess.Create>
        <Button type='primary' icon={<UploadOutlined />} onClick={() => {
          setShowUploadModal({ visible: true, itemData: {} })
        }}>
          Upload
        </Button>
      </OperateAccess.Create>,
      (selectRows) => (
        <OperateAccess.Assign>
          <Button disabled={!selectRows.length} onClick={() => {
            for (const item of selectRows) {
              if (!['unassigned', 'assigned', 'conversion_failed'].includes(item.clue_status)) {
                message.warn('Only the status of "Unassigned" and "Assigned" and "Conversion Failed" can be converted to "Assigned"');
                return;
              }
            }
            const itemData: Record<string, any> = {
              type: 'allot'
            };
            setShowLeadAssignment({
              visible: true,
              ids: selectRows.map(it => it.id),
              itemData
            })
          }}>
            {localeData.LeadAssignment}
          </Button>
        </OperateAccess.Assign>
      ),
      (selectRows) => (
        <OperateAccess.Update>
          <Button disabled={!selectRows.length} onClick={() => {
            for (const item of selectRows) {
              if (!['unassigned', 'conversion_failed'].includes(item.clue_status)) {
                message.warn('Only the status of "Unassigned" and "Conversion Failed" can be converted to "Assigned"');
                return;
              }
              if (item.clue_status === 'conversion_failed' && item.bd_id) {
                message.warn("leads BD already exists ");
                return;
              }
            }
            const itemData: Record<string, any> = {
              type: 'myself'
            };
            if (USER_IS_BD) {
              itemData.bd_id = String(curUserInfo.id)
            }
            setShowLeadAssignment({
              visible: true,
              ids: selectRows.map(it => it.id),
              itemData
            })
          }}>
            Assign To Myself
          </Button>
        </OperateAccess.Update>
      ),
      (selectRows) => (
        <OperateAccess.Update>
          <Button disabled={!selectRows.length} onClick={() => {
            for (const item of selectRows) {
              if (!['unassigned', 'assigned'].includes(item.clue_status)) {
                message.warn('"Unassigned" / "Assigned" status is required to perform this operation');
                return;
              }
            }
            setMarkingOther({
              visible: true,
              itemData: {
                ids: selectRows.map(it => it.id),
              }
            })
          }}>
            Marking as 'Other'
          </Button>
        </OperateAccess.Update>
      ),
      BtnGroupType.BatchDelete,
      BtnGroupType.Export,
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    async beforeEdit(record) {
      const newRecord = beforeEditTransform(record);
      setCurEditRecordId(newRecord.id);
      setCurDropdownRecord(newRecord)
      return newRecord;
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);

        const res = await request.post('/clue/export', {
          ...params,
          sheetName: 'activity-record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'lead_pool.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    rowClassName: (record) => {
      if (record.creator_id === curUserInfo.id) {
        return 'self-creator-clue-row';
      }
      return ''
    },
    ...createCommonTableProps({ curUserInfo }),
  });
  const {
    showLeadAssignment,
    conversionCustomer,
    conversionFailed,
    markingInvalid,
    markingMismatch,
    markingOther,
    showFollowUpRecord,
    showUploadModal,
    statusMenu,
    editModalStatusMenu,
    setCurDropdownRecord,
    setCurEditRecordId,
    setConversionCustomer,
    editStatusChangeHandler,
    setShowLeadAssignment,
    setConversionFailed,
    setMarkingInvalid,
    setMarkingMismatch,
    setMarkingOther,
    setShowFollowUpRecord,
    setShowUploadModal,

    markingContacted,
    setMarkingContacted

  } = useCommonState({
    tableProps,
    tableRef
  })

  useEffect(() => {
    editStatusChangeHandler();
  }, [tableProps.listData]);

  clearUrlSearchEffect()

  useEffect(() => {
    const clueId = searchParams.get('clue_id');
    if (!clueId) { return; }
    tableProps.setParams({
      ...tableProps.params,
      id: clueId
    });
  }, [searchParams.get('clue_id')])


  return <section className="business-list-wrap lead-pool-wrap">
    {/* @ts-ignore */}
    <FlatTable {...tableProps} modelWrapClassName='lead-pool-model-wrap' isHiddenConfirm={tableProps.itemData.clue_status === 'deleted'} modalType="drawer" ref={tableRef} modalWidth={940} layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      confirmText='Save'
      customBtnCreate={(type, form, onFinish, loading) => {
        if (type === 'create') {
          return (
            <Button type='primary' loading={loading} onClick={async () => {

              const {
                values,
                result
              } = await onFinish({})
              setConversionCustomer({
                visible: false, // 不打开选择转化类型
                itemData: result
              })
              convertToCustomerRef.current.handleTypeModal({
                type: 'create_new'
              })
            }}>
              Save & Create Customer
            </Button>
          );
        }
        return (
          <OperateAccess.Update>
            <Dropdown onOpenChange={(open) => {

            }} menu={{ items: editModalStatusMenu }}>
              <Button >
                More Operation <DownOutlined />
              </Button>
            </Dropdown>

          </OperateAccess.Update>
        )
      }}
      updateModalClose={() => {
        setCurEditRecordId(null);
      }}
    />
    <LeadAssignment ids={showLeadAssignment.ids} itemData={showLeadAssignment.itemData} visible={showLeadAssignment.visible} setVisible={(isRefresh) => {
      setShowLeadAssignment({
        visible: false,
        ids: [],
        itemData: {}
      })
      if (isRefresh) {
        tableProps.defaultFetchHangler();
      }
      // setCurDropdo 
    }} />
    <ConversionCustomer
      ref={convertToCustomerRef}
      visible={conversionCustomer.visible}
      leadInfo={conversionCustomer.itemData}
      onOk={() => {
        tableProps.defaultFetchHangler();
      }}
      setVisible={(isRefresh) => {
        setConversionCustomer({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }} />

    <ConversionFailed
      visible={conversionFailed.visible}
      itemData={conversionFailed.itemData}
      setVisible={(isRefresh) => {
        setConversionFailed({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <MarkingInvalid
      visible={markingInvalid.visible}
      itemData={markingInvalid.itemData}
      setVisible={(isRefresh) => {
        setMarkingInvalid({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <MarkingMismatch
      visible={markingMismatch.visible}
      itemData={markingMismatch.itemData}
      setVisible={(isRefresh) => {
        setMarkingMismatch({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />

    <MarkingContacted
      visible={markingContacted.visible}
      itemData={markingContacted.itemData}
      setVisible={(isRefresh) => {
        setMarkingContacted({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />
    <MarkingOther
      visible={markingOther.visible}
      itemData={markingOther.itemData}
      setVisible={(isRefresh) => {
        setMarkingOther({
          visible: false,
          itemData: {}
        })
        if (isRefresh) {
          tableProps.defaultFetchHangler();
        }
      }}
    />
    <FollowUpRecord type='clue' itemData={showFollowUpRecord.itemData} visible={showFollowUpRecord.visible} onClose={() => {
      setShowFollowUpRecord({
        visible: false,
        itemData: {}
      })
    }} />

    <UploadLeads visible={showUploadModal.visible} onClose={(isRefresh) => {
      setShowUploadModal({
        visible: false,
        itemData: {}
      })
      if (isRefresh) {
        tableProps.defaultFetchHangler();
      }
    }} />


    <CustomerDetail visible={customerDetail.visible} onClose={() => {
      setCustomerDetail({
        visible: false,
        itemData: {}
      })
    }} customerData={customerDetail.itemData} />
  </section>
}

export default BasePageHoc(LeadPool)
