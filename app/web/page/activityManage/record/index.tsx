import React, { useState } from 'react';
import {
  FlatTableActionType,
  FlatTableBtnGroupType,
  FlatSwitchButton,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import moment from 'moment';
import request from '@/modules/request';
import { Button, message, Space } from 'antd';
import BasePageHoc from '@/component/base-page-hoc'
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import useAppStore, { getActivity } from '@/store/app.store';

function ActivityRecord() {
  const { SET_ACTIVITYS } = useAppStore(useSelector(['SET_ACTIVITYS']));

  const columns = useFlatTableColumns([
    {
      key: 'id',
      align: 'left',
      // @ts-ignore
      index: 0,
    },
    {
      key: 'name',
      align: 'left',
      filter: {
        type: 'Input',
        title: 'Name/ID'
      },
      index: 1,
      create: {
        type: 'Input',
        formItemProps: {
          required: true,
        },
        index: 1,
        // @ts-ignore
        maxLength: 256,
        showCount: true
      },
      update: true
    },
    {
      key: 'content',
      align: 'left',
      index: 2,
      filter: {
        type: 'Input'
      },
      create: {
        type: 'TextArea',
        formItemProps: {
          required: false,
        },
        index: 4,
        // @ts-ignore
        span: 24,
        labelCol: {
          span: 3
        },
        maxLength: 9999,
        // showCount: true
      },
      update: true
    },
    {
      key: 'time_range',
      align: 'center',
      // @ts-ignore
      index: 3,
      filter: {
        type: 'RangePicker',
        // defaultValue: [moment(), moment()],
        // format: 'YYYY-MM-DD',
      },
      create: {
        type: 'RangePicker',
        formItemProps: {
          required: true,
        },
        index: 3
      },
      // @ts-ignore
      mapToData: ['start_time', 'end_time'],
      update: true,
      render(_, record) {
        if (!record.start_time || !record.end_time) return null
        return `${record.start_time} ~ ${record.end_time}`
      }
    },
    {
      key: 'cost',
      align: 'right',
      // @ts-ignore
      index: 4,
      create: {
        type: 'InputNumber',
        min: 0,
        precision: 2,
        formItemProps: {
          required: true,
        },
        addonBefore: '$',
        index: 2
      },
      update: true
    },

  ],
    {
      Fields: {
        id: 'ID',
        name: 'Name',
        content: 'Content',
        time_range: 'Time',
        cost: 'Cost'
      }
    }
  );

  const tableProps = useFlatTable({
    title: 'Activity List',
    fetchUrl: '/activity/record',
    pageSize: 20,
    columns,
    // historyModalConfig: ({ itemData, historyModalVisible }) => ({
    //   customColumns: useHistoryModalColumns({ visible: historyModalVisible, id: itemData.id, pathname: '/operation/user-manage/user' }),
    //   fetchApi: {
    //     listData: '/log?pathname=/operation/user-manage/user',
    //   },
    // }),
    actions: [
      FlatTableActionType.Edit,
      FlatTableActionType.Delete,
    ],
    btnGroup: [
      FlatTableBtnGroupType.Create,
      FlatTableBtnGroupType.Export,
      FlatTableBtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    async beforeEdit(record) {
      record.time_range = [moment(record.start_time), moment(record.end_time)];
      return record;
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/activity/record/export', {
          ...params,
          sheetName: 'activity-record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'activity_record.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    async afterCreate() {
      const activitys = await getActivity();
      SET_ACTIVITYS(activitys);
    }
  });

  return <section className="business-list-wrap">
    {/* <div>
      <Button onClick={() => {
        request.post('/clue/runSchedule')
      }}>
        线索过期未分配提醒的机制
      </Button>
      <Button onClick={() => {
        request.post('/clue/runNotifySchedule')
      }}>
        审批修改文件上传通知
      </Button>
    </div> */}
    <FlatTable {...tableProps} />
  </section>
}

export default BasePageHoc(ActivityRecord)
