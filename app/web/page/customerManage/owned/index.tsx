import React, { useEffect, useState } from 'react';
import {
  // FlatTable,
  FlatTableActionType,
} from '@flat-design/components-pc';
import { Button, Col, Form, Input, message, Popconfirm, Row, Select } from 'antd';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import OperateAccess from '@/component/auth/operate-access';
import FollowUpRecord from '@/component/follow-up-record';
import request from '@/modules/request';
import UpdateBD from '../components/UpdateBD';
import ContactInfomation from '@/page/clueManage/components/ContactInfomation';
import UploadInput from '@/component/upload-file';
import { beforeEditHandler, commonTableProps, useCommonState } from '../hook/commonFunc';
import { useCustomerColumns } from '../hook/useColumns';
import { clearUrlSearchEffect } from '@/hooks';
import { createDefaultContractType, createLeadSource } from '@/page/clueManage/hook/contact';
import useSelector from '@/hooks/useSelector';
import useAppStore, { getCustomer } from '@/store/app.store';
import { CreateModal } from '@/component';
import { userIsBD } from '@@/lib/constant';
import useUserStore from '@/store/user.store';
import { useCreateClueAndCustomer } from '../hook/useCreateClueAndCustomer';


export default () => {
  const searchParams = new URLSearchParams(location.search);
  const { ACTIVITYS, ENUMS, SET_CUSTOMER } = useAppStore(useSelector(['ACTIVITYS', 'ENUMS', 'SET_CUSTOMER']));
  const curUserInfo = useUserStore(state => state);

  const [itemData, setItemData] = useState();
  const columns = useCustomerColumns({
    itemData,
    setItemData
  }, 'owned')

  const tableProps = useFlatTable({
    title: 'Customers List',
    fetchUrl: '/customer',
    lang: 'zh',
    actionWidth: 394,
    pageSize: 20,
    columns,
    defaultParams: {
      id: searchParams.get('client_id') || undefined,
      PAGE_TYPE: 'owned',
      client_status: searchParams.get('status') || undefined
    },
    actions: [
      FlatTableActionType.Edit,
      (record) => (
        <OperateAccess.Update>
          <Popconfirm
            disabled={record.client_status === 1}
            title="Confirm that the status changes to 【Cooperated】"
            onConfirm={async () => {
              return changeClientStatus(record.id, { client_status: 1 });
            }}
            okText="Confirm"
            cancelText="Cancel"
          >
            <Button type='primary' disabled={record.client_status === 1} onClick={() => {

            }}>
              Change To Cooperated
              {/* {localeData.ConvertToCustomer} */}
            </Button>
          </Popconfirm>

        </OperateAccess.Update>
      ),
      (record) => (
        <OperateAccess.Update>
          <Button type='primary' onClick={() => {
            setShowFollowUpRecord({
              visible: true,
              itemData: {
                ...record
              }
            })
          }}>Follow Up Records</Button>
        </OperateAccess.Update>
      )
    ],
    btnGroup: [
      <OperateAccess.Create>
        <Button type='primary' onClick={() => {
          if (!userIsBD(curUserInfo)) {
            message.warn('Only BD can create customers')
            return;
          }
          setCustomerLeadModal({
            visible: true,
            itemData: {
              contact_type: createDefaultContractType(),
              client_status: 1,
              lead_type: 'create_new_lead',
              bd_id: String(curUserInfo.id)
            }
          })
        }}>
          Create Customer
        </Button>
      </OperateAccess.Create>,
      (selectRows) => (
        <OperateAccess.Transfer>
          <Button type="primary" disabled={!selectRows.length} onClick={() => {
            for (const item of selectRows) {
              const mainInfo = item.crm_clues.find(it => it.clue_identity === 1);
              if (!mainInfo) {
                message.warn(`There is no main lead for the ${item.client_name}`);
                return;
              }
            }
            const masterCueId = selectRows.map(it => it.crm_clues.find(it => it.clue_identity === 1)?.id);
            setShowUpdateBD({
              visible: true,
              master_cue_ids: masterCueId,
              itemData: {}
            })

          }}>
            Customer transfer
          </Button>
        </OperateAccess.Transfer>
      ),
      BtnGroupType.Export,
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    async beforeEdit(values, index) {
      beforeEditHandler(values, setItemData)

      try {
        tableProps.listData[index].loading = true;
        tableProps.setListData([...tableProps.listData]);
        await handlerCurEditPoolInfos(values.id)
        return values;
      } finally {
        tableProps.listData[index].loading = false;
        tableProps.setListData([...tableProps.listData])
      }

    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/customer/export', {
          ...params,
          PAGE_TYPE: 'owned',
          sheetName: 'activity-record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'customer_list.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    ...commonTableProps
  });

  const { customerLeadColumn, customerLeadModal, setCustomerLeadModal, handleCreateClueAndCustomer } = useCreateClueAndCustomer({
    onSuccess: async (result) => {
      tableProps.defaultFetchHangler();
    },
    type: 'customer'
  })

  const {
    setShowUpdateBD,
    showFollowUpRecord,
    setShowFollowUpRecord,
    changeClientStatus,
    handlerCurEditPoolInfos,
    curEditPoolInfos,
    showUpdateBD
  } = useCommonState({
    tableProps
  });

  useEffect(() => {
    const type = searchParams.get('type');
    if (type === 'open_create') {
      setCustomerLeadModal({
        visible: true,
        itemData: {
          contact_type: createDefaultContractType(),
          client_status: 1,
          lead_type: 'create_new_lead',
          bd_id: String(curUserInfo.id)
        }
      })
    }
  }, [searchParams])

  clearUrlSearchEffect();

  return <section className="business-list-wrap">
    {/* @ts-ignore */}
    <FlatTable {...tableProps} modalType="drawer"
      modalWidth={980}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      customRenderFormContent={(type, form) => {
        if (type === 'create') {
          return null;
        }

        return curEditPoolInfos.map((item, index) => {
          let clueFrom = ''
          if (item.clue_from) {
            clueFrom = Number.isNaN(Number(item.clue_from)) ? item.clue_from : Number(item.clue_from)
          }
          return (
            <div className="group-section" key={item.id}>
              <div className='group-title' style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  {item.clue_identity === 1 ? 'Main' : 'Minor'} Lead {index + 1}：{item.company_name}
                </div>
                {
                  item.clue_identity === 2 && (
                    <div>
                      <OperateAccess.Destroy>
                        <Popconfirm
                          title="Are you sure you want to delete this relationship?"
                          okText="Confirm"
                          onConfirm={async () => {
                            await request.post('/customer/clientDeleteRelationship', {
                              clue_id: item.id
                            })
                            await handlerCurEditPoolInfos(tableProps.itemData.id);
                            message.success('Delete Relationship successfully');
                          }}
                        >
                          <Button danger>
                            Delete Relationship
                          </Button>
                        </Popconfirm>
                      </OperateAccess.Destroy>
                    </div>
                  )
                }

              </div>
              <div className="group-content">
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item label="Contact Name">
                      <Input disabled={true} value={item.connect_name} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Job Title">
                      <Input disabled={true} value={item.position} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Lead Source">
                      <Select disabled={true} value={clueFrom} options={
                        createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || []
                      }>

                      </Select>
                      {/* <Input disabled={true} value={item.position} /> */}
                    </Form.Item>
                  </Col>
                  {
                    item.contact_type && (
                      <Col span={24}>
                        <ContactInfomation
                          form={form} disabled={true}
                          onlyKey={(index + 1).toString()}
                          defaultValue={Object.entries(item.contact_type).map(
                            ([key, value]) => {
                              return { key, value }
                            }
                          )}
                        />
                      </Col>
                    )
                  }
                  {
                    item.attachment && (
                      <Col span={24}>
                        <Form.Item label="Contact attachment">
                          <UploadInput
                            label="attachment"
                            form={form}
                            itemData={{
                              attachment: item.attachment.split(',').map((src, idx) => {
                                const name = src.split('/').pop();
                                return {
                                  url: src,
                                  name: name,
                                  uid: idx
                                }
                              })
                            }}
                            setItemData={setItemData}
                            isLargeMode={false}
                            disabled={true}
                            accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg"
                          />
                        </Form.Item>
                      </Col>
                    )
                  }

                </Row>
              </div>
            </div>
          )
        })

      }}
    />

    <FollowUpRecord type='client' itemData={showFollowUpRecord.itemData} visible={showFollowUpRecord.visible} onClose={() => {
      setShowFollowUpRecord({
        visible: false,
        itemData: {}
      })
    }} />

    <UpdateBD title="Customer transfer" master_cue_ids={showUpdateBD.master_cue_ids} itemData={showUpdateBD.itemData} visible={showUpdateBD.visible} setVisible={(isRefresh) => {
      setShowUpdateBD({
        visible: false,
        master_cue_ids: [],
        itemData: {}
      })
      if (isRefresh) {
        tableProps.defaultFetchHangler();
      }
    }} />

    <CreateModal
      modalType="drawer"
      title={`Create Customer`}
      columns={customerLeadColumn}

      visible={customerLeadModal.visible}
      setVisible={() => {
        // props.setVisible(visible)
        setCustomerLeadModal({
          visible: false,
          itemData: {}
        })
      }}
      itemData={customerLeadModal.itemData}
      onChange={handleCreateClueAndCustomer}
      width={940}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
    />

  </section>
}
