import React, { useEffect, useState } from 'react';
import {
  // FlatTable,
  FlatTableActionType,
} from '@flat-design/components-pc';
import { Button, Col, Form, Input, message, Popconfirm, Row, Select } from 'antd';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import OperateAccess from '@/component/auth/operate-access';
import FollowUpRecord from '@/component/follow-up-record';
import request from '@/modules/request';
import moment from 'moment';
import UpdateBD from '../components/UpdateBD';
import ContactInfomation from '@/page/clueManage/components/ContactInfomation';
import UploadInput from '@/component/upload-file';
import { beforeEditHandler, commonTableProps, useCommonState } from '../hook/commonFunc';
import { useCustomerColumns } from '../hook/useColumns';
import useUserStore from '@/store/user.store';
import { clientRelatedFieldHandler } from '@@/lib/constant';
import { clearUrlSearchEffect } from '@/hooks';
import { createLeadSource } from '@/page/clueManage/hook/contact';
import useAppStore from '@/store/app.store';
import useSelector from '@/hooks/useSelector';


export default () => {
  const { ACTIVITYS, ENUMS } = useAppStore(useSelector(['ACTIVITYS', 'ENUMS']));

  const searchParams = new URLSearchParams(location.search);
  const curUserInfo = useUserStore(state => state);
  const [itemData, setItemData] = useState();
  const columns = useCustomerColumns({
    itemData,
    setItemData
  }, 'pool');

  const tableProps = useFlatTable({
    title: 'Customers List',
    fetchUrl: '/customer',
    lang: 'zh',
    actionWidth: 100,
    pageSize: 20,
    columns,
    defaultParams: {
      id: searchParams.get('client_id') || undefined
    },
    renderEditText() {
      return 'Detail'
    },
    actions: [
      FlatTableActionType.Edit,
      // (record) => (
      //   <OperateAccess.Update>
      //     <Popconfirm
      //       disabled={record.client_status === 1}
      //       title="Confirm that the status changes to 【Cooperated】"
      //       onConfirm={async () => {
      //         return changeClientStatus(record.id, { client_status: 1 });
      //       }}
      //       okText="Confirm"
      //       cancelText="Cancel"
      //     >
      //       <Button type='primary' disabled={record.client_status === 1}>
      //         Change To Cooperated
      //         {/* {localeData.ConvertToCustomer} */}
      //       </Button>
      //     </Popconfirm>

      //   </OperateAccess.Update>
      // ),
      // (record) => (
      //   <OperateAccess.Update>
      //     <Button type='primary' onClick={() => {
      //       setShowFollowUpRecord({
      //         visible: true,
      //         itemData: {
      //           ...record
      //         }
      //       })
      //     }}>Follow Up Records</Button>
      //   </OperateAccess.Update>
      // )
    ],
    btnGroup: [
      (selectRows) => (
        <OperateAccess.Assign>
          <Button type="primary" disabled={!selectRows.length} onClick={() => {
            for (const item of selectRows) {
              const mainInfo = item.crm_clues.find(it => it.clue_identity === 1);
              if (!mainInfo) {
                message.warn(`There is no main lead for the ${item.client_name}`);
                return;
              }
            }
            const masterCueId = selectRows.map(it => it.crm_clues.find(it => it.clue_identity === 1)?.id);
            setShowUpdateBD({
              visible: true,
              master_cue_ids: masterCueId,
              itemData: {}
            })

          }}>
            Customer handover
          </Button>
        </OperateAccess.Assign>
      ),
      BtnGroupType.Export,
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn
    ],
    async beforeEdit(values, index) {
      beforeEditHandler(values, setItemData)

      try {
        tableProps.listData[index].loading = true;
        tableProps.setListData([...tableProps.listData]);
        await handlerCurEditPoolInfos(values.id)
        return values;
      } finally {
        tableProps.listData[index].loading = false;
        tableProps.setListData([...tableProps.listData])
      }

    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/customer/export', {
          ...params,
          sheetName: 'activity-record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'customer_list.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    ...commonTableProps
  });

  const {
    setShowUpdateBD,
    showFollowUpRecord,
    setShowFollowUpRecord,
    changeClientStatus,
    handlerCurEditPoolInfos,
    curEditPoolInfos,
    showUpdateBD
  } = useCommonState({
    tableProps
  });

  clearUrlSearchEffect()


  return <section className="business-list-wrap">
    {/* @ts-ignore */}
    <FlatTable {...tableProps} isHiddenConfirm={true} modalType="drawer"
      modalWidth={980}
      layout={{ labelCol: { span: 24 }, layout: 'vertical' }}
      customRenderFormContent={(type, form) => {
        if (type === 'create') {
          return null;
        }

        return curEditPoolInfos.map((item, index) => {
          const clientRelatedFieldIsShow = clientRelatedFieldHandler(item, curUserInfo);
          let clueFrom = ''
          if (item.clue_from) {
            clueFrom = Number.isNaN(Number(item.clue_from)) ? item.clue_from : Number(item.clue_from)
          }
          return (
            <div className="group-section" key={item.id}>
              <div className='group-title' style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  {item.clue_identity === 1 ? 'Main' : 'Minor'} Lead {index + 1}：{item.company_name}
                </div>
                {
                  item.clue_identity === 2 && (
                    <div>
                      <OperateAccess.Destroy>
                        <Popconfirm
                          title="Are you sure you want to delete this relationship?"
                          okText="Confirm"
                          onConfirm={async () => {
                            await request.post('/customer/clientDeleteRelationship', {
                              clue_id: item.id
                            })
                            await handlerCurEditPoolInfos(tableProps.itemData.id);
                            message.success('Delete Relationship successfully');
                          }}
                        >
                          <Button danger>
                            Delete Relationship
                          </Button>
                        </Popconfirm>
                      </OperateAccess.Destroy>
                    </div>
                  )
                }

              </div>
              <div className="group-content">
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item label="Contact Name">
                      {
                        clientRelatedFieldIsShow ? (
                          <Input disabled={true} value={item.connect_name} />
                        ) : (
                          '-'
                        )
                      }
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Job Title">
                      {
                        clientRelatedFieldIsShow ? (
                          <Input disabled={true} value={item.position || null} />
                        ) : (
                          '-'
                        )
                      }
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Lead Source">
                      <Select disabled={true} value={clueFrom} options={
                        createLeadSource(ENUMS.lead_source || [], ACTIVITYS) || []
                      }>

                      </Select>
                    </Form.Item>
                  </Col>
                  {
                    item.contact_type && (
                      <Col span={24}>
                        {
                          clientRelatedFieldIsShow ? (
                            <ContactInfomation
                              form={form} disabled={true}
                              onlyKey={(index + 1).toString()}
                              defaultValue={Object.entries(item.contact_type).map(
                                ([key, value]) => {
                                  return { key, value }
                                }
                              )}
                            />
                          ) : null
                        }
                      </Col>
                    )
                  }
                  {
                    item.attachment && (
                      <Col span={24}>
                        <Form.Item label="Contact attachment">
                          <UploadInput
                            label="attachment"
                            form={form}
                            itemData={{
                              attachment: item.attachment.split(',').map((src, idx) => {
                                const name = src.split('/').pop();
                                return {
                                  url: src,
                                  name: name,
                                  uid: idx,
                                  status: 'done'
                                }
                              })
                            }}
                            setItemData={setItemData}
                            isLargeMode={false}
                            disabled={true}
                            accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg"
                          />
                        </Form.Item>
                      </Col>
                    )
                  }

                </Row>
              </div>
            </div>
          )
        })

      }}
    />

    <FollowUpRecord type='client' itemData={showFollowUpRecord.itemData} visible={showFollowUpRecord.visible} onClose={() => {
      setShowFollowUpRecord({
        visible: false,
        itemData: {}
      })
    }} />

    <UpdateBD title="Customer handover" master_cue_ids={showUpdateBD.master_cue_ids} itemData={showUpdateBD.itemData} visible={showUpdateBD.visible} setVisible={(isRefresh) => {
      setShowUpdateBD({
        visible: false,
        master_cue_ids: [],
        itemData: {}
      })
      if (isRefresh) {
        tableProps.defaultFetchHangler();
      }
    }} />


  </section>
}
