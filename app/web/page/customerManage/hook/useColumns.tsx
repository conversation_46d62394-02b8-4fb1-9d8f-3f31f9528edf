import useSelector from "@/hooks/useSelector";
import useAppStore from "@/store/app.store";
import { CUSTOMER_COOP_TYPE, CUSTOMER_STATUS_COLOR_MAP } from "./contact";
import { Popover, Space, Tag, Tooltip } from "antd";
import { createClueCustomerColumns, customerFields, createcCustomerCommonColumns } from "@/page/clueManage/hook/useCustomerColumns";
import useFlatTableColumns from "@/component/flat-table/useFlatTableColumns";
import useUserStore from "@/store/user.store";
import { clientRelated<PERSON>ieldHandler, CUSTOMER_STATUS_MAP, GET_COOP_BUS_STR, userIsMarketer } from "@@/lib/constant";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { renderContactDetail } from "@/page/clueManage/hook/useColumns";

export const useCustomerColumns = ({ itemData = {}, setItemData }: any, PAGE_TYPE: 'pool' | 'owned' = 'pool') => {
  const { BD_USERS, BD_ASSISTANT_USERS, ENUMS } = useAppStore(useSelector(['BD_USERS', 'BD_ASSISTANT_USERS', 'ENUMS']));
  const curUserInfo = useUserStore(state => state);
  const userIsMKT = userIsMarketer(curUserInfo);
  // const customerCls = createcCustomerCommonColumns();
  // customerCls.forEach((it: Recordable) => {
  //   if (PAGE_TYPE === 'pool') {
  //     it.update = {
  //       ...it.create,
  //       ...(it.update || {}),
  //       disabled: true
  //     }
  //   }
  // })
  const cls: any[] = [
    {
      key: 'id',
      index: 1,
      align: 'left',
      width: 180,
      filter: {
        type: 'Input',
        hidden: userIsMKT
      },
      render(id, record) {
        return GET_COOP_BUS_STR(record)

      }
    },
    {
      key: 'client_name',
      align: 'left',
      width: 240,
      index: 2,
      filter: {
        type: 'Input',
      },
      update: {
        disabled: true
      }
    },
    {
      key: 'client_status',
      align: 'left',
      index: 3,
      width: 140,
      filter: {
        hidden: userIsMKT,
        type: 'Select',
        options: Object.entries(CUSTOMER_STATUS_MAP).map(([key, value]) => ({
          label: value,
          value: key
        }))
      },
      create: {
        disabled: true
      },
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
      render(status) {
        const color = CUSTOMER_STATUS_COLOR_MAP[status] || '';
        return (
          <Tag style={{ marginRight: 0 }} color={color}>
            {CUSTOMER_STATUS_MAP[status] || '-'}
          </Tag>
        )
      },
      export: {
        mapping: CUSTOMER_STATUS_MAP
      }
    },
    {
      key: 'payment_legal_name',
      hidden: true,
      update: {
        disabled: PAGE_TYPE === 'pool'
      }
    },
    {
      key: 'company_name',
      align: 'left',
      index: 4,
      width: 200,
      filter: {
        type: 'Input',
      },
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    {
      key: 'bus_line',
      align: 'left',
      index: 5,
      width: 120,
      checked: false,
      filter: {
        hidden: userIsMKT,
        type: 'Select',
        options: ENUMS.customer_department,
      },
      export: {
        mapping: ENUMS.customer_department.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
    },
    {
      key: 'coop_bus',
      align: 'left',
      index: 6,
      width: 300,
      checked: false,
      filter: {
        hidden: userIsMKT,
        type: 'Cascader',
        multiple: true,
        options: CUSTOMER_COOP_TYPE,
        maxTagCount: 3
      },
      render(coopBus) {
        return coopBus.split(',').join(' - ')
      },

    },
    {
      key: 'comp_address',
      align: 'left',
      width: 200,
      index: 7,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
      filter: {
        hidden: userIsMKT,
        type: 'Input',
      },
      render(text) {
        if (!text) { return '-' }
        return (
          <Tooltip title={text}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '180px' }}>
              {text}
            </div>
          </Tooltip>
        )
      }
    },
    {
      key: 'country',
      hidden: true,
      index: 7,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    {
      key: 'tag_reg_no',
      hidden: true,
      index: 7,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    {
      key: 'usdt_account_id',
      hidden: true,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    {
      key: 'other_payment_terms',
      hidden: true,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    {
      key: 'pay_type',
      align: 'left',
      index: 7,
      width: 120,
      filter: {
        hidden: userIsMKT,
        type: 'Select',
        options: ENUMS.pay_type,
      },
      export: {
        mapping: ENUMS.pay_type.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
      update: {
        disabled: itemData.pay_type === 'Post-Payment' || PAGE_TYPE === 'pool'
      }
    },
    {
      key: 'payment_terms',
      hidden: true,
    },
    {
      key: 'bd_id',
      align: 'left',
      index: 8,
      width: 120,
      filter: {
        type: 'Select',
        hidden: PAGE_TYPE === 'owned' || userIsMKT,
        options: BD_USERS
      },
      render(_, record) {
        if (!record.crm_clues?.length) { return '-' }
        const mainInfo = record.crm_clues.find(it => it.clue_identity === 1);
        if (!mainInfo) { return '-' }
        return BD_USERS.find(it => it.value === mainInfo.bd_id)?.label || '-'
      },
      export: {
        mapping: BD_USERS.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
    },
    {
      key: 'assist_id',
      align: 'left',
      index: 9,
      width: 120,
      filter: {
        hidden: userIsMKT,
        type: 'Select',
        options: BD_ASSISTANT_USERS
      },
      render(_, record) {
        if (!record.crm_clues?.length) { return '-' }
        const mainInfo = record.crm_clues.find(it => it.clue_identity === 1);
        if (!mainInfo) { return '-' }
        return BD_ASSISTANT_USERS.find(it => it.value === mainInfo.assist_id)?.label || '-'
      },
      export: {
        mapping: BD_ASSISTANT_USERS.reduce((acc, cur) => {
          acc[cur.value] = cur.label;
          return acc;
        }, {}),
      },
    },
    {
      key: 'contact_type',
      align: 'center',
      title: (
        <Space>
          Contact Detail
          <Tooltip title="Please enter the contact name, contact information, Job title">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      ),
      index: 12,
      width: 200,
      checked: false,
      export: {
        title: 'Contact Detail'
      },
      render(_, record) {
        if (!record.crm_clues?.length) { return '-' }
        const mainInfo = record.crm_clues.find(it => it.clue_identity === 1);
        if (!mainInfo?.contact_type) { return '-' }
        const isShow = clientRelatedFieldHandler(mainInfo, curUserInfo);
        if (!isShow) { return '-' }

        const entriesData = Object.entries(mainInfo.contact_type);
        const displayData = entriesData.slice(0, 2);
        const jobTitle = mainInfo.position || '-';
        const jobTitleText = jobTitle || '-'
        const connectName = `${mainInfo.connect_name} (Title: ${jobTitleText})`;

        const result = (
          <Popover content={
            <div>
              {
                [['Name', connectName], ...entriesData].map(it => (
                  renderContactDetail(it as string[], true)
                ))
              }
            </div>
          }>
            <>
              {
                [['Name', connectName], ...displayData].map(it => (
                  renderContactDetail(it as string[])
                ))
              }
              {
                entriesData.length > 2 && <div style={{ textAlign: 'left', lineHeight: '10px', marginLeft: '26px' }}>...</div>
              }
            </>

          </Popover>
        );

        return result;

      },
    },

    {
      key: 'ctime',
      hidden: true,
      update: {
        type: 'DatePicker',
        span: 6,
        index: 15,
        disabled: true,
        group: 'Customer Information'
      },
      filter: {
        type: 'RangePicker',
        index: 99,
        title: 'Creation Time'
      },
    },
    {
      key: 'remark',
      hidden: true,
      update: {
        disabled: PAGE_TYPE === 'pool'
      },
    },
    // ...customerCls
  ];
  // 下面只是重复配置的利用
  const mergeCls = createClueCustomerColumns({
    itemData,
    setItemData
  })
  cls.forEach(it => {
    const findItem = mergeCls.find(item => item.key === it.key);
    if (findItem) {
      it.create = {
        ...findItem.create,
        ...(it.create || {})
      };
      it.update = {
        // @ts-ignore
        ...(findItem.update || findItem.create),
        ...(it.update || {})
      }
    }
  });


  const columns = useFlatTableColumns(cls,
    {
      Fields: {
        ...customerFields
      }
    }
  );
  return columns
}
