import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import { useCustomerClueColumns } from '@/page/clueManage/hook/useCustomerColumns';
import useAppStore from '@/store/app.store';
import useUserStore from '@/store/user.store';
import { isNumber } from '@@/lib/tool';
import { message } from 'antd';
import react, { useEffect, useState } from 'react'
import { getClientName } from './commonFunc';

export const useCreateClueAndCustomer = ({ onSuccess, type }) => {
  const curUserInfo = useUserStore(state => state);

  const { REFRESH_DATA, ACTIVITYS } = useAppStore(useSelector(['REFRESH_DATA', 'ACTIVITYS']));
  const [customerLeadItemData, setCustomerLeadItemData] = useState({
    lead_type: 'create_new_lead'
  });

  const [customerLeadModal, setCustomerLeadModal] = useState({
    visible: false,
    itemData: {}
  });

  const [leadList, setLeadList] = useState<Record<string, any>[]>([]);

  const customerLeadColumn = useCustomerClueColumns({
    itemData: customerLeadItemData,
    setItemData: setCustomerLeadItemData,
    isDisabledClue: false,
    isCutomerClueCreate: true,
    leadList,
    type
  });

  const handleCreateClueAndCustomer = async (values) => {

    if (values.lead_type === 'create_new_lead') {
      // 选择创建新线索
      if (values.contact_type) {
        values.contact_type = values.contact_type.filter(it => it.key && it.value).reduce((obj, { key, value }) => {
          obj[key] = value;
          return obj
        }, {})
        if (Object.values(values.contact_type).length === 0) {
          values.contact_type = null;
        }
      }
      if (values.attachment) {
        values.attachment = values.attachment.map(it => it.url).join(',')
      }

      if (!values.contact_type && !values.attachment) {
        message.warn('Please enter content or upload attachment')
        return;
      }

      if (isNumber(values.clue_from)) {
        values.active_id = values.clue_from
      } else {
        values.active_id = null // 清空
      }

      const newClientName = getClientName(values);
      values.client_name = newClientName;
    }


    for (const key in values) {
      if (!Object.prototype.hasOwnProperty.call(values, key)) { continue };
      if (key === 'contact_type') { continue };
      if (Array.isArray(values[key])) {
        values[key] = values[key].join(',')
      }
    }
    const result = await request.post('/customer/createClueAndCustomer', values);
    if (result.isSuccess) {
      message.success('Create successfully')
      // tableProps.defaultFetchHangler();
      REFRESH_DATA('customer')
      onSuccess && onSuccess(result.data)
      setCustomerLeadModal({
        visible: false,
        itemData: {}
      })
    }

  }

  const getLeads = async () => {
    const result = await request.get('/clue', {
      pageIndex: 1,
      pageSize: 99999,
      clue_status: 'assigned'
    })
    const leads = result.data || [];
    let leadsList = leads.map(it => {
      const leadSource = ACTIVITYS.find(item => item.value === Number(it.clue_from))?.label || it.clue_from
      return {
        label: `${it.id}: ${it.company_name} (${leadSource})`,
        value: it.id,
        bd_id: it.bd_id
      }
    });
    if (type === 'customer') {
      leadsList = leadsList.filter(it => Number(it.bd_id) === Number(curUserInfo.id))
    }
    setLeadList(leadsList)
  }

  useEffect(() => {
    if (!customerLeadModal.visible) {
      setCustomerLeadItemData({
        lead_type: 'create_new_lead'
      })
      getLeads();
    }
  }, [customerLeadModal.visible])

  return {
    customerLeadColumn,
    customerLeadModal,
    setCustomerLeadModal,
    handleCreateClueAndCustomer
  }
}
