import { useState } from "react";
import { useLocation } from 'react-router-dom';
import { message } from "antd";
import request from "@/modules/request";
import { createDefaultContractType } from "@/page/clueManage/hook/contact";
import { COMMON_BEFORE_FILTER, OPTION_SEPARATOR } from "@@/lib/constant";
import moment from "moment";

const beforeSubmitHandler = (values) => {
  for (const key in values) {
    if (!Object.prototype.hasOwnProperty.call(values, key)) { continue };
    if (key === 'contact_type') { continue };
    if (Array.isArray(values[key])) {
      values[key] = values[key].join(',')
    }
  }
  return {
    isSuc: true,
    values
  };
};

export const beforeEditHandler = (values, setItemData) => {
  const mainInfo = values.crm_clues.find(it => it.clue_identity === 1);
  if (mainInfo) {
    values.bd_id = mainInfo.bd_id;
    values.assist_id = mainInfo.assist_id;
    values.ctime = moment(values.ctime);
  };
  if (values.effective_date) {
    values.effective_date = moment(values.effective_date);
  }
  if (values.expiry_date) {
    values.expiry_date = moment(values.expiry_date);
  }
  if (values.template) {
    values.template = values.template.split(',')
  }
  if (values.coop_bus) {
    values.coop_bus = values.coop_bus.split(',')
  }
  setItemData({
    ...values
  })
}

export const commonTableProps = {
  allowRowSelect: true,
  beforeCreate() {
    return {
      contact_type: createDefaultContractType()
    }
  },
  async beforeHandleCreate(values) {
    return beforeSubmitHandler(values);
  },
  async beforeHandleUpdate(values) {
    return beforeSubmitHandler(values);
  },
  beforeFilter(filters) {
    return COMMON_BEFORE_FILTER(filters)
  }
}


export const useCommonState = ({
  tableProps,
}) => {
  const [showUpdateBD, setShowUpdateBD] = useState({
    visible: false,
    master_cue_ids: [] as number[],
    itemData: {} as Record<string, any>
  });
  const [curEditPoolInfos, setCurEditPoolInfos] = useState<Record<string, any>[]>([]);

  const [showFollowUpRecord, setShowFollowUpRecord] = useState({
    visible: false,
    itemData: {}
  });

  const changeClientStatus = async (id, values: Record<string, any>) => {
    const res = await request.put(`/customer/${id}`, values);
    if (res.isSuccess) {
      message.success('success');
      tableProps.defaultFetchHangler()
    }
  };

  const handlerCurEditPoolInfos = async (id: number) => {
    const detail = await request.get(`/customer/${id}`);
    setCurEditPoolInfos(detail.data.crm_clues);
  }

  return {
    showFollowUpRecord,
    setShowFollowUpRecord,
    changeClientStatus,
    handlerCurEditPoolInfos,
    curEditPoolInfos,
    showUpdateBD,
    setShowUpdateBD
  }

}

export const getClientNameExtra = (values: { coop_bus: string[], client_name: string }) => {
  let extra = '';

  if ((values.coop_bus?.includes('Demand Group')) && !values.client_name?.includes('DSP')) {
    extra = `The system will automatically change to：${values.client_name}_DSP`
  } else if (((values.coop_bus?.includes('Publisher Group') && values.coop_bus?.includes('Adx'))) && !values.client_name?.includes('SSP')) {
    extra = `The system will automatically change to：${values.client_name}_SSP`
  }
  return extra;
}

export const getClientName = (values: { coop_bus: string[], client_name: string }) => {
  let clientName = values.client_name;
  if ((values.coop_bus?.includes('Demand Group')) && !values.client_name?.includes('DSP')) {
    clientName = `${values.client_name}_DSP`
  } else if (((values.coop_bus?.includes('Publisher Group') && values.coop_bus?.includes('Adx'))) && !values.client_name?.includes('SSP')) {
    clientName = `${values.client_name}_SSP`
  }
  return clientName;
}
