import React, { useEffect, useMemo, useState } from 'react';
import {
  FlatTableActionType,
  FlatTableBtnGroupType,
  FlatSwitchButton,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import moment from 'moment';
import request from '@/modules/request';
import { Button, Checkbox, message, Space, Tooltip } from 'antd';
import BasePageHoc from '@/component/base-page-hoc'
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import useAppStore, { getActivity } from '@/store/app.store';
import { dimensionMap, metricMap, TABLE_SORT_LIST } from '@@/lib/pageData/index'
import './index.scss'
import { ColumnSetting } from '@/component/flat-table/types';
import { COMMISSION_ADV_TYPE, COMMISSION_PUB_TYPE } from '@@/lib/constant';
import { SortOrder } from 'antd/lib/table/interface';
import Revise from '../components/Revise';

function CommissionReport() {
  const { ADV_OP_USERS, PUB_OP_USERS, ADV_BD_USERS, PUB_BD_USERS } = useAppStore(useSelector(['ADV_OP_USERS', 'PUB_OP_USERS', 'ADV_BD_USERS', 'PUB_BD_USERS']));
  const [dimensionList, setDimensionList] = useState<any>(Array.from(dimensionMap.values()).map(it => it.key));
  const [metricList, setMetricList] = useState<any>(Array.from(metricMap.values()).map(it => it.key));
  const dimensionKeys = Array.from(dimensionMap.values()).map(it => it.key);
  const metricKeys = Array.from(metricMap.values()).map(it => it.key);

  const [refreshListKey, setrefreshListKey] = useState(0);
  const allColumnsKey = [...dimensionKeys, ...metricKeys];
  const currentSelectColumn = useMemo(() => {
    const columnsSortKey = [...dimensionList, ...metricList];
    return columnsSortKey;

  }, [refreshListKey]);
  const [showReviseInfo, setShowReviseInfo] = useState({
    visible: false,
    itemData: {},
  });
  const [filters, setFilters] = useState({
    advertiserGroups: [],
    publisherGroups: []
  });

  const getTableColumn = (): (ColumnSetting | string)[] => {
    return [
      {
        key: 'month', width: 120, sorter: true, filter: {
          type: 'RangePicker',
          picker: 'month' as 'month',
          // defaultValue: [moment().subtract(6, 'months'), moment().subtract(1, 'months')]
        },
        mapToData: ['billing_start_month', 'billing_end_month', 'YYYY-MM'],
      },
      {
        key: 'adv_group', width: 180, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          const renderText = `${text}:${record.advertiser_group_name || ''}`
          return (
            <Tooltip title={renderText} placement='topLeft'>
              {renderText}
            </Tooltip>
          )
        },
        filter: {
          type: 'Select',
          options: filters.advertiserGroups,
          mode: 'multiple',
          maxTagCount: 3,
        }
      },
      {
        key: 'adv_type', width: 180, filter: {
          type: 'Select',
          options: COMMISSION_ADV_TYPE,
          mode: 'multiple',
          maxTagCount: 3,
        }
      },
      { key: 'payment_period', width: 150 },
      {
        key: 'adv_bd', width: 150, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={text} placement='topLeft'>
              {text}
            </Tooltip>
          );
        },
        filter: {
          type: 'Select',
          options: ADV_BD_USERS.map(it => ({
            label: it.label,
            value: it.label
          })),
          mode: 'multiple',
          maxTagCount: 3,
        }

      },
      {
        key: 'adv_owner', width: 130, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={record.adv_owner_name} placement='topLeft'>
              {record.adv_owner_name}
            </Tooltip>
          );
        },
        filter: {
          type: 'Select',
          options: ADV_OP_USERS.map(it => ({
            label: it.label,
            value: `${it.value}:${it.label}`
          })),
          mode: 'multiple',
          maxTagCount: 3,
        }
      },
      {
        key: 'pub_group', width: 180, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          const renderText = `${text}:${record.publisher_group_name || ''}`
          return (
            <Tooltip title={renderText} placement='topLeft'>
              {renderText}
            </Tooltip>
          );
        },
        filter: {
          type: 'Select',
          options: filters.publisherGroups,
          mode: 'multiple',
          maxTagCount: 3,
        },
      },
      {
        key: 'pub_type', width: 120, filter: {
          type: 'Select',
          options: COMMISSION_PUB_TYPE,
        }
      },
      {
        key: 'pub_bd', width: 150, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={text} placement='topLeft'>
              {text}
            </Tooltip>
          );
        },
        filter: {
          type: 'Select',
          options: PUB_BD_USERS.map(it => ({
            label: it.label,
            value: it.label
          })),
          mode: 'multiple',
          maxTagCount: 3,
        }
      },
      {
        key: 'pub_owner', width: 180, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={record.pub_owner_name} placement='topLeft'>
              {record.pub_owner_name}
            </Tooltip>
          );
        },
        filter: {
          type: 'Select',
          options: PUB_OP_USERS.map(it => ({
            label: it.label,
            value: `${it.value}:${it.label}`
          })),
          mode: 'multiple',
          maxTagCount: 3,
        }
      },
      { key: 'billing_revenue', width: 140, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[], defaultSortOrder: 'descend' as SortOrder },
      { key: 'paid_revenue', width: 120, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[] },
      {
        key: 'paid_month', width: 150, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={text} placement='topLeft'>
              {text}
            </Tooltip>
          );
        },
        filter: {
          type: 'RangePicker',
          picker: 'month' as 'month',
        },
        mapToData: ['paid_start_month', 'paid_end_month', 'YYYY-MM'],
      },
      {
        key: 'paid_date', width: 150, ellipsis: true, render(text, record, index) {
          if (index === 0) { return '-' };
          return (
            <Tooltip title={text} placement='topLeft'>
              {text}
            </Tooltip>
          );
        }
      },
      { key: 'overdue_payment_date', width: 190 },
      { key: 'deduction', width: 150 },
      { key: 'pub_cost', width: 150, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[] },
      { key: 'gross_profit', width: 150, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[] },
      { key: 'gross_profit_margin', width: 190, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[] },

      { key: 'commissionable_amount', width: 200, sorter: true, sortDirections: ['descend', 'ascend'] as SortOrder[] }
    ].sort((a, b) => {
      // 按currentSelectColumn的index去排序
      return allColumnsKey.indexOf(a.key) - allColumnsKey.indexOf(b.key)
    }).map(it => ({
      ...it,
      index: allColumnsKey.indexOf(it.key),
      hidden: !currentSelectColumn.includes(it.key)
    }))
  }

  const columns = useFlatTableColumns(
    getTableColumn(),
    {
      Fields: {
        month: 'Billing Month',
        adv_group: 'Adv Group',
        adv_type: 'Adv Type',
        payment_period: 'Payment Period',
        adv_bd: 'Adv BD',
        adv_owner: 'Adv Owner',
        pub_group: 'Pub Group',
        pub_type: 'Pub Type',
        pub_bd: 'Pub BD',
        pub_owner: 'Pub Owner',
        billing_revenue: 'Billing Revenue',
        billing_month: 'Billing Month',
        paid_revenue: 'Paid Revenue',
        paid_month: 'Paid Month',
        paid_date: 'Paid Date',
        pub_cost: 'Pub Cost',
        gross_profit: 'Gross Profit',
        gross_profit_margin: 'Gross Profit Margin',
        overdue_payment_date: 'Overdue Payment Date',
        deduction: 'Deduction',
        commissionable_amount: 'Commissionable Amount'
      }
    }
  );

  const tableProps = useFlatTable({
    title: 'Commission Report List',
    fetchUrl: '/commission/report',
    pageSize: 100,
    columns,
    defaultParams: {
      dimensionStr: dimensionList.join(','),
      month: [moment().subtract(6, 'months'), moment().subtract(1, 'months')],
    },
    customPagination: true,
    filterBeforeReset: () => {
      return {
        month: [moment().subtract(6, 'months'), moment().subtract(1, 'months')],
      }
    },
    btnGroup: [
      (record) => {
        return (
          <Button type='primary' onClick={() => {
            setShowReviseInfo({
              visible: true,
              itemData: { ...record }
            })
          }}>Revise</Button>
        )
      },
      BtnGroupType.Export,
      BtnGroupType.OperationRecord,
      BtnGroupType.CustomColumn,
    ],
    actions: [
      (record) => {
        if (!record.month) { return null };
        return (
          <Button type='primary'>Detail</Button>
        )
      }
    ],
    async beforeEdit(record) {
      record.time_range = [moment(record.start_time), moment(record.end_time)];
      return record;
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/commissionReport/export', {
          ...params,
          sheetName: 'commission-report',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'commission-report.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
    dataFetchAfter() {
      // 数据获取之后
      setrefreshListKey(refreshListKey + 1)
    },
    beforeFilter(filters) {
      for (const key in filters) {
        if (!Object.prototype.hasOwnProperty.call(filters, key)) { continue };
        if (Array.isArray(filters[key])) {
          filters[key] = filters[key].join(',')
        }
      }
      return filters
    }
  });

  const getFilters = async () => {
    const result = await request.get('/commissionReport/getFilters');
    setFilters(result.data)
  }
  useEffect(() => {
    getFilters();
    tableProps.setSorter('billing_revenue,desc')
  }, [])

  return <section className="business-list-wrap">
    <div className='section-filter checkbox-container'>
      <div className='operation-bar'>
        <h2>Dimension</h2>
      </div>
      <Checkbox.Group style={{ paddingLeft: `2.6%` }} value={dimensionList} onChange={value => {
        setDimensionList(value)
      }}>
        {Array.from(dimensionMap.entries()).map(([key, value]: [string, any]) => {
          return (
            <Checkbox key={`dimension_checkbox_${key}`} value={value.key}>
              {key}
            </Checkbox>
          );
        })}
      </Checkbox.Group>
    </div>

    <div className='section-filter checkbox-container'>
      <div className='operation-bar'>
        <h2>Metric</h2>
      </div>
      <Checkbox.Group style={{ paddingLeft: `2.6%` }} value={metricList} onChange={value => setMetricList(value)}>
        {Array.from(metricMap.entries()).map(([key, value]: [string, any]) => {
          return (
            <Checkbox key={`metric_checkbox_${key}`} value={value.key}>
              {key}
            </Checkbox>
          );
        })}
      </Checkbox.Group>
    </div>

    <FlatTable {...tableProps} />

    {
      showReviseInfo.visible && (
        <Revise
          visible={showReviseInfo.visible}
          setVisible={() => {
            setShowReviseInfo({
              visible: false,
              itemData: {}
            })
          }}
          itemData={showReviseInfo.itemData}
          filters={filters}
        />
      )
    }
  </section>
}

export default BasePageHoc(CommissionReport)
