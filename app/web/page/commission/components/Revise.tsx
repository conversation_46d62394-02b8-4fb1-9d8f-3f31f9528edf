import { CreateModal } from '@/component';
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';
import useSelector from '@/hooks/useSelector';
import request from '@/modules/request';
import useAppStore from '@/store/app.store';
import { Form, message } from 'antd';
import React from 'react';
interface IProps {
  visible: boolean
  setVisible: (isRefresh: boolean) => void
  itemData: Record<string, any>
  filters: {
    advertiserGroups: { label: string; value: string }[]
    publisherGroups: { label: string; value: string }[]
  }
}

const REVISE_ITEM = [
  { label: 'Adv BD', value: 'adv_bd' },
  { label: 'Adv Owner', value: 'adv_owner' },
  { label: 'Pub BD', value: 'pub_bd' },
  { label: 'Pub Owner', value: 'pub_owner' },
  { label: 'Actual Receipt', value: 'actual_reveipt' },
  { label: 'Actual Payment', value: 'actual_payment' }
]

export default function (props: IProps) {
  const { ADV_OP_USERS, PUB_OP_USERS, ADV_BD_USERS, PUB_BD_USERS } = useAppStore(useSelector(['ADV_OP_USERS', 'PUB_OP_USERS', 'ADV_BD_USERS', 'PUB_BD_USERS']));
  const [formInstance] = Form.useForm();
  const itemWatch = Form.useWatch('item', formInstance) || '';
  console.log('1111', itemWatch)
  const columns = useFlatTableColumns([
    {
      key: 'item',
      hidden: true,
      create: {
        type: 'Select',
        index: 1,
        options: REVISE_ITEM,
        formItemProps: {
          required: true,
        },
      },
    },
    {
      key: 'adv_group',
      create: {
        type: 'Select',
        index: 2,
        hidden: !['adv_bd', 'adv_owner', 'actual_reveipt'].includes(itemWatch),
        formItemProps: {
          required: true,
        },
        options: props.filters.advertiserGroups,
      },
    },
    {
      key: 'pub_group',
      create: {
        type: 'Select',
        index: 2,
        hidden: !['pub_bd', 'pub_owner', 'actual_payment'].includes(itemWatch),
        formItemProps: {
          required: true,
        },
        options: props.filters.publisherGroups,
      },
    },
    {
      key: 'month',
      create: {
        type: 'DatePicker',
        showTime: false,
        // @ts-ignore
        picker: 'month',
        formItemProps: {
          required: true,
        },
      }
    },
    {
      key: 'amount',
      create: {
        type: 'InputNumber',
        hidden: !['actual_reveipt', 'actual_payment'].includes(itemWatch),
        formItemProps: {
          required: true,
        },
      }
    },
    {
      key: 'new_adv_bd',
      create: {
        type: 'Select',
        hidden: itemWatch !== 'adv_bd',
        options: ADV_BD_USERS.map(it => ({
          label: it.label,
          value: it.label
        })),
        formItemProps: {
          required: true,
        },
      },
    },
    {
      key: 'new_adv_owner',
      create: {
        type: 'Select',
        hidden: itemWatch !== 'adv_owner',
        options: ADV_OP_USERS.map(it => ({
          label: it.label,
          value: `${it.value}:${it.label}`
        })),
        mode: 'multiple',
        maxTagCount: 3,
        formItemProps: {
          required: true,
        },
      }
    },
    {
      key: 'new_pub_bd',
      create: {
        type: 'Select',
        hidden: itemWatch !== 'pub_bd',
        options: PUB_BD_USERS.map(it => ({
          label: it.label,
          value: it.label
        })),
        formItemProps: {
          required: true,
        },
      }
    },
    {
      key: 'new_pub_owner',
      create: {
        type: 'Select',
        formItemProps: {
          required: true,
        },
        options: PUB_OP_USERS.map(it => ({
          label: it.label,
          value: `${it.value}:${it.label}`
        })),
        mode: 'multiple',
        maxTagCount: 3,
      }
    },
    {
      key: 'note',
      create: {
        type: 'TextArea',
      },
    }
  ], {
    Fields: {
      item: 'Item',
      adv_group: 'Adv Group',
      pub_group: 'Pub Group',
      month: ' Billing Month',
      amount: 'Amount',
      note: 'Note',
      new_adv_bd: 'Adv BD',
      new_adv_owner: 'Adv Owner',
      new_pub_bd: 'Pub BD',
      new_pub_owner: 'Pub Owner'
    }
  })

  const handleCreate = async (options) => {
    // const res = await request.post('/clue/batchLeadAssignment', {
    //   ...options,
    //   ids: props.ids,
    //   type: props.itemData?.type
    // });
    // if (res.isSuccess) {
    //   message.success('Lead assigned successfully')
    //   props.setVisible(true)
    // } else {
    //   message.error(res.msg)
    // }

  }

  return (
    <CreateModal
      title={`Commmission Revise`}
      columns={columns}
      visible={props.visible}
      itemData={props.itemData}
      setVisible={(visible) => {
        props.setVisible(visible)
      }}
      onChange={handleCreate}
      width={910}
      layout={{
        labelCol: { span: 7 }
      }}
      formInstance={formInstance}
      style={{ top: '10px' }}
    />
  )
}
