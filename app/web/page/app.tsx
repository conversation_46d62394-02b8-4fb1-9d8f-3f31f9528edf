import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { AppContext } from '@/store';
import Route from '@/router';
import bootstrap from '@/framework/bootstrap';
import { formatAuth, getUniqueArray, isBrowser } from '@@/lib/tool';
import { hot } from 'react-hot-loader/root';
import watermark from '@flat/rollup-plugin-watermark'; // 水印库
import { __ } from '@/modules/format';
import 'antd/dist/antd.variable.min.css';
import useRouterStore from '../store/router.store';
import useSystemUpdate from '@fe-design/egg-system-update-notification/app/web/hooks/useSystemUpdate';
import useAppMessageBus from '@/hooks/useAppMessageBus'
import './app.scss';
import { message } from 'antd';
import useUserStore from '@/store/user.store';
import useAppStore, { getActivity, getCountrys, getCustomer, getEnums, getSystemAccounts } from '@/store/app.store'
import useSelector from '@/hooks/useSelector';
import { AliveScope } from 'react-activation'
import request from '@/modules/request';
import { userISDemoAccount } from '@@/lib/constant';

if (isBrowser()) {
  const roleAuth = window.__INITIAL_STATE__.auth.roleAuth || [];
  console.log('roleAuth', roleAuth)
  const lang = window.__INITIAL_STATE__.lang;
  window.__INITIAL_STATE__.auth.authObjTree = formatAuth(roleAuth);
  const routeAccessAuths = new Map<string, Record<string, any>>();
  roleAuth.forEach(item => {
    const { path } = item;
    if (!routeAccessAuths.has(path)) {
      routeAccessAuths.set(path, {
        ...item
      });
    }
  });

  const routeGuard = function (url: string) {
    const { addRouteHistory } = useRouterStore.getState();
    const { pathname } = new URL(url, window.location.origin);
    // console.log('next route', url);
    // 只记录“运营管理”下的子路由
    if (pathname.startsWith('/operation') && pathname.split('/').length >= 4) {
      const route = routeAccessAuths.get(pathname);
      if (!route) {
        message.error(`${url}：无访问权限或此路由不存在`);
        return false;
      }
      addRouteHistory({
        name: route[lang === 'en' ? 'remark_en' : 'remark'],
        path: pathname
      });
    }
    return true;
  };

  const bindEventListener = function <T extends keyof History>(type: T) {
    const historyEvent = history[type];
    return function (this: any, state: any, unused: any, url: string) {
      if (!routeGuard(url)) return;

      const newEvent = historyEvent.apply(this, arguments);
      const e = new CustomEvent(type, {
        detail: {
          from: window.location.pathname + window.location.search + window.location.hash,
          to: url,
          state
        }
      });
      // @ts-ignore
      e.arguments = arguments;
      window.dispatchEvent(e);
      return newEvent;
    };
  };

  history.pushState = bindEventListener('pushState');
  history.replaceState = bindEventListener('replaceState');
  routeGuard(window.location.pathname + window.location.search + window.location.hash);
}



function Admin(props) {
  window.__INITIAL_STATE__.auth.authObjTree = formatAuth(window.__INITIAL_STATE__.auth.roleAuth);
  const initState = window.__INITIAL_STATE__;
  const { user, env } = initState;
  const letterArr = env.split('');
  letterArr[0] = letterArr[0].toUpperCase();

  // 系统更新通知推送 
  const eventSource = useSystemUpdate({
    enable: window.__INITIAL_STATE__.env !== 'local', // 本地环境不用生效
    isForceUpdate: false,
    uid: window.__INITIAL_STATE__.user.id || window.__INITIAL_STATE__.user.email,
    username: window.__INITIAL_STATE__.user.pub_name,
    lang: props?.lang,
    notification: {
      title: window.__INITIAL_STATE__.localeData.AppName,
      icon: '/public/img/logo.png',
      duration: 0
    }
  });
  // 系统消息总线
  useAppMessageBus(eventSource.current);
  const { setup: setupUserStore } = useUserStore(useSelector(['setup']));
  const {
    setup: setupAppStore,
  } = useAppStore(useSelector([
    'setup',
  ]));

  const initUserStore = () => {
    // if (window.__INITIAL_STATE__.isLogin) {
    const userInfo = window.__INITIAL_STATE__.user;
    const roleAuth = window.__INITIAL_STATE__.auth.roleAuth;
    const routeOperateAuths = new Map<string, string[]>();
    const routeAccessAuths = new Map<string, Record<string, any>>();
    roleAuth.forEach(item => {
      const { path, operate_auth } = item;
      if (!routeOperateAuths.has(path)) {
        routeOperateAuths.set(path, operate_auth);
      }
      if (!routeAccessAuths.has(path)) {
        routeAccessAuths.set(path, {
          ...item
        });
      }
    });

    setupUserStore({
      dbId: userInfo.id,
      id: userInfo.id,
      dingUserId: userInfo.d_user_id,
      email: userInfo.email,
      nickname: userInfo.pub_name,
      username: userInfo.pub_name,
      realname: userInfo.pub_name,
      team: userInfo.team,
      avatar: userInfo.avatar,
      workPlace: userInfo.work_place,
      roleCode: userInfo.roleCode,
      crmAuditType: userInfo.crm_audit_type ? userInfo.crm_audit_type.split(',') : [],
      routeOperateAuths,
      routeAccessAuths,
      bd_member: userInfo.bd_member,
      op_member: userInfo.op_member,
    });
    // }
  }

  const initAppStore = async () => {
    try {
      const result = await request.get('/user/getByRoleUsers');
      const { ops, bds, bdLeaders, opLeaders, bdAssistants,
        mkts, finances, legals, advOps, pubOps, advBds, pubBds, assetGroups, taxs, superAdmins, admins,
        programmaticOps
      } = result.data;
      const appstoreRes = {
        ADV_OP_USERS: advOps,
        PUB_OP_USERS: pubOps,
        OP_USERS: ops,
        ADV_BD_USERS: advBds,
        PUB_BD_USERS: pubBds,
        BD_USERS: bds,
        BD_LEADER_USERS: bdLeaders,
        OP_LEADER_USERS: opLeaders,
        BD_ASSISTANT_USERS: bdAssistants,
        MKT_USERS: mkts,
        FIN_USERS: finances,
        LEGAL_USERS: legals,
        ASSET_USERS: assetGroups,
        TAX_USERS: taxs,
        SUPER_ADMIN_USERS: superAdmins,
        ADMIN_USERS: admins,
        PROGRAMMATIC_OP_USERS: programmaticOps,
        ALL_AUDIT_USERS: getUniqueArray([...assetGroups, ...finances, ...taxs, ...legals], 'value')
      }
      if (userISDemoAccount(user)) {
        for (const key in appstoreRes) {
          appstoreRes[key] = []
        }
      }
      setupAppStore(appstoreRes)
    } catch (err) {
      console.log('获取用户角色组失败', err)
    }
  }

  const initCommonFilter = async () => {

    const [activitys, enums, customer, countrys, accounts] = await Promise.all([getActivity(), getEnums(), getCustomer(), getCountrys(), getSystemAccounts()]);

    setupAppStore({
      ACTIVITYS: activitys,
      ENUMS: enums,
      CUSTOMER: customer,
      ACCOUNTS: userISDemoAccount(user) ? [] : accounts,
      COUNTRYS: countrys
    })

  }

  const checkAndCompleteDingtalkTodo = () => {
    if (!window.location.search) { return };
    const searchParams = new URLSearchParams(window.location.search);
    const dingtalkTimeStamp = searchParams.get('dingtalk_ts');
    if (!dingtalkTimeStamp) { return };

    setTimeout(() => {
      request.post('/ddCloud/completeTodo', {
        dingtalk_ts: dingtalkTimeStamp
      })
    }, 1000)
  }

  // 初始化有关用户信息的全局状态
  useEffect(() => {
    if (window.__INITIAL_STATE__.isLogin) {
      initUserStore();
      initAppStore();
      initCommonFilter();
      checkAndCompleteDingtalkTodo();
    }

  }, []);



  // 初始化
  if (Object.keys(user).length) {
    watermark({ rotate: '-40', content: `${__(letterArr.join(''))}_${user.pub_name}\n${__('AppName')}` });
  }
  return (
    <AppContext.Provider value={initState}>
      <BrowserRouter>
        {/* @ts-ignore */}
        <AliveScope>
          <Route />
        </AliveScope>
      </BrowserRouter>
    </AppContext.Provider>
  );
}

// EASY_ENV_IS_DEV 这段为了ssr热更新，不可漏
export default bootstrap(EASY_ENV_IS_DEV ? hot(Admin) : Admin);
