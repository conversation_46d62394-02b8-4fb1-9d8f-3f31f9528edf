import React, { useEffect, useMemo, useState } from 'react';
import {
  FlatTableActionType,
  FlatTableBtnGroupType,
  FlatSwitchButton,
} from '@flat-design/components-pc';
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import request from '@/modules/request';
import { Button, Col, Divider, message, Row, Select, Space, Statistic, Tooltip } from 'antd';
import BasePageHoc from '@/component/base-page-hoc'
import useFlatTableColumns from '@/component/flat-table/useFlatTableColumns';

import './index.scss';
import { OPTION_SEPARATOR, TODO_STATUS, TODO_STEP, TODO_STEP_TO_ROLE, TODO_TYPE, userIsADMIN, userIsBD, userIsBDLeader, userIsOPLeader } from '@@/lib/constant';
import dayjs from 'dayjs';
import useUserStore from '@/store/user.store';
import useAppStore from '@/store/app.store';
import useSelector from '@/hooks/useSelector';
import { deduplicateArray } from '@@/lib/tool';
import OperateAccess from '@/component/auth/operate-access';
import { useLocalStorage, useSessionStorage } from '@/hooks/useStorage';

function TodoManageRecord() {
  const { BD_USERS, OP_USERS, FIN_USERS, BD_LEADER_USERS, OP_LEADER_USERS, PROGRAMMATIC_OP_USERS, BD_ASSISTANT_USERS } = useAppStore(useSelector([
    'BD_USERS', 'OP_USERS', 'FIN_USERS', 'BD_LEADER_USERS', 'OP_LEADER_USERS', 'PROGRAMMATIC_OP_USERS', 'BD_ASSISTANT_USERS'
  ]));
  const [GLOBAL_USER, SET_GLOBAL_USER] = useState<number | undefined>(undefined);
  const curUserInfo = useUserStore(state => state);
  const [roleList, setRoleList] = useState<Recordable>([]);
  const sessionStorage = useSessionStorage();

  const [sessionClickRecordIds, setsessionClickRecordIds] = useState<number[]>(sessionStorage.getStoredValue('take_action_click_record', []) || []);

  const [overallData, setOverallData] = useState({
    all_pending: 0,
    finance: 0,
    contract: 0,
    leads: 0,
    task_overdue: 0,
  });

  const isAdmin = userIsADMIN(curUserInfo);
  const isBDLeader = userIsBDLeader(curUserInfo);
  const isOPLeader = userIsOPLeader(curUserInfo);
  const GLOBAL_USER_OPTIONS = useMemo(() => {
    let options: Record<string, any>[] = [];
    if (isAdmin) {
      options = [
        ...BD_USERS,
        ...OP_USERS,
        ...FIN_USERS,
        ...BD_LEADER_USERS,
        ...OP_LEADER_USERS,
        ...PROGRAMMATIC_OP_USERS,
        ...BD_ASSISTANT_USERS
      ]
    } else if (isBDLeader) {
      // 拿到当前Leader下的成员
      const bdMember = curUserInfo.bd_member;
      if (bdMember) {
        options = bdMember.split(',').map(it => BD_USERS.find(userItem => Number(userItem.value) === Number(it)) || '').filter(it => it) as any;
      }
    } else if (isOPLeader) {
      const opMember = curUserInfo.op_member;
      if (opMember) {
        options = opMember.split(',').map(it => deduplicateArray([...OP_USERS, ...PROGRAMMATIC_OP_USERS], 'value').find(userItem => Number(userItem.value) === Number(it)) || '').filter(it => it) as any;
      }
    }
    return deduplicateArray(options, 'value')
  }, [
    BD_USERS,
    OP_USERS,
    FIN_USERS,
    BD_LEADER_USERS,
    OP_LEADER_USERS,
    PROGRAMMATIC_OP_USERS,
    BD_ASSISTANT_USERS
  ]);
  const USER_OPTIONS = useMemo(() => {
    return deduplicateArray([
      ...BD_USERS,
      ...OP_USERS,
      ...FIN_USERS,
      ...BD_LEADER_USERS,
      ...OP_LEADER_USERS,
      ...PROGRAMMATIC_OP_USERS,
      ...BD_ASSISTANT_USERS
    ], 'value')
  }, [
    BD_USERS,
    OP_USERS,
    FIN_USERS,
    BD_LEADER_USERS,
    OP_LEADER_USERS,
    PROGRAMMATIC_OP_USERS,
    BD_ASSISTANT_USERS
  ]);

  const columns = useFlatTableColumns([
    {
      key: 'content',
      align: 'left',
      // @ts-ignore
      index: 0,
      ellipsis: true,
      width: 300,
      filter: {
        type: 'Input',
      },
      render(text, record) {
        return (
          <Tooltip title={text}>
            {text}
          </Tooltip>
        )
      }
    },
    {
      key: 'type',
      align: 'left',
      width: 120,
      filter: {
        type: 'Select',
        options: TODO_TYPE,
        mode: 'multiple',
      },
      index: 1,
      render(type) {
        return TODO_TYPE.find(item => item.value === type)?.label;
      }
    },
    {
      key: 'belong',
      align: 'left',
      index: 2,
      width: 170,
      filter: {
        type: 'Select',
        options: TODO_STEP,
        mode: 'multiple',
      },
      render(step) {
        return TODO_STEP.find(item => item.value === step)?.label;
      }
    },
    {
      key: 'due_time',
      align: 'left',
      // @ts-ignore
      index: 3,
      width: 120,
      render(unix) {
        return dayjs(unix * 1000).format('YYYY-MM-DD');
      }
    },
    {
      key: 'owner',
      align: 'left',
      // @ts-ignore
      index: 4,
      width: 250,
      filter: {
        type: 'Select',
        options: GLOBAL_USER_OPTIONS,
        mode: 'multiple',
        disabled: !(isAdmin || isBDLeader || isOPLeader)
      },
      ellipsis: true,
      render(owner, record) {
        if (!owner) { return null };
        const prefix = TODO_STEP_TO_ROLE[record.belong];
        const ownerName = owner.split(',').map(it => USER_OPTIONS.find(user => user.value === it)?.label || it).filter(it => it).join(',');
        const result = `${prefix} - ${ownerName}`;
        return (
          <Tooltip title={result}>
            {result}
          </Tooltip>
        )
      }
    },
    {
      key: 'duration',
      align: 'left',
      // @ts-ignore
      index: 5,
      width: 80,
      render(duration, record) {
        // 当前时间减去待办生成的日期，粒度为天，格式如7d
        const now = dayjs();
        const dueTime = dayjs(record.ctime);
        const diff = now.diff(dueTime, 'day');
        return `${diff}d`;
      }
    },
    {
      key: 'status',
      align: 'left',
      // @ts-ignore
      index: 6,
      width: 80,
      filter: {
        type: 'Select',
        options: TODO_STATUS.filter(it => it.value !== 2),
        mode: 'multiple',
      },
      render(status) {
        return TODO_STATUS.find(item => item.value === status)?.label;
      }
    }
  ],
    {
      Fields: {
        content: 'Content',
        type: 'Type',
        belong: 'Step',
        due_time: 'Due Date',
        owner: 'Owner',
        duration: 'Duration',
        status: 'Status'
      }
    }
  );

  const tableProps = useFlatTable({
    title: 'Todo List',
    fetchUrl: '/crmTodoManager/record',
    pageSize: 20,
    columns,
    actions: [
      (record) => (
        <Button type='primary' onClick={() => {
          const belong = record.belong;
          const env = window.__INITIAL_STATE__.env;
          const hostStrategy = {
            'local': 'http://**************:8008',
            'test': 'http://************:6517',
            'prod': 'https://traffic.mobshark.net'
          }
          if (belong === 1) {
            // 通知运营去确认账单
            const { page_type, month, group_id } = record.relation_json || {};
            if (page_type === "programmatic") {
              window.open(`${hostStrategy[env]}/programmatic-billing-report?adv_group=${group_id}&month=${month}&now=${Date.now()}`)
            } else {
              window.open(`${hostStrategy[env]}/billing-report?adv_group=${group_id}&month=${month}&now=${Date.now()}`)
            }
          } else if (belong === 2) {
            // 通知运营去确认账单
            const { month, group_id, page_type } = record.relation_json || {};
            if (page_type === "programmatic") {
              window.open(`${hostStrategy[env]}/programmatic-billing-report?pub_group=${group_id}&month=${month}&now=${Date.now()}&type=cost`)
            } else {
              window.open(`${hostStrategy[env]}/billing-report?pub_group=${group_id}&month=${month}&now=${Date.now()}&type=cost`)
            }
          } else if (belong === 3 || belong === 7) {
            // 通知商务区盖章
            const { month, group_id, adv_type } = record.relation_json || {};
            window.open(`${hostStrategy[env]}/invoice-management?toBilling=1&adv_group=${group_id}&month=${month}&adv_type=${adv_type}&now=${Date.now()}`)
          } else if (belong === 4 || belong === 5) {
            // 4. 通知财务去盖章，5. 通知商务去匹配账单 
            const { invoice_no } = record.relation_json || {};
            window.open(`${hostStrategy[env]}/invoice-management?toInvoiceNo=1&invoice_no=${invoice_no}&now=${Date.now()}`)
          } else if (belong === 6) {
            const { payment_id, payment_date, payment_type } = record.relation_json || {};
            window.open(`${hostStrategy[env]}/invoice-management?toPayment=1&id=${payment_id}&date=${payment_date}&type=${payment_type}&now=${Date.now()}`)
          } else if (belong === 8) {
            const { month, group_id, adv_type } = record.relation_json || {};
            window.open(`${hostStrategy[env]}/write-off-manager?adv_group=${group_id}&adv_type=${adv_type}&month=${month}&now=${Date.now()}`)
          }

          const newSessionClickRecordIds = [...sessionClickRecordIds, record.id];
          setsessionClickRecordIds(newSessionClickRecordIds);
          sessionStorage.setStoredValue('take_action_click_record', JSON.stringify(newSessionClickRecordIds));
        }}>

          Take Action
        </Button>
      )
    ],
    btnGroup: [
      FlatTableBtnGroupType.Export,
      BtnGroupType.CustomColumn
    ],
    async beforeEdit(record) {
      return record;
    },
    beforeFilter(filters) {

      for (const key in filters) {
        if (!Object.prototype.hasOwnProperty.call(filters, key)) { continue };
        if (Array.isArray(filters[key])) {
          filters[key] = filters[key].join(OPTION_SEPARATOR)
        }
      }
      return {
        ...filters,
        global_user: GLOBAL_USER
      }
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/crmTodoManager/record', {
          ...params,
          sheetName: 'activity-record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'activity_record.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
  });

  const getOverallData = async () => {
    const res = await request.get('/crmTodo/overallData', { global_user: GLOBAL_USER });
    setOverallData(res.data.result);
  }

  const getAllRole = async () => {
    const { data } = await request.get('/role', { pageIndex: 1, pageSize: 9999 });
    setRoleList(data || []);
  };

  useEffect(() => {
    getAllRole();
  }, []);

  useEffect(() => {
    getOverallData();
    tableProps.fetchData()
  }, [GLOBAL_USER])

  return <section className="business-list-wrap todo-manage-wrap">
    <Row gutter={24}>
      <Col span={14}>
        <div className='todo-manage-overall'>

          <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            {
              isAdmin ? 'Overall - Admin' : (
                <>
                  {curUserInfo.nickname} - {curUserInfo.roleCode.map(code => {
                    const roleName = roleList.find(role => role.code === code)?.name;
                    return roleName;
                  }).join(',')}
                </>
              )
            }
            {
              (isAdmin || isBDLeader || isOPLeader) && (
                <Select
                  style={{ width: 200, marginLeft: 20 }}
                  options={GLOBAL_USER_OPTIONS}
                  value={GLOBAL_USER}
                  showSearch
                  allowClear
                  optionFilterProp='label'
                  onChange={(value, option) => {
                    console.log('31231', value, option)
                    SET_GLOBAL_USER(value);
                  }}
                  placeholder="Select to view only the data of a certain user"></Select>
              )
            }

          </div>
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
            <Statistic title="All Pending" value={overallData.all_pending ?? 0} />
            <div style={{ height: 30, width: 1, backgroundColor: '#bebebe' }}></div>
            <Statistic title="Finance" value={overallData.finance ?? 0} />
            <div style={{ height: 30, width: 1, backgroundColor: '#bebebe' }}></div>
            <Statistic title="Contract" value={overallData.contract ?? 0} />
            <div style={{ height: 30, width: 1, backgroundColor: '#bebebe' }}></div>
            <Statistic title="Leads" value={overallData.leads ?? 0} />
            <div style={{ height: 30, width: 1, backgroundColor: '#bebebe' }}></div>
            <Statistic title="Task Overdue" value={overallData.task_overdue ?? 0} valueStyle={{ color: '#cf1322' }} />
          </div>
        </div>
      </Col>
      <Col span={10}>
        <div className='todo-manage-quick-start'>
          <div className='todo-manage-quick-start-header' >
            <h2 style={{ fontWeight: 'bold', fontSize: 16 }}>Quick Start</h2>
            <Divider style={{ margin: '10px 0' }} />
            <Space style={{ display: 'flex', flexWrap: 'wrap' }}>
              <OperateAccess.Create locationPath='/operation/client-manage/personal'>
                <Button type='link' onClick={() => {
                  if (!userIsBD(curUserInfo)) {
                    message.warn('Only BD can create customers')
                    return;
                  }
                  window.open('/operation/client-manage/personal?type=open_create', '_blank');
                }}>Create Customer</Button>
              </OperateAccess.Create>

              <OperateAccess.Create locationPath='/operation/contract-manage/owned'>
                <Button type='link' onClick={() => {
                  window.open('/operation/contract-manage/owned?type=open_create', '_blank');
                }}>Create Contract</Button>
              </OperateAccess.Create>

              <Button type='link' onClick={() => {
                const env = window.__INITIAL_STATE__.env;
                const hostStrategy = {
                  'local': 'http://**************:8008',
                  'test': 'http://************:6517',
                  'prod': 'https://traffic.mobshark.net'
                }
                window.open(`${hostStrategy[env]}/invoice-management?toCustonInvoice=true`, '_blank');
              }}>Generate Custom Invoice</Button>
              <Button type='link' onClick={() => {
                const env = window.__INITIAL_STATE__.env;
                const hostStrategy = {
                  'local': 'http://**************:8008',
                  'test': 'http://************:6517',
                  'prod': 'https://traffic.mobshark.net'
                }
                window.open(`${hostStrategy[env]}/invoice-management?toPayment=true`, '_blank');

              }}>Match Payment</Button>
            </Space>
          </div>
        </div>
      </Col>
    </Row>

    <FlatTable {...tableProps} rowClassName={record => {
      if (sessionClickRecordIds.includes(record.id)) {
        return 'take-action-row-active'
      }
      return ''
    }} />
  </section>
}

export default BasePageHoc(TodoManageRecord)
