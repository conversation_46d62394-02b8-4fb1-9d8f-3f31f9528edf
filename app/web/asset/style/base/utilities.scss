@import '../common/mixins';

//
// Utility classes
// --------------------------------------------------
.clearfix {
  @include clearfix;
}

.pull-right {
  float: right !important;
}

.pull-left {
  float: left !important;
}

// text
// ---------------------
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.inherit-link {
  color: inherit;
  &:hover {
    color: inherit;
  }
}

// ---------------------------------------
.block {
  display: block !important;
}

.hide {
  display: none !important;
}

.auto-width {
  width: auto !important;
}

.v-middle {
  vertical-align: middle;
}

.pointer {
  cursor: pointer;
}

.flex {
  display: flex;
  &-vertical {
    @extend .flex;
    align-items: center;
  }
  &-horizontal {
    @extend .flex;
    justify-content: center;
  }
  &-space-between {
    @extend .flex;
    justify-content: space-between;
  }
}

.pre-wrap {
  white-space: pre-wrap;
}

.no-wrap {
  white-space: nowrap;
}

.text-ellipsis {
  @include text-ellipsis()
}

.placeholder {
  color: #939AA9;
}

// margin & padding gap
$step: 5;

@for $i from 1 to 7 {
  $value: $step * $i;

  .mt-#{$value} {
    margin-top: $value + px;
  }
  .mr-#{$value} {
    margin-right: $value + px;
  }
  .mb-#{$value} {
    margin-bottom: $value + px;
  }
  .ml-#{$value} {
    margin-left: $value + px;
  }
  .pt-#{$value} {
    padding-top: $value + px;
  }
  .pr-#{$value} {
    padding-right: $value + px;
  }
  .pb-#{$value} {
    padding-bottom: $value + px;
  }
  .pl-#{$value} {
    padding-left: $value + px;
  }
}

// body lock
.scroll-lock {
  height: 100vh;
  overflow: hidden !important;
  touch-action: none;
}