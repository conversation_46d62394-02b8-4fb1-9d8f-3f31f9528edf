@import './reset.scss';
@import './utilities.scss';

html {
  --n-text-color: rgb(51, 54, 57);
  --n-label-text-color: rgb(31, 34, 37);
  --n-item-color-active: rgba(100, 108, 255, 0.1);
  --n-text-color-hover: #646cff;
  --n-bezier: cubic-bezier(.4, 0, .2, 1);
  --base-text-color: rgb(31, 31, 31);
  --soy-primary-color: #646cff;

  overflow-x: hidden;
  // zoom: 0.9;

  // #app {
  //   height: 100%;
  // }

  /* 设置默认缩放为 90% */
}

body,
html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // font-family: v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

  #app,
  .ant-modal-root {


    // .ant-modal-confirm-btns {
    //   float: inherit;
    //   text-align: center;
    // }

    .ant-modal-confirm .ant-modal-confirm-btns .ant-btn {
      margin-left: 16px;
      height: 34px;

      *.ant-btn-primary {
        background-color: var(--soy-primary-color);
        color: #fff;
      }
    }

  }

  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next,
  .ant-pagination-item {
    height: 28px;
    min-width: 28px;
    line-height: 26px;
    border-radius: 6px;
  }

  .ant-pagination-item-link {
    border-radius: 6px !important;
  }


  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
  }



  .ant-select-single .ant-select-selector .ant-select-selection-item,
  .ant-select-single .ant-select-selector .ant-select-selection-placeholder,
  .ant-select-selection-item {
    line-height: 27px;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: rgba(0, 0, 0, 0.35);
  }

  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.35);
  }

  .ant-select-multiple .ant-select-selection-item {
    height: 22px;
    line-height: 21px;
    margin-top: 0;
  }

  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    color: rgba(0, 0, 0, 0.35);
  }

  .ant-select-multiple .ant-select-selection-item-remove>.anticon {
    vertical-align: -0.1em;
  }

  .ant-input,
  .ant-input-number,
  .ant-form-item-control-input,
  .ant-input-affix-wrapper,
  .ant-picker,
  .ant-mentions,
  .ant-select-show-search.ant-select-multiple .ant-select-selector {
    border-radius: 6px;
  }

  .ant-input-number {
    width: 100%;
  }

  .ant-input-number-input {
    width: 100%;

  }

  .form-list-ant-btn.ant-btn,
  .ant-btn-icon-only {
    width: 46px;
    height: 32px;

    .action {
      font-size: 12px;
    }
  }

  .ant-btn {
    height: 28px;
    font-size: 14px;
    padding: 0 14px;
    border-radius: 6px;
    font-weight: 400;
    transition: color .3s var(--n-bezier),
      background-color .3s var(--n-bezier),
      opacity .3s var(--n-bezier),
      border-color .3s var(--n-bezier);
  }

  .ant-btn-lg {
    height: 40px;
    line-height: 38px;
    font-size: 14px;
  }

  .ant-btn-link,
  .ant-btn-text {
    padding-left: 0;

    &:last-child {
      padding-right: 0;
    }
  }

  .ant-btn-sm {
    font-size: 12px;
    height: 24px;
  }

  .ant-layout-sider {
    background-color: #fff;
    box-shadow: 0 0 rgb(0 0 0 / 0), 0 0 rgb(0 0 0 / 0), 2px 0 8px 0 rgb(29, 35, 41, 0.05);
    z-index: 99;

    &.ant-menu-inline-collapsed {
      flex: 0 0 64px !important;
      max-width: 64px !important;
      min-width: 64px !important;
      width: 64px !important;
    }
  }

  .ant-menu-horizontal {
    border-bottom: none;
  }

  html .ant-menu .anticon {
    margin-right: 8px;
  }

  .ant-menu-submenu-title .anticon,
  .ant-menu-item .anticon {
    font-size: 18px;
  }

  .ant-menu-submenu-title .anticon+span {
    margin-left: 0;

  }

  .ant-layout-header {
    color: var(--n-text-color);
    // height: 48px;
    height: 100px;
    line-height: normal;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .3s;
    box-shadow: 0 0 rgb(0 0 0 / 0), 0 0 rgb(0 0 0 / 0), 0 1px 2px rgb(0, 21, 41, 0.08);
  }

  .ant-menu {
    .anticon {
      margin-right: 10px;
    }

    .ant-menu-sub.ant-menu-inline {
      background-color: #fff;
    }
  }

  .ant-table-thead {
    tr {
      th {
        padding: 15px 16px;
        font-weight: 500;
        color: var(--n-label-text-color);
        text-align: left;
        background: rgba(250, 250, 252, 1);
        font-weight: 500;

        &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
          width: 0;
        }

        &.ant-table-selection-column {
          text-align: center;
        }
      }
    }
  }

  .ant-table-content,
  .ant-table-body {
    scrollbar-color: rgba(0, 0, 0, .16) rgba(0, 0, 0, 0);
    scrollbar-width: thin;
  }

  .ant-table-tbody {
    tr {
      td {
        overflow-wrap: break-word;
        border-bottom: 1px solid rgba(239, 239, 245, 1);
        transition: all 0.3s, border 0s;
        padding: 12px 16px;
        color: rgb(51, 54, 57);
      }
    }
  }



  .ant-select-multiple .ant-select-selection-search {
    margin-top: 0;
    margin-bottom: 0;
  }

  .ant-select-multiple .ant-select-selector {
    height: 100%;
    min-height: 100%;
    padding-top: 0;
    padding-bottom: 0;
    overflow: hidden;
    // overflow-y: scroll;
    // white-space: nowrap;
    // text-overflow: ellipsis;
    // -o-text-overflow: ellipsis;
  }

  .ant-popover-inner-content {
    padding: 8px 16px;

    p {
      margin-bottom: 5px;
    }
  }

  // 表格
  .ant-table {
    font-size: 14px;
    border: 1px solid rgba(239, 239, 245, 1);
    border-radius: 6px;
  }

  .ant-table-thead>tr>th,
  .ant-table-tbody>tr>td,
  .ant-table tfoot>tr>th,
  .ant-table tfoot>tr>td {
    padding: 8px;
  }



  // operation-bar
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.mb-0 {
      margin-bottom: 0;
      padding-bottom: 0;
      padding-top: 0;
    }

    &.flex-end {
      display: flex;
      justify-content: flex-end;
      align-self: flex-start;
    }

    .ant-btn {
      height: 34px;

      &.filter-btn-query {
        border-color: var(--ant-primary-color);
        color: var(--ant-primary-color);
      }
    }

    h2 {
      font-size: 16px;
    }
  }


  .ant-form-item-label {
    overflow: initial;

    >label {
      &:before {
        display: inline-block;
        margin-right: 4px;
        color: white;
        font-size: 14px;
        line-height: 1;
        content: '*';
        padding-top: 3px;
      }
    }
  }

  .schema-form-editor {
    display: flex;
    align-items: stretch;

    .schema-input {
      flex: 1;
    }

    .schema-translate-icon {
      width: 50px;
      color: #ccc;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .schema-form {
      flex: 1;
      border: 1px solid rgba($color: #000, $alpha: 0.1);
      padding: 15px 11px 5px;
      border-radius: 2px;

      .ant-form-item {
        display: flex;
        margin-bottom: 10px;

        .ant-col-24 {
          &:first-child {
            flex: 0 0 auto;
            margin-right: 10px;
          }

          &:last-child {
            flex: 1;
          }
        }
      }
    }
  }

  .ant-modal-header {
    padding: 20px 24px;
    border-bottom: none;
  }

  .ant-modal-title {
    color: var(--n-label-text-color);
    font-size: 18px;
    font-weight: 500;
  }

  .ant-modal-close-x {
    width: 44px;
    height: 44px;
    line-height: 44px;
  }

  .ant-modal-body {
    padding: 16px 24px;
    font-size: 13px;
  }

  .ant-modal-footer {
    text-align: right;

    .ant-btn {
      height: 34px;
    }

    .ant-btn-primary {
      background-color: var(--soy-primary-color);
      color: #fff;
      font-size: 14px;
    }
  }

  .ant-form-item-label>label {
    color: var(--n-label-text-color);
    font-size: 14px;
    font-weight: 400;

    span {
      word-break: break-word;
      white-space: pre-wrap;
      // white-space: nowrap !important;
      // overflow: hidden;
      // text-overflow: ellipsis;
    }
  }

  .ant-form-item-explain,
  .ant-form-item-extra {
    font-size: 12px;
  }

  .ant-tabs-card.ant-tabs-top>.ant-tabs-nav .ant-tabs-tab+.ant-tabs-tab,
  .ant-tabs-card.ant-tabs-bottom>.ant-tabs-nav .ant-tabs-tab+.ant-tabs-tab,
  .ant-tabs-card.ant-tabs-top>div>.ant-tabs-nav .ant-tabs-tab+.ant-tabs-tab,
  .ant-tabs-card.ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-tab+.ant-tabs-tab {
    margin-left: 0;
  }

  .ant-tabs-card.ant-tabs-top>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-card.ant-tabs-top>div>.ant-tabs-nav .ant-tabs-tab {
    border-radius: 0;
  }

  .ant-tabs-card>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-card>div>.ant-tabs-nav .ant-tabs-tab {
    border: 1px solid #eee;
    border-left: none;
    border-bottom-color: #fafafa;
    border-top-color: #fafafa;

    &:first-child {
      border-left: 1px solid #eee;
    }
  }

  .ant-tabs-card>.ant-tabs-nav .ant-tabs-tab-active,
  .ant-tabs-card>div>.ant-tabs-nav .ant-tabs-tab-active {
    background-color: #2d83fd;
    border: 1px solid #2d83fd;
    border-left: 1px solid #2d83fd;

    &:first-child {
      border-left: 1px solid #2d83fd;
    }
  }

  .ant-tabs-card.ant-tabs-top>.ant-tabs-nav .ant-tabs-tab-active,
  .ant-tabs-card.ant-tabs-top>div>.ant-tabs-nav .ant-tabs-tab-active {
    border-bottom-color: transparent;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #fff;
  }

  .ant-tabs-small>.ant-tabs-nav .ant-tabs-tab {
    font-size: 12px;
  }

  .operation-title {
    font-weight: bold;
    font-size: 14px;
  }

  .ant-tabs-top>.ant-tabs-nav,
  .ant-tabs-bottom>.ant-tabs-nav,
  .ant-tabs-top>div>.ant-tabs-nav,
  .ant-tabs-bottom>div>.ant-tabs-nav {
    margin: 0 0 8px 0;
  }

  .ant-col-0 {
    width: 0;
  }

  .ant-form-item-with-help .ant-form-item-explain {
    // display: none;
  }

  // .ant-form-item-control {
  //   overflow: hidden;
  // }
  .ant-select-selection-overflow {
    height: 100%;
    align-content: baseline;
  }
}

.ant-table-thead th.ant-table-column-sort {
  background-color: rgba(250, 250, 252, 1);
}

.ant-btn-default {
  &:hover {
    color: var(--n-text-color-hover);
    border-color: var(--n-text-color-hover);
  }
}


.ant-btn-primary {
  border-color: var(--ant-primary-color);
  color: var(--ant-primary-color);
  background-color: transparent;

  &:hover {
    background-color: inherit;
    color: var(--ant-primary-color);
  }
}

.ant-form-item * .ant-form-item {
  margin-bottom: 0;
}

.ant-drawer-body {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, .5) transparent;
}

.ant-form-vertical .ant-form-item-label {
  padding: 0 0 6px;
}

.ant-dropdown-menu {
  border-radius: 6px;

  .ant-dropdown-menu-item {
    padding: 8px 12px;
  }
}

.ant-tag {
  height: 28px;
  line-height: 28px;
  border-radius: 6px;
}

.ant-select-arrow {
  color: rgba(194, 194, 194, 1);
}

.ant-btn-dangerous[disabled] {
  color: #f5222d;
  background-color: #0000;
  border: 1px solid #f5222d;
  opacity: 0.5;
}

.ant-select-selection-placeholder {
  color: #c2c2c2;
}

.ant-btn-primary[disabled] {
  color: rgba(0, 0, 0, .25) !important;
  border-color: #d9d9d9 !important;
}

.link-jump-text {
  color: var(--ant-primary-color);
  cursor: pointer;
}

.ant-modal-mask {
  z-index: 998;
}

.ant-modal-wrap {
  z-index: 999;
}

.ant-drawer,
.ant-drawer-mask,
.ant-drawer-content-wrapper {
  // z-index: 1050;
}

.ant-notification {
  z-index: 1100;
}

.description-text {
  font-weight: bold;
  width: 80px;
  text-align: right;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
