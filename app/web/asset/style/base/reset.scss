article,
aside,
blockquote,
body,
button,
code,
dd,
details,
div,
dl,
dt,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hr,
iframe,
input,
legend,
li,
menu,
nav,
ol,
p,
pre,
section,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body,
button {
  font-size: 12px;
  line-height: 1.5;
}

fieldset,
img {
  border: 0;
}

img {
  max-width: 100%;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

button {
  font-feature-settings: normal;
  font-kerning: auto;
  font-optical-sizing: auto;
  font-stretch: 100%;
  font-style: normal;
  font-variant-alternates: normal;
  font-variant-caps: normal;
  font-variant-east-asian: normal;
  font-variant-ligatures: normal;
  font-variant-numeric: normal;
  font-variant-position: normal;
  font-variation-settings: normal;
}

button,
input,
select,
textarea {
  outline: 0;
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
  -webkit-appearance: none;
}

textarea {
  resize: none;
  overflow: auto;
}

a:active,
a:hover,
a:link,
a:visited {
  text-decoration: none;
}

em,
i {
  font-style: normal;
}

ol,
ul {
  list-style: none;
}

[v-cloak] {
  display: none;
}

iframe {
  border: none;
}

b {
  font-weight: normal;
}

::-webkit-input-placeholder {
  color: rgba($color: #939aa9, $alpha: 0.5);
}

:-moz-placeholder,
::-moz-placeholder {
  color: rgba($color: #939aa9, $alpha: 0.5);
}

:-ms-input-placeholder {
  color: rgba($color: #939aa9, $alpha: 0.5);
}

/* 去掉默认填充的黄色底色 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px white inset !important;
}
