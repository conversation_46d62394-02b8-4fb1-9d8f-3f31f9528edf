


@function strip-units($number){
  @return $number / ($number * 0 + 1);
}

@function px2rem($px){
  @return strip-units($px) / 100 * 1rem;
}


@mixin ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin ellipsis-m($line:2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: $line;
  word-break: break-word;
}

@mixin clearfix() {
  &::after {
    content: "";
    display: block;
    clear: both;
  }
}

@mixin text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin transition($property: all, $duration: .4s, $timing-function: ease, $delay: 0s) {
  transition: $property $duration $timing-function $delay;
}