<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Flat Ads CRM</title>
    <meta name="keywords" content="Flat,Application Platform,Create Application" />
    <meta name="description" content="Flat Ads CRM" />
    <meta http-equiv="content-type" content="text/html;charset=utf-8" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <script src="/public/lib/react.production.min.js"></script>
    <script src="/public/lib/react-dom.production.min.js"></script>
    <script src="/public/lib/moment.min.js"></script>
    <script src="/public/lib/moment-timezone.min.js"></script>
    <script src="/public/lib/axios.min.js"></script>
    <!-- <script src="/public/lib/antd.min.js"></script> -->
    <script src="/public/lib/lodash.min.js"></script>
    <!-- <link type="text/css" rel="stylesheet" href="/public/lib/antd.min.css" /> -->
  </head>
  <body>
    <main id="app">
      <style type="text/css">
        @keyframes antSpinMove {
          to {
            opacity: 1;
          }
        }
        @keyframes antRotate {
          to {
            transform: rotate(405deg);
          }
        }
        .ant-spin {
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          margin: 0;
          padding: 0;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
          font-variant: tabular-nums;
          line-height: 1.5715;
          list-style: none;
          -webkit-font-feature-settings: 'tnum';
          font-feature-settings: 'tnum';
          position: absolute;
          display: none;
          color: #1890ff;
          text-align: center;
          vertical-align: middle;
          opacity: 0;
          -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
          transition: -webkit-transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
          transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
          transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86), -webkit-transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
        }
        .ant-spin-spinning {
          position: static;
          display: inline-block;
          opacity: 1;
        }
        .ant-spin-dot {
          position: relative;
          display: inline-block;
          font-size: 20px;
          width: 1em;
          height: 1em;
        }
        .ant-spin-dot-spin {
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
          -webkit-animation: antRotate 1.2s infinite linear;
          animation: antRotate 1.2s infinite linear;
        }
        .ant-spin-lg .ant-spin-dot {
          font-size: 32px;
        }
        .ant-spin-lg .ant-spin-dot i {
          width: 14px;
          height: 14px;
        }
        .ant-spin-dot-item:nth-child(1) {
          top: 0;
          left: 0;
        }
        .ant-spin-dot-item:nth-child(2) {
          top: 0;
          right: 0;
          -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
        }
        .ant-spin-dot-item:nth-child(3) {
          right: 0;
          bottom: 0;
          -webkit-animation-delay: 0.8s;
          animation-delay: 0.8s;
        }
        .ant-spin-dot-item:nth-child(4) {
          bottom: 0;
          left: 0;
          -webkit-animation-delay: 1.2s;
          animation-delay: 1.2s;
        }
        .ant-spin-dot-item {
          position: absolute;
          display: block;
          width: 9px;
          height: 9px;
          background-color: #1890ff;
          border-radius: 100%;
          -webkit-transform: scale(0.75);
          transform: scale(0.75);
          -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
          opacity: 0.3;
          -webkit-animation: antSpinMove 1s infinite linear alternate;
          animation: antSpinMove 1s infinite linear alternate;
        }

        .position-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      </style>
      <div class="ant-spin ant-spin-lg ant-spin-spinning position-center">
        <span class="ant-spin-dot ant-spin-dot-spin">
          <i class="ant-spin-dot-item"></i>
          <i class="ant-spin-dot-item"></i>
          <i class="ant-spin-dot-item"></i>
          <i class="ant-spin-dot-item"></i>
        </span>
      </div>
    </main>
    <script type="text/javascript" src="https://res.jscssfunny.com/fe/pub/js/operation-sdk-min.js"></script>
  </body>
</html>
