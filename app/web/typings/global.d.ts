declare var EASY_ENV_IS_NODE: boolean;
declare var EASY_ENV_IS_DEV: boolean;
declare var EASY_ENV_IS_BROWSER: boolean;
declare var process: {
  env: {
    NODE_ENV: string;
  };
};
interface Window {
  __INITIAL_STATE__: any;
  gaEvent: (eventCategory: string, eventAction: string) => void;
  stores: any;
  DDLogin: any;
  attachEvent: any;
}
interface Document {
  [key: string]: any;
}
interface NodeModule {
  hot: {
    accept: any;
  };
}

interface Recordable {
  [key: string]: any;
}

interface ICommonRecord {
  label: string;
  value: number | string;
}
