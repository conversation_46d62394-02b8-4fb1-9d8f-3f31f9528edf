import axios from 'axios';
import { isPureObject } from '@@/lib/tool';
import { message } from 'antd';
import { __ } from '@/modules/format';
import { UN_AUTHORIZED_CODE } from '@flat-design/egg-login-verify';

const instance: any = axios.create({
  baseURL: '/api',
  timeout: 60000, // 请求超时时间 60s
  withCredentials: true, // 允许跨域传递 cookie
  headers: {
    // 接口不缓存
    common: {
      'If-Modified-Since': 'Mon, 26 Jul 1997 05:00:00 GMT',
      'Cache-Control': 'no-cache',
      Pragma: 'no-cache'
    }
  },
  xsrfCookieName: 'csrfToken'
});

const ServerError = __('ServerError');
const getFunctionOrigin: any = instance.get;
const PARAMS = 'params';

// 请求拦截
instance.interceptors.request.use(
  config => {
    // 根据业务，自定义一些header字段
    config.headers['X-REDIRECT-URL'] = window?.location?.href;
    config.headers.pathname = window?.location?.pathname;
    return config;
  },
  error => {
    // reject
    return Promise.reject(error);
  }
);

// 响应拦截
instance.interceptors.response.use(
  response => {
    // message.destroy();
    // 简单处理
    const { data, config, headers } = response;
    if (headers['content-type'] === 'application/octet-stream') {
      return data;
    }
    if (config.stream) {
      return response;
    }
    const { isSuccess, msg, code } = data;
    if (!isSuccess) {
      if (config['X-showMessage'] !== false) {
        message.error(msg || ServerError, 2, () => {
          if (code === UN_AUTHORIZED_CODE) {
            window.location.href = `/login`;
          }
        });
      }
      return Promise.reject(data);
    }

    if (config['X-showMessage'] === true) {
      message.success(msg || ServerError);
    }
    const e = new Event('resize');
    window.dispatchEvent(e); // 解决antd stick 滚动条问题
    return data;
  },
  error => {
    let msg = 'request error!';
    if (error.response?.data?.status) {
      msg = error.response.data.status + ' - ' + error.response.data.message;
    }
    message.error(msg);
    // reject
    return Promise.reject(error);
  }
);

// get抹平与post的参数差异：get(url[, data[, config]])
instance.get = (url, data?, config?: any) => {
  const configuration = { [PARAMS]: undefined };
  if (isPureObject(data)) {
    if (data && data.hasOwnProperty(PARAMS)) {
      Object.assign(configuration, data, config);
    } else {
      Object.assign(configuration, { [PARAMS]: data }, config);
    }
  } else {
    if (isPureObject(config)) {
      Object.assign(configuration, config);
    }
  }
  return getFunctionOrigin.call(instance, url, configuration);
};

export default instance;
