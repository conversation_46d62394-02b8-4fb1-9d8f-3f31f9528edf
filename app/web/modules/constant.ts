// 后台初始化需要配置的内容如下：
const AppConfig = {
  name: '后台官方名称XXX', // 后台官方名称
  slogan: '后台slogan（可选）', // 后台slogan
  themeColor: '#2d83fd'
};
// 后台初始化需要配置的内容结束

const BaseData = {
  type: [
    { text: 'H5', value: 'H5' },
    { text: 'PC', value: 'PC' },
    { text: 'PWA', value: 'PWA' }
  ],
  authType: [
    { text: '菜单', value: 'menu' },
    { text: '接口', value: 'api' },
    { text: '操作', value: 'action' }
  ]
};

const CpTypeConfig = {
  '1': '代投',
  '2': '纯开户',
  '3': '二级代理',
  '4': '厂商买量'
};

const AccountStatus = {
  '1': '上线',
  '-1': '被封',
  '2': '受限',
  '3': '备用',
  '4': '暂停使用',
  '5': '删除'
};

const OrderStatus = {
  1: '下单',
  2: '开投',
  3: '停投',
  4: '重启'
};

const monitorRules = {
  target: {
    roi: 'ROI',
    expense: '花费'
  },
  dayNumber: {
    min: 1,
    max: 7
  },
  compare: {
    '>': '大于',
    '>=': '大于等于',
    '<': '小于',
    '<=': '小于等于'
  }
};

export { BaseData, AppConfig, CpTypeConfig, AccountStatus, OrderStatus, monitorRules };
