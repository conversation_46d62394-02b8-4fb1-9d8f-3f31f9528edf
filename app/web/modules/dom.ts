/**
 * 平滑滚动到目标元素
 * @param element 目标元素
 */
export const scrollTo = (element: any) => {
  // 设置滚动行为改为平滑的滚动
  // 本来移动到顶
  const top = element.getBoundingClientRect().top + window.scrollY;
  // 居中偏移
  const centerOffset = (element.offsetHeight - window.innerHeight) / 2;
  window.scrollTo({
    top: top + centerOffset,
    behavior: 'smooth'
  });
};

/**
 * 多个图片实现懒加载
 * @param selector
 *
 * useEffect(() => {
    if (res?.data.rows.length) {
      lazyLoadImage('.ant-image');
    }
  }, [res]);
 * 用例：
 * <Image.PreviewGroup>
    {data.photo_src.map((src, index, srcs) => (
      <div key={index}>
        <Image src={require('@/asset/images/image-holder.png')} data-src={src} className="lazy-load" preview={{ src }} alt="screenshot" />
        <Checkbox defaultChecked={hanleChecked(data, index)} onChange={e => changeScreenshotStatus(e, data, index)}>
          Violating Content
        </Checkbox>
      </div>
    ))}
  </Image.PreviewGroup>
  或者
   <Image src={require('@/asset/images/image-holder.png')} data-src={src} className="lazy-load" alt="screenshot" />
 */
export const lazyLoadImage = (selector = '.lazy-load') => {
  var lazyImages = [].slice.call(document.querySelectorAll(selector));

  if ('IntersectionObserver' in window) {
    let lazyImageObserver = new IntersectionObserver(function (entries, observer) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          let lazyImage: any = entry.target;
          lazyImage.src = lazyImage.dataset.src;
          lazyImageObserver.unobserve(lazyImage);
        }
      });
    });

    lazyImages.forEach(function (lazyImage: HTMLElement) {
      let image: HTMLImageElement;
      if (selector === '.ant-image') {
        image = (lazyImage as HTMLDivElement).firstChild as HTMLImageElement;
      } else {
        image = lazyImage as HTMLImageElement;
      }

      if (!image.dataset.src && !lazyImage.dataset.src) {
        throw new Error('lazy-load:请设置data-src');
      }

      if (!image.dataset.src && lazyImage.dataset.src) {
        image.dataset.src = lazyImage.dataset.src;
      }
      lazyImageObserver.observe(image);
    });
  } else {
    // Possibly fall back to event handlers here
    console.log('lazyLoadImage not support IntersectionObserver in window');
  }
};
