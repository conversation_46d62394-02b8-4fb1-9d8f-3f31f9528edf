export default function (list, key = 'auth_code', parentKey = 'parent') {
  if (!list) {
    return;
  }
  const map = {};
  let node;
  const tree = {};
  let i: number;

  for (i = 0; i < list.length; i += 1) {
    map[list[i][key]] = i; // initialize the map
    list[i].children = []; // initialize the children
    tree[list[i].type] = [];
  }

  for (i = 0; i < list.length; i += 1) {
    node = list[i];
    if (node[parent<PERSON><PERSON>] !== '0') {
      // if you have dangling branches check that map[node[parent<PERSON><PERSON>]] exists
      list[map[node[parentKey]]].children.push(node);
    } else {
      tree[node.type].push(node);
    }
  }
  return tree;
}

export const hasAuth = (list, name) => {
  return list && list.find(item => item.name === name);
};

export const authList = list => {
  if (!list) {
    return [];
  }
  let authList: any[] = [];
  list.forEach(item => {
    if (item.children && item.children.length > 0) {
      item.children.forEach((child: any) => {
        authList.push(child.name);
      });
    } else {
      authList.push(item.name);
    }
  });
  return authList;
};
