const loadScript = url => {
  return new Promise((resolve, reject) => {
    const body = document.getElementsByTagName('body')[0] || document.documentElement;
    const script: any = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    let done = false;

    body.appendChild(script);

    script.onload = script.onreadystatechange = function () {
      if (!done && (!this.readyState || this.readyState === 'loaded' || this.readyState === 'complete')) {
        done = true;
        resolve();
        // Handle memory leak in IE
        script.onload = script.onreadystatechange = null;
        if (body && script.parentNode) {
          body.removeChild(script);
        }
      }
    };
  });
};

export { loadScript };
