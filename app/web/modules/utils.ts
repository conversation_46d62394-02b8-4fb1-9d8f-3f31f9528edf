
/** 获取地址栏参数 */
export const getUrlParams = (url: string, paramName?: string) => {
  const urlObj: any = new URL(url);
  const searchParams = urlObj.searchParams;
  const entries = searchParams.entries();
  if (paramName) {
    return Object.fromEntries(entries)[paramName];
  } else {
    return Object.fromEntries(entries);
  }
};

/** 是否为超级管理员 */
export function isSuperAdmin() {
  let result = false;
  // @ts-ignore
  const authObj = window.__INITIAL_STATE__?.auth ?? {};
  if (authObj.userRole) {
    const userRoleList = authObj.userRole ?? [];
    for (const userRole of userRoleList) {
      if (userRole?.role_code === 'role-superadmin') {
        result = true;
      }
    }
  }
  return result;
}

export function enterFullscreen() {
  if (document.documentElement.requestFullscreen) {
    document.documentElement.requestFullscreen();
    // @ts-ignore
  } else if (document.documentElement.mozRequestFullScreen) { /* Firefox */
    // @ts-ignore
    document.documentElement.mozRequestFullScreen();
    // @ts-ignore
  } else if (document.documentElement.webkitRequestFullscreen) { /* Chrome, Safari and Opera */
    // @ts-ignore  
    document.documentElement.webkitRequestFullscreen();
    // @ts-ignore 
  } else if (document.documentElement.msRequestFullscreen) { /* IE/Edge */
    // @ts-ignore   
    document.documentElement.msRequestFullscreen();
  }
}

// 退出全屏
export function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.mozCancelFullScreen) { /* Firefox */
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) { /* Chrome, Safari and Opera */
    document.webkitExitFullscreen();
  } else if (document.msExitFullscreen) { /* IE/Edge */
    document.msExitFullscreen();
  }
}

export function isFullscreen() {
  return (
    document.fullscreenElement ||
    document.mozFullScreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  );
}


export const urlsToAntdUploadList = (urls: string) => {
  if (!urls) {
    return [];
  }
  return urls.split(',').map((src, idx) => {
    const name = src.split('/').pop();
    return {
      url: src,
      name: name,
      uid: idx,
      status: 'done'
    }
  })
}
