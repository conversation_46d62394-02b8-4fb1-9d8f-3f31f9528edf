export function formatDate(date, format = 'yyyy-MM-dd') {
  // date = null or '', then return
  if (!date) {
    return '';
  }

  // check is date type or not
  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  if (String(date) === 'Invalid Date') {
    return 'Invalid Date';
  }

  const o = {
    'M+': date.getMonth() + 1, // month
    'd+': date.getDate(), // day
    'h+': date.getHours(), // hour
    'm+': date.getMinutes(), // minute
    's+': date.getSeconds(), // second
    'q+': Math.floor((date.getMonth() + 3) / 3), // quarter
    S: date.getMilliseconds() // millisecond
  };

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  if (/(D)/.test(format)) {
    format = format.replace(RegExp.$1, ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'][date.getDay()]);
  }

  for (const k in o) {
    if (o.hasOwnProperty(k) && new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }

  return format;
}

/**
 * i18n多语言转化
 * 支持拼接，如果第一个参数包含占位符，则走占位符逻辑
 */
export const __ = (...rest) => {
  const { lang, localeData } = window.__INITIAL_STATE__;
  const arr = Array.from(rest);
  if (localeData[rest?.[0]].includes('%s')) {
    return gettext(...rest);
  }
  const valArr = arr.map((item, index) => {
    return index ? localeData?.[item]?.toLowerCase() : localeData?.[item];
  });
  return valArr.join(lang === 'zh' ? '' : ' ');
};
const format = (...args) => {
  const formatRegExp = /%[sdj%]/g;
  if (args.some(s => typeof s !== 'string')) {
    throw new Error('参数只支持字符串');
  }
  let i = 1;
  const len = args.length;
  const str = String(args[0]).replace(formatRegExp, (x: any) => {
    // 核心方法，也就是这段正则的匹配了。
    if (x === '%%') {
      return '%';
    }
    if (i >= len) {
      return x;
    }
    switch (x) {
      case '%s':
        return String(args[i++]);
      // case '%d':
      //   return Number(args[i++]);
      // case '%j':
      //   try {
      //     return JSON.stringify(args[i++]);
      //   } catch (_) {
      //     return '[Circular]';
      //   }
      default:
        return x;
    }
  });
  return str;
};

/**
 * 通过key名获取对应文案，支持多个占位符，比如语言包里有
 * en: Hi: 'Hi %s，I am %s'
 * zh: Hi: '你好%s，我是%s'
 * 代码中使用 gettext('HiThere', 'Jack', "Tom")
 * 英文下输出：Hi Jack, I am Tom
 */
export const gettext = (...args) => {
  const { localeData } = window.__INITIAL_STATE__;
  const key = args?.[0];
  const value = localeData?.[key] || key;
  return format(value, ...args.slice(1));
};

export default {
  formatDate
};
