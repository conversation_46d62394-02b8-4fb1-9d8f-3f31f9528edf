import useRouterStore, { PathState, PathTo } from '../store/router.store';
import { useHistory } from 'react-router-dom';
import useSelector from './useSelector';


export function useRouterState() {
  const history = useHistory();
  const {
    routeState,
    setRouteState,
    removeRouteState
  } = useRouterStore(
    useSelector(['routeState', 'setRouteState', 'removeRouteState'])
  );

  function push(to: PathTo, state?: PathState) {
    const from = location.pathname;
    const payload = {
      ...state,
      $$from: from,
      $$to: to
    };
    setRouteState(`${from}=>${to}`, payload);
    history.push(to, payload);
  }

  function replace(to: PathTo, state?: PathState) {
    const from = location.pathname;
    const payload = {
      ...state,
      $$from: from,
      $$to: to
    };
    setRouteState(`${from}=>${to}`, payload);
    history.replace(to, payload);
  }

  function getState(from: PathTo) {
    const to = location.pathname;
    return routeState.get(`${from}=>${to}`);
  }

  function removeState(from: PathTo) {
    const to = location.pathname;
    removeRouteState(`${from}=>${to}`);
  }

  return {
    push,
    replace,
    getState,
    removeState
  };
}
