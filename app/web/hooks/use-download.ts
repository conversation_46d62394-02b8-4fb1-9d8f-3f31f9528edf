import request from '@/modules/request';

function UseDownload() {
  const exportExcel = async (url, params, filename = 'export.csv') => {
    const result = await request.post(url, { pageSize: 5000, pageIndex: 1, ...params }, { stream: true, responseType: 'blob' }).catch(err => {
      return false;
    });
    // console.log("result: ", result);
    if (!result) {
      return Promise.reject(result.msg || 'Export failed');
    }
    downloadFile(result.data, 'text/csv;charset=utf-8', filename);
  };

  const exportTxt = async (url, params, filename = 'file.txt') => {
    const result = (await request.post(url, { pageSize: 5000, pageIndex: 1, ...params }, { stream: true, responseType: 'blob' })) as any;
    downloadFile(result.data, 'application/octet-stream', filename);
  };

  // 下载文件
  const downloadFile = (data: any, type: string, downloadName: string) => {
    const blob = new Blob([data], { type });
    // const bufferData = Buffer.from(data, 'binary');
    // const blob = new Blob([bufferData], { type });
    const newUrl = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = newUrl;
    a.download = downloadName; // 这里的文件名可以在请求下载文件哪里获取
    a.click();
    window.URL.revokeObjectURL(a.href);
  };

  return {
    exportExcel,
    downloadFile,
    exportTxt
  };
}

export const fileDownload = (file: any) => {
  // 创建一个Blob URL
  const url = URL.createObjectURL(file);

  // 创建一个隐藏的下载链接
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', file.name);

  // 触发点击事件
  document.body.appendChild(link);
  link.click();

  // 释放URL对象
  URL.revokeObjectURL(url);
  document.body.removeChild(link);
}

export const urlDownload = async (fileUrl: string, fileName: string) => {
  const response = await fetch(fileUrl);
  const blob = await response.blob();

  // 使用 URL.createObjectURL 创建 Blob URL
  const url = URL.createObjectURL(blob);

  // 创建隐藏的下载链接
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.type = 'application/octet-stream';  // 强制下载

  // 触发下载
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // 释放 Blob URL
  URL.revokeObjectURL(url);
}

export default UseDownload;
