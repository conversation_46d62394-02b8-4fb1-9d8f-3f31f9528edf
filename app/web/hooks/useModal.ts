import { useState, useEffect } from 'react';
import emitter from '@/modules/emitter';
import { makeCode } from '@@/lib/tool';

export default function () {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<any>(null);
  const [type, setType] = useState('add'); // add/edit
  const [modalName, setModalName] = useState(`modal.${makeCode(10)}`);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [refreshId, setRefreshId] = useState(0); // 刷新id，自增

  useEffect(() => {
    // 监听事件
    emitter.on(modalName, handleType);
    setModalName(modalName);

    function handleType(type, item) {
      setData(item);
      setType(type);
      setVisible(true);
    }
  }, []);

  useEffect(() => {
    if (visible) {
      setRefreshId(count => count + 1);
    }
  }, [visible]);

  function emit(type, item) {
    emitter.emit(modalName, type, item);
  }

  return {
    visible,
    setVisible,
    refreshId,
    setRefreshId,
    type,
    setType,
    modalName,
    setModalName,
    data,
    setData,
    confirmLoading,
    setConfirmLoading,
    emit
  };
}
