/**
 * The common state and function for common page list
 * author: wujj
 * date" 2020-04-23
 */
import { useState, useEffect } from 'react';
import request from '@/modules/request';

export default (initStates: any = {}) => {
  const states = Object.assign(
    {},
    {
      loading: true,
      fetchUrl: '',
      pageSize: 20
    },
    initStates
  );

  // 弹层按钮状态
  const [btnLoading, setBtnLoading] = useState(false);
  // 弹层按钮状态
  const [fetchUrl, setFetchUrl] = useState(states.fetchUrl);
  // 分页index
  const [pageIndex, setPageIndex] = useState(1);
  // 表格数据
  const [listData, setListData] = useState<any>([]);
  // 单条数据
  const [itemData, setItemData] = useState<any>({});
  // 查询数据总数
  const [total, setTotal] = useState(0);
  // 分页size
  const [pageSize, setPageSize] = useState(states.pageSize);
  // loading
  const [loading, setLoading] = useState(states.loading);
  // 刷新id，自增
  const [refreshId, setRefreshId] = useState(0);
  const [extraData, setExtraData] = useState<any>({});

  // 获取列表数据
  function fetchData(params: any = {}) {
    if (!fetchUrl) {
      return Promise.reject();
    }
    setLoading(true);
    return request
      .get(
        fetchUrl,
        Object.assign(
          {
            pageIndex,
            pageSize
          },
          params
        )
      )
      .then((res: any) => {
        const { data, total, extra } = res;
        for (let i = 0; i < data.length; i++) {
          const item = data[i];

          if (typeof item === 'object') {
            item.key = item.id ? item.id + '_' + i : Math.random();
          }
        }
        setListData(data);
        setTotal(total);

        if (extra) {
          setExtraData(extra);
        }

        const proto: any = Reflect.getPrototypeOf(data); // 将total添加到list原型
        Reflect.set(proto, '_total', total);

        return Promise.resolve(data);
      })
      .catch((error) => {
        return Promise.reject(error);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleItemData(data) {
    const { id } = data;
    if (id) {
      // @ts-ignore
      const target = listData.find(item => item.id === id);
      if (target) {
        Object.assign(target, data);
      } else {
        listData.unshift(data);
      }

      setListData(listData);
      setRefreshId(count => count + 1);
    }
  }

  // 处理表格变化，这里主要处理升降序
  function handleTableSort(pagination, filters, sorter, extra) {
    const { field = states.sortBy, order = states.orderBy } = sorter;
    // setOrderBy(order.replace('end', ''));
    // setSortBy(field);
  }

  // 成为对外接口
  return {
    loading,
    setLoading,
    btnLoading,
    setBtnLoading,
    fetchUrl,
    setFetchUrl,
    pageIndex,
    setPageIndex,
    pageSize,
    setPageSize,
    listData,
    setListData,
    total,
    setTotal,
    fetchData,
    refreshId,
    setRefreshId,

    handleTableSort,
    handleItemData,
    itemData,
    setItemData,
    extraData,
    setExtraData,
  };
};
