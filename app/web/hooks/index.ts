import useModal from './useModal';
import useQueryList from './useQueryList';
import useAuth from './useAuth';
import useDownload from './use-download';
import { useLocalStorage, useSessionStorage } from './useStorage'
import { useContext, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { clearUrlParameter } from '@@/lib/tool';
import { AppContext } from '@/store';
import { UploadProps } from 'antd';


export { useModal, useQueryList, useAuth, useDownload, useLocalStorage, useSessionStorage };

export const clearUrlSearchEffect = () => {
  const curLocation = useLocation();
  const searchParams = new URLSearchParams(location.search);

  useEffect(() => {
    if (curLocation.pathname !== location.pathname) {
      return;
    }
    clearUrlParameter();
  }, [searchParams.size])
}


export const useUploadProps = (url: string) => {
  const { csrf } = useContext(AppContext);
  const uploadProps: UploadProps = {
    name: 'file',
    action: url, // 校验Excel路由
    accept: '.xlsx, .xls',
    headers: {
      'X-XSRF-TOKEN': csrf
    },
    itemRender: () => null,
  }

  return uploadProps
}
