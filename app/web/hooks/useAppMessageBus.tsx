import { useEffect } from 'react';
import { Button, notification, Row, Space, Tag } from 'antd';
import useUserStore from '../store/user.store';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../../lib/systemEvent'
import React from 'react';
import { isValidArray, sleep } from '../../lib/tool'
import { HISOTRY_STASH } from '../router/historyStash'
import useNotificationStore, { getUnreadCount } from '@/store/notification.store';
import useSelector from './useSelector';

/**
 * @description 系统消息总线，负责接收、处理来自服务端的各类推送消息
 * @param eventSource
 */
export default function useAppMessageBus(eventSource: EventSource) {
  const { unreadCount, setUnreadCount } = useNotificationStore(useSelector(['unreadCount', 'setUnreadCount']));

  const currentUserInfo = useUserStore(state => ({
    roleCode: state.roleCode,
  }));

  useEffect(() => {
    // 被授予角色权限
    const handleAuthRolePass = async (e) => {
      try {
        notification.close(APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS);
        if (!isValidArray(currentUserInfo.roleCode)) {
          notification.warning({
            key: APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS,
            message: 'Role permissions have been updated',
            description: (
              <Space direction="vertical">
                <Row>Your role permissions have been updated and the page is about to refresh automatically for permissions to take effect...</Row>
              </Space>
            ),
            duration: null
          });
          await sleep(1000);
          window.location.reload();
        } else {
          notification.warning({
            key: APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS,
            message: 'Role permissions have been updated',
            description: (
              <Space direction="vertical">
                <Row>Your role permissions have been updated. The permissions take effect after you manually refresh the page</Row>
              </Space>
            ),
            duration: null
          });
        }
      } catch (e) {
        console.error(`[useAppMessageBus => authorization.pass]`, e);
      }
    };

    const handleContractAudit = async (e) => {
      const unreadCount = await getUnreadCount();
      setUnreadCount(unreadCount);
    };


    if (eventSource && eventSource.readyState !== eventSource.CLOSED) {
      // 被授予角色权限
      eventSource.addEventListener(APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS, handleAuthRolePass);
      eventSource.addEventListener(APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT, handleContractAudit);
      return () => {
        eventSource.removeEventListener(APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS, handleAuthRolePass);
        eventSource.removeEventListener(APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT, handleContractAudit);
      };
    }

  }, [eventSource, currentUserInfo.roleCode]);
}
