import React, { useEffect, useState, useContext } from 'react';
import authObjTree, { hasAuth } from '@/modules/authObjTree';
import { AppContext } from '@/store';

export default function () {
  let {
    auth: { authObjTree, roleAuth }
  } = useContext(AppContext);
  authObjTree = authObjTree || {};
  const { action } = authObjTree;
  const [updateAPP, setUpdateAPP] = useState(hasAuth(action, 'updateAPP'));
  const [createAPP] = useState(hasAuth(action, 'createAPP'));
  const [isNotAuth] = useState(!roleAuth || roleAuth.length === 0);

  return {
    updateAPP,
    createAPP,
    isNotAuth
  };
}
