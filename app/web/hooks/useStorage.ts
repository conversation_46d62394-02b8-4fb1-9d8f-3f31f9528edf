function useStorage(storageType) {
  const storage = storageType === 'local' ? window.localStorage : window.sessionStorage;

  const getStoredValue = (key: string, initialValue: any) => {
    const storedValue = storage.getItem(key);
    if (storedValue) {
      try {
        return JSON.parse(storedValue);
      } catch (error) {
        console.error('Failed to parse stored value:', error);
        return initialValue;
      }
    }
    return initialValue;
  };

  const setStoredValue = (key: string, value: any) => {
    try {
      storage.setItem(key, value);
    } catch (error) {
      console.error('Failed to set stored value:', error);
    }
  };

  const removeStoredValue = (key: string) => {
    storage.removeItem(key);
  };

  return { getStoredValue, setStoredValue, removeStoredValue };
}

function useLocalStorage() {
  const { getStoredValue, setStoredValue, removeStoredValue } = useStorage('local');

  return { getStoredValue, setStoredValue, removeStoredValue };
}

function useSessionStorage() {
  const { getStoredValue, setStoredValue, removeStoredValue } = useStorage('session');

  return { getStoredValue, setStoredValue, removeStoredValue };
}

export { useLocalStorage, useSessionStorage }
