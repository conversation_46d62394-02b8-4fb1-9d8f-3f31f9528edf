import React, { useEffect, useState, useContext } from 'react';
import { useModal } from '@/hooks';
import request from '@/modules/request';
import { Form, Modal } from 'antd';
import { AuthTree } from '@/component';
import { AppContext } from '@/store';

interface ModalProps {
  title: string;
  name: string;
  onSuccess?: (data: any, oldData: any) => void;
}

function EditModal({ title, name, onSuccess }: ModalProps) {
  const { localeData } = useContext(AppContext);
  const { visible, setVisible, type, emit, data, confirmLoading, setConfirmLoading } = useModal();
  const [ruleForm, setRuleForm] = useState<any>({});
  const [form] = Form.useForm();
  const [code, setCode] = useState('');
  const [authList, setAuthList] = useState([]);

  const [cacheList, setCacheList] = useState<any>([]);


  // 提供外部访问
  EditModal.prototype.open = emit;

  useEffect(() => {
    if (data) {
      setRuleForm(data);
      setCode(data.code);
    }
  }, [data]);

  useEffect(() => {
    if (type !== 'edit') {
      setCode('');
    }
  }, [type]);

  useEffect(() => {
    if (ruleForm && visible) {
      form.setFieldsValue(ruleForm);
    }
  }, [ruleForm]);

  async function handleOk() {
    const validData = await form.validateFields().catch(console.error);
    let result: any = null;
    if (validData) {
      setConfirmLoading(true);
      const url = type === 'add' ? `/${name}?code=${ruleForm.code}` : `/${name}/${data.id}`;
      const method = type === 'add' ? 'post' : 'put';
      let postData: any = [];
      if (name === 'roleAuth') {
        postData = authList.map(item => ({ role_code: ruleForm.code, auth_code: item }));
      }
      if (name === 'userRole') {
        postData = authList.map(item => ({ user_code: ruleForm.code, role_code: item }));
      }
      result = await request[method](url, postData, { 'X-showMessage': true }).catch(err => {
        console.error(err);
        setConfirmLoading(false);
      });
      if (result) {
        setVisible(false);
        if (onSuccess) {
          onSuccess(
            type === 'add'
              ? result.data?.map(item => {
                const oms_role = name === 'userRole' ? cacheList.find(i => i.code === item.role_code) : undefined;
                return {
                  ...item,
                  oms_role
                };
              })
              : { id: data.id, ...validData },
            ruleForm.userRoles
          );
        }
      }
      setConfirmLoading(false);
    }
  }

  /**
   * 获取弹层标题
   */
  function getModalTitle() {
    const typeName = title;
    let actionName = localeData.Edit;
    switch (type) {
      case 'add':
        actionName = localeData.Create;
        break;
      case 'edit':
        actionName = localeData.Edit;
        break;
      case 'copy':
        actionName = localeData.Copy;
        break;
      default:
        break;
    }
    return `${actionName}${typeName}${code ? ` - ${code}` : ''}`;
  }

  function handleValuesChange(values) {
    setRuleForm(Object.assign({}, ruleForm, values));
  }

  function getCheckedData(ruleForm, name) {
    const key = `${name}s`;
    return ruleForm[key] && ruleForm[key].map(item => item['roleAuth' === name ? 'auth_code' : 'role_code']);
  }

  return (
    <Modal
      key={code}
      width={900}
      getContainer={false}
      title={getModalTitle()}
      open={visible}
      onOk={handleOk}
      confirmLoading={confirmLoading}
      wrapClassName="edit-modal-wrap"
      onCancel={() => {
        setVisible(false);
      }}
    >
      <AuthTree name={name} onfetchList={setCacheList} checkedData={getCheckedData(ruleForm, name)} onChange={setAuthList} />
    </Modal>
  );
}

export default EditModal;
