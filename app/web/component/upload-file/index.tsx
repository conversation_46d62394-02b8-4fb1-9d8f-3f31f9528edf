import React, { useContext, useState } from 'react';
import { Button, Space, Tooltip, Upload, message } from 'antd';
import { PlusOutlined, UploadOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
// 大文件上传附加代码 开始
import request from '@/modules/request';
// import OSS from 'ali-oss';
import { Bucket, OSS_PREFIX_URL, OSS_ROOT_PATH } from '@@/lib/constant';
import { fileDownload, urlDownload } from '@/hooks/use-download';
import './index.scss'

// 大文件上传附加代码 结束

interface uploadImageProp {
  id: string
  label: string;
  itemData: any;
  setItemData: Function;
  form: any;
  accept?: string;
  multiple?: boolean;
  maxCount?: number;
  isLargeMode?: boolean; // 默认是大文件上传（SDK 上传）
  partSize?: number; // 分片大小
  disabled?: boolean
  loading?: boolean
  children?: React.ReactElement
  showUploadList?: boolean
  tipsRender?: () => React.ReactNode
  onUploadPercent: (percen: number, isNotUpdate?: boolean) => void
  onChange?: (fileList: any[]) => void
}

// createModal, updateModal 类型： Component 组件
// 自定义 图表上传 (图标 - 单个上传、素材 - 多个上传)
const UploadInput: any = (opt: uploadImageProp) => {
  const { csrf, localeData } = useContext(AppContext);
  const uploadButton = <PlusOutlined />;
  let showAddBtn = true;

  const { label, form, maxCount = 5, itemData, setItemData, multiple = false } = opt;
  const stashFileList: Record<string, any>[] = itemData[label] || [];
  const [fileList, setFileList] = useState<Record<string, any>[]>(stashFileList);

  // 大文件上传附加代码 开始
  // 是否大文件上传模式（ali-oss的SDK），原来是走 node端上传，两者兼容
  const isLargeMode = opt.isLargeMode === false ? false : true;
  const { UploadLargeFileTxt } = localeData;
  const [abortCheckpoint, setAbortCheckpoint] = useState<any>(null);

  async function uploadLargeFile({ file, onSuccess, onError, onProgress }) {
    let tempData: any = {};
    const result = await request.get(`/get-upload-key`, {});
    if (result.isSuccess) {
      tempData = result.data;
    } else {
      message.error(UploadLargeFileTxt.GetTmpTokenFail);
      return;
    }
    if (!tempData.SecurityToken) {
      message.error(UploadLargeFileTxt.GetTmpTokenFail);
      return;
    }
    const OSS = await import('ali-oss');
    const client = new OSS({
      region: 'oss-ap-southeast-1',
      accessKeyId: tempData.AccessKeyId,
      accessKeySecret: tempData.AccessKeySecret,
      stsToken: tempData.SecurityToken,
      bucket: Bucket
    });
    const progress = async (p: any, _checkpoint: any) => {
      // Object的上传进度。
      setAbortCheckpoint(_checkpoint);
      const percent = Number((p * 100).toFixed(2));
      // console.log(percent, _checkpoint);
      onProgress({ percent });
      if (p === 1) {
        setAbortCheckpoint(null);
      }
    };
    // 默认存放路径如下：有些业务存放路径依赖上游服务端接口获取
    let name = '';
    const filenameArr = file.name.split('.');
    const suffix = filenameArr[filenameArr.length - 1];
    filenameArr.pop();
    const nameStr = filenameArr.join('.');
    let newfilename = nameStr.replace(/[^a-z|^A-Z|^0-9|\-|_]/g, '');
    if (newfilename) {
      newfilename = newfilename + '-';
    }
    name = `${OSS_ROOT_PATH}${newfilename}${Date.now()}.${suffix}`;
    try {
      await client.multipartUpload(name, file, {
        ...(abortCheckpoint && {
          checkpoint: abortCheckpoint
        }),
        progress,
        // 设置分片大小。默认值为 5 MB，最小值为100 KB。
        partSize: opt?.partSize || 1024 * 1024 * 5,
        meta: {
          year: new Date().getFullYear(),
          people: 'cms'
        }
      });
      const fileUrl = `${OSS_PREFIX_URL}${name}`;
      // 构造原来 node上传的返回格式
      onSuccess({ data: { path: fileUrl }, isSuccess: true });
    } catch (error) {
      message.error(UploadLargeFileTxt.UploadFileFail);
      onError(error);
    }
  }
  // 大文件上传附加代码 结束

  let uploadImgPros: any = {
    fileList,
    onChange: async ({ fileList: newFileList, file }) => {

      if (file?.percent === 100) {
        setTimeout(() => {
          opt.onUploadPercent && opt.onUploadPercent(file?.percent);
        }, 1500)
      } else {
        opt.onUploadPercent && opt.onUploadPercent(file?.percent);
      }
      const formatFileList = newFileList.filter(it => {
        if (it.url) {
          return true;
        };
        if (it.status === 'done' && !it.url) {
          const isSuccess = it.response.isSuccess;
          if (isSuccess) {
            it.url = it.response.data.path;
          } else {
            it.status = 'error';
          }
        }
        return it.status;
      });
      setFileList(formatFileList);
      const newItemData = { ...itemData };
      const doneList = newFileList.filter(it => it.status === 'done');


      newItemData[label] = doneList.map(it => ({
        uid: it.uid,
        name: it.name,
        status: it.status,
        size: it.size,
        type: it.type,
        url: it.url
      }));
      setItemData(newItemData);
      opt.onChange && opt.onChange(newItemData[label])
      form && form.setFieldsValue({ [label]: newItemData[label] });
    },
    onRemove: file => {
      // 多张图片上传，不应该删除所有
      const { uid } = file;
      const newFileList: any = fileList.filter((item: any) => {
        const oldUid = item.uid;
        return oldUid !== uid;
      });
      setFileList(newFileList);
      if (multiple === true) {
        const newFileItemList = itemData[label].filter((item: any) => {
          const oldUid = item.uid;
          return oldUid !== uid;
        });
        setItemData({
          ...itemData,
          [label]: newFileItemList
        });
        opt.onChange && opt.onChange(newFileItemList)
        form && form.setFieldsValue({ [label]: newFileItemList });
      } else {
        setItemData({
          ...itemData,
          [label]: []
        });
        opt.onChange && opt.onChange([])
        form && form.setFieldsValue({ [label]: [] });
      }
    }
  };

  uploadImgPros.beforeUpload = file => {
    if (multiple === true && maxCount) {
      let fileCount = 1;
      if (fileList && fileList.length > 0) {
        fileCount = fileList.length + 1;
      }
      if (fileCount > maxCount) {
        message.warn(`The number of uploaded images cannot exceed ${maxCount}`);
        setTimeout(() => {
          opt.onUploadPercent && opt.onUploadPercent(100, true);
        }, 500)
        return false;
      }
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('Image must smaller than 10MB!');
    }
    const valid = isLt10M;
    if (!valid) {
      setTimeout(() => {
        opt.onUploadPercent && opt.onUploadPercent(100);
      }, 1500)
    }
    return valid

  };

  if (multiple === false && fileList?.length > 0) {
    showAddBtn = false;
  }
  let fileParams: any = {
    ...uploadImgPros,
    multiple,
    // listType: 'picture-card',
    action: `/api/upload`,
    headers: {
      'X-XSRF-TOKEN': csrf
    },
    showUploadList: {
      showDownloadIcon: true,
      downloadIcon: <DownloadOutlined style={{ color: '#40a9ff' }} />,
      showRemoveIcon: true,
      removeIcon: <DeleteOutlined style={{ color: '#ff7875' }} />
    }
  };
  if (opt.disabled || opt.loading) {
    // fileParams.disabled = true;
  }
  if (opt.maxCount) {
    fileParams.maxCount = opt.maxCount;
  }
  // 大文件上传附加代码 开始
  if (isLargeMode) {
    delete fileParams.action;
    delete fileParams.headers;
    // 通过覆盖默认的上传行为，可以自定义自己的上传实现
    fileParams.customRequest = uploadLargeFile;
  }
  if (opt.accept) {
    fileParams.accept = opt.accept;
  }
  if (opt.showUploadList !== void 0) {
    fileParams.showUploadList = opt.showUploadList;
  }

  return (
    <div className={`custom-upload-container ${opt.disabled || opt.loading ? 'disabled' : ''}`} id={opt.id}>
      <Upload {...fileParams} onDownload={(file) => {
        const originFileObj = file.originFileObj;
        if (!originFileObj) {
          urlDownload(file.url!, file.name || file.fileName!);
          return;
        }
        fileDownload(file.originFileObj)
      }} >
        {!opt.disabled && (
          opt.children ? opt.children : (
            <div>
              <div><Button icon={<UploadOutlined />}>Upload</Button></div>
              <div>{opt.tipsRender && opt.tipsRender()}</div>
            </div>
          )
        )}

      </Upload>
      {!opt.disabled && (
        opt.children ? null :
          <div>
            <div style={{ color: '#a4aab2' }}>Required Format: rar, zip, doc, docx, pdf, jpg, jpeg, png, {opt.accept?.includes?.('xlsx') ? 'xlsx, xls' : ''}</div>
          </div>
      )}

    </div>
  );
};

export default UploadInput;
