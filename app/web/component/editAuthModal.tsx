import React, { useEffect, useState, useContext } from 'react';
import { useModal } from '@/hooks';
import request from '@/modules/request';
import { Form, Input, Modal, Row, Col, Radio } from 'antd';
import { UploadOutlined, LoadingOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import { __ } from '@/modules/format';

interface BaseDataItem {
  text: string;
  value: number | string;
}

interface ModalProps {
  title: string;
  name: string;
  onSuccess?: (data: any) => void;
}

const titleProfix = {
  parent: '父级',
  child: '子级'
};

const layout = {
  labelCol: { span: 5 }
};

function EditModal({ title, name, onSuccess }: ModalProps) {
  const { visible, setVisible, type, emit, data, confirmLoading, setConfirmLoading } = useModal();
  const [ruleForm, setRuleForm] = useState<any>({});
  const [form] = Form.useForm();
  const [code, setCode] = useState('');
  const {
    BaseData: { authType },
    auth,
    localeData
  } = useContext(AppContext);
  const titleProfix = {
    parent: localeData.Parent,
    child: localeData.Child
  };

  const { item, type: t } = data || {};
  // title
  title = (t ? titleProfix[t] : '') + title;

  // 提供外部访问
  EditModal.prototype.open = emit;

  useEffect(() => {
    const { item } = data || {};
    const formData = JSON.parse(JSON.stringify(item || {}));

    if (['child'].includes(t)) {
      formData.parent = formData.code;
      delete formData.name;
      delete formData.remark;
    }

    if (item) {
      setRuleForm(formData);
      setCode(formData.code);
      form.setFieldsValue(formData);
      if (type === 'add' && t === 'child') {
        form.setFieldValue('name', '');
        form.setFieldValue('remark', '');
      }
    } else {
      setCode('');
      setRuleForm({});
    }
  }, [data]);

  useEffect(() => {
    if (Object.keys(ruleForm).length === 0) {
      form.resetFields();
    }
  }, [ruleForm]);

  async function handleOk() {
    const validData = await form.validateFields().catch(console.error);
    let result: any = null;
    if (validData) {
      setConfirmLoading(true);
      const url = type === 'add' ? `/${name}` : `/${name}/${data.item.id}`;
      const method = type === 'add' ? 'post' : 'put';
      validData.parent = validData.parent || '0';

      if (t === 'parent') {
        validData.child = code;
      }

      // 新建时，设置position
      if (type === 'add') {
        if (validData.parent === '0') {
          validData.position = auth.authObjTree[validData.type] ? auth.authObjTree[validData.type][auth.authObjTree[validData.type].length - 1].position + 1 : 0;
        } else {
          validData.position = data.item.children.length ? data.item.children[data.item.children.length - 1].position + 1 : 0;
        }
      }

      // @ts-ignore
      result = await request[method](url, validData, { 'X-showMessage': true }).catch(err => {
        console.error(err);
        setConfirmLoading(false);
      });
      if (result) {
        setVisible(false);
        if (onSuccess) {
          onSuccess(type === 'add' ? result.data : { id: data.id, ...validData });
        }
      }
      setConfirmLoading(false);
    }
  }

  /**
   * 获取弹层标题
   */
  function getModalTitle() {
    const typeName = title;
    let actionName = localeData.Edit;
    switch (type) {
      case 'add':
        actionName = localeData.Create;
        break;
      case 'edit':
        actionName = localeData.Edit;
        break;
      case 'copy':
        actionName = localeData.Copy;
        break;
      default:
        break;
    }
    return `${actionName}${typeName}${code ? ` - ${code}` : ''}`;
  }

  function handleValuesChange(values) {
    setRuleForm(Object.assign({}, ruleForm, values));
  }

  function typeChange() { }

  return (
    <Modal
      width={900}
      getContainer={false}
      title={getModalTitle()}
      visible={visible}
      onOk={handleOk}
      confirmLoading={confirmLoading}
      wrapClassName="edit-modal-wrap"
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Form {...layout} form={form} className="login-form" initialValues={ruleForm} onValuesChange={handleValuesChange}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="parent" label={'父级'} >
              <Input placeholder={__('Please', 'Select', 'Parent')} disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="type" label={'类型'} rules={[{ required: true, message: __('Please', 'Select') }]} >
              <Radio.Group onChange={typeChange} disabled={type === 'edit' || ['parent', 'child'].includes(t)}>
                {authType.map(item => (
                  <Radio key={item.value} value={item.value}>
                    {item.text}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="name" label={'权限标识'} rules={[{ required: true, message: __('Please', 'Input') }]} >
              <Input placeholder={__('Please', 'Input')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="remark" label={'权限名称'}>
              <Input placeholder={__('Please', 'Input')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="remark_en" label={'备注(英)'}>
              <Input placeholder={__('Please', 'Input')} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

export default EditModal;
