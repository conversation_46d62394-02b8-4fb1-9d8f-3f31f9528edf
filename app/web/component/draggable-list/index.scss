.draggable-list-wrapper{
  display: flex;
  .draggable-list{
    display: flex;
    flex: 1;
    &-header{

    }

    &-main{
      display: flex;
      flex: 1;
      &-list-item{
        &-wrapper{
          //transition: background-color ease-in .3s;
        }

        &-body{
          //padding: 8px 12px;
          //transition: background-color 0.3s;
          //&:hover{
          //  background-color: rgba(0,0,0,0.04);
          //}
        }
      }
    }

    &-main-empty{
      justify-content: center;
      align-items: center;
    }

    &-footer{

    }

    &.draggable-list-vertical{
      flex-direction: column;

      .draggable-list-main{
        flex-direction: column;
      }
    }

    &.draggable-list-horizontal{
      flex-direction: row;
      .draggable-list-main{
        flex-direction: row;
      }
    }
  }

}