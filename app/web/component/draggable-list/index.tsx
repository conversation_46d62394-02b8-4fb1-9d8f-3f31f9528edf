import './index.scss';
import {
  DragDropContext,
  Draggable,
  DraggableProvided, DraggableRubric, DraggableStateSnapshot,
  Droppable, DroppableStateSnapshot,
  OnDragEndResponder,
  Responders
} from 'react-beautiful-dnd';
import React, {
  CSSProperties,
  ReactNode,
  PropsWithChildren,
  useMemo,
  HTMLAttributes,
  forwardRef,
  useImperativeHandle,
  useRef, ForwardRefExoticComponent, PropsWithoutRef, RefAttributes, useState,
} from 'react';
import { Empty } from 'antd';
import { useIsomorphicLayoutEffect } from 'ahooks';

export interface DraggableListProps<T extends { id: string, isDragDisabled?: boolean }> {
  dataSource: T[];
  renderItem: (item, draggableStateSnapshot: DraggableStateSnapshot, index: number) => ReactNode;
  mode?: 'vertical' | 'horizontal';
  header?: ReactNode;
  footer?: ReactNode;
  headerProps?: HTMLAttributes<HTMLElement>;
  footerProps?: HTMLAttributes<HTMLElement>;
  wrapperClassName?: string;
  wrapperStyle?: CSSProperties;
  dragEvents?: Partial<Responders>;
  onDragChange?: (newDataSource: T[], indexInfo: { from: number, to: number }) => void;
  dragStyleResponder?: (state: DraggableStateSnapshot) => CSSProperties | undefined;
  dropStyleResponder?: (state: DroppableStateSnapshot) => CSSProperties | undefined;
}

type ListItemProps = PropsWithChildren<{
  draggableProvided: DraggableProvided;
  draggableStateSnapshot: DraggableStateSnapshot;
  draggableRubric: DraggableRubric,
  dragStyleResponder
}>;

const isFunction = (fn: unknown) => typeof fn === 'function';

function ListItem(props: ListItemProps) {
  const {
    draggableProvided,
    draggableRubric,
    draggableStateSnapshot,
    children,
    dragStyleResponder,
  } = props;

  return (
    <div
      className="draggable-list-main-list-item-wrapper"
      style={isFunction(dragStyleResponder) ? dragStyleResponder(draggableStateSnapshot) : {}}
      ref={draggableProvided.innerRef}
      {...draggableProvided.draggableProps}
      {...draggableProvided.dragHandleProps}
    >
      <div className="draggable-list-main-list-item-body">
        {children}
      </div>
    </div>
  );
}

export interface DraggableListRef {
  scrollToBottom: () => void;
}

const DraggableList: ForwardRefExoticComponent<
  PropsWithoutRef<DraggableListProps<any>> & RefAttributes<any>
> = forwardRef<DraggableListRef, DraggableListProps<any>>(<T extends { id: string, isDragDisabled?: boolean } = { id: string, isDragDisabled?: boolean }>(props: DraggableListProps<T>, ref) => {

  const {
    dataSource,
    renderItem,
    mode = 'vertical',
    header,
    footer,
    wrapperClassName = '',
    wrapperStyle = {},
    headerProps = {},
    footerProps = {},
    dragEvents,
    onDragChange,
    dragStyleResponder,
    dropStyleResponder
  } = props;

  const [draggableList, setDraggableList] = useState<any[]>([]);
  const [dragDisabledList, setDragDisabledList] = useState<any[]>([]);
  const listBottomPlaceholderRef = useRef<HTMLDivElement>(null);

  const handleDragEnd: OnDragEndResponder = (result, provided) => {
    const { source, destination, draggableId } = result;
    if (!destination) return;
    const newDraggableList = Array.from(draggableList);
    const [removed] = newDraggableList.splice(result.source.index, 1);
    if (removed) {
      newDraggableList.splice(destination.index, 0, removed);
    }
    isFunction(onDragChange) && onDragChange?.([
      ...dragDisabledList,
      ...newDraggableList
    ], {
      from: source.index,
      to: destination.index
    });
    if (dragEvents) {
      // @ts-ignore
      isFunction(dragEvents?.onDragEnd) && dragEvents?.onDragEnd(result, provided);
    }
  };

  useImperativeHandle(ref, () => {
    return {
      scrollToBottom() {
        if (listBottomPlaceholderRef.current) {
          listBottomPlaceholderRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
      }
    };
  });

  useIsomorphicLayoutEffect(() => {
    const newDraggableList: any[] = [];
    const newDragDisabledList: any[] = [];
    dataSource.forEach(item => {
      if (item.isDragDisabled) newDragDisabledList.push(item);
      else newDraggableList.push(item);
    });
    setDraggableList(newDraggableList);
    setDragDisabledList(newDragDisabledList);
  }, [dataSource]);

  return (
    <section className={`draggable-list-wrapper ${wrapperClassName}`} style={{ ...wrapperStyle }}>
      <DragDropContext
        {...dragEvents}
        onDragEnd={handleDragEnd}
      >
        <section className={`draggable-list ${mode === 'vertical' ? 'draggable-list-vertical' : 'draggable-list-horizontal'}`}>
          <header {...headerProps} className={`draggable-list-header ${headerProps.className || ''}`} > {header}</header>
          {
            dataSource.length > 0 ? (
              <>
                {
                  dragDisabledList.map((item, index) => {
                    return renderItem(item, null, index);
                  })
                }
                <Droppable droppableId="droppable-list">
                  {
                    (provided, snapshot) => (
                      <main
                        className="draggable-list-main"
                        style={isFunction(dropStyleResponder) ? dropStyleResponder?.(snapshot) || {} : {}}
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {
                          draggableList.map((item, index) => {
                            return (
                              <Draggable key={item.id} draggableId={item.id} index={index} isDragDisabled={item.isDragDisabled}>
                                {
                                  (provided, snapshot, rubric) => (
                                    <ListItem draggableProvided={provided} draggableStateSnapshot={snapshot} draggableRubric={rubric} dragStyleResponder={dragStyleResponder}>
                                      {renderItem(item, snapshot, index)}
                                    </ListItem>
                                  )
                                }
                              </Draggable>
                            );
                          })
                        }
                        {provided.placeholder}
                        <div ref={listBottomPlaceholderRef}></div>
                      </main>
                    )
                  }
                </Droppable>
              </>
            ) : (
              <main className="draggable-list-main draggable-list-main-empty">
                <Empty />
              </main>
            )
          }
          <footer {...footerProps} className={`draggable-list-footer ${footerProps.className || ''}`}>{footer}</footer>
        </section>
      </DragDropContext >
    </section >
  );
});

export default DraggableList;
