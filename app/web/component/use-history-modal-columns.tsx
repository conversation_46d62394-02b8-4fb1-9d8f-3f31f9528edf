import React from 'react';
import { FlatBigTextCell, useFlatTableColumns } from '@flat-design/components-pc';
import { useRemoteOptions } from '@flat-design/hooks';
import moment from 'moment-timezone';

export default function (opt: any) {
  const { visible, id, pathname } = opt;
  const remoteOptionsQuery = id ? { id } : undefined;
  const historyOperatorListOptions = useRemoteOptions({
    apiPath: `/log/operator-list?pathname=${pathname}`,
    labelFields: [],
    valueField: '',
    disabled: !visible,
    params: remoteOptionsQuery,
  });
  const historyActionOptions = useRemoteOptions({
    apiPath: `/log/action-list?pathname=${pathname}`,
    labelFields: [],
    valueField: '',
    disabled: !visible,
    params: remoteOptionsQuery,
  });

  const historyModalColumns = useFlatTableColumns(
    [
      {
        key: 'id',
      },
      {
        key: 'item_id',
        filter: {
          type: 'Input',
        },
      },
      {
        key: 'ctime',
        render(val) {
          return moment(val).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        key: 'operator',
        filter: {
          type: 'Select',
          options: historyOperatorListOptions,
        },
      },
      {
        key: 'action',
        filter: {
          type: 'Select',
          options: historyActionOptions,
        },
      },
      {
        key: 'record',
        filter: {
          type: 'Input',
        },
        render(val) {
          return <FlatBigTextCell text={val} usePoponver tooltipWidth={800} />;
        },
      },
    ],
    {
      Fields: {
        id: 'ID',
        item_id: '数据ID',
        ctime: '创建时间',
        operator: '操作人',
        action: '操作类型',
        record: '当前内容',
      },
    },
  );

  return historyModalColumns;
}