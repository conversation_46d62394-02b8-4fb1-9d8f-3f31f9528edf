import React, { ReactComponentElement, useState } from 'react'
import { Button } from 'antd';

import { SettingOutlined } from '@ant-design/icons'
import { ButtonType } from 'antd/lib/button';
import request from '@/modules/request';


interface IProps {
  api: string;
  method?: 'get' | 'post'
  params?: Record<string, any>
  icon?: ReactComponentElement<any>
  type?: ButtonType
  onOk: (res: Record<string, any>) => void
  children?: React.ReactNode
}

export default function LoadingButton({ api, method = 'get', params = {}, icon = <SettingOutlined />, type = 'primary', onOk = (res = {}) => { }, children, onClicking = (e: React.MouseEvent<HTMLElement, MouseEvent>) => { } }) {
  const [loading, setLoading] = useState(false);
  return (
    <Button
      type={type as ButtonType}
      icon={icon}
      loading={loading}
      onClick={(e) => {
        onClicking(e)
        setLoading(true);
        request[method](api, params).then(res => {
          onOk(res.data)
        }).finally(() => {
          setLoading(false);
        })
      }}
    >
      {children}
    </Button>
  )
}
