import React, { useState, useEffect, useContext, useImperativeHandle, useRef } from 'react';
import { Divider, Table, Row, Col, Space, Button, Modal, Popconfirm, Tooltip, message, Dropdown, Checkbox, Popover } from 'antd';
// @ts-ignore
import request from '@/modules/request';
// @ts-ignore
import { useLocalStorage, useQueryList } from '@/hooks';
// @ts-ignore
import { __ } from '@/modules/format';
// @ts-ignore
import { AppContext } from '@/store';
import './index.scss';
import CustomColumn, { ChangeType } from './customColumn'

import { ExportOutlined, PlusOutlined, DeleteOutlined, FileSearchOutlined, SettingOutlined, HolderOutlined, MoreOutlined, EllipsisOutlined } from '@ant-design/icons';
import { UseTableReturn } from './types';
import { IFlatHistoryModal } from '@flat-design/components-pc/es/flat-history-modal';
import { isFunction, isObject } from '@@/lib/tool';
import OperateAccess from '../auth/operate-access';
import { useLocation } from 'react-router-dom';
import { defaultParamsMapToDataHandler } from './tool'

const STORAGE_VERSION = '1.0.0';

export enum BtnGroupType {
  Create = 'create', // 新建
  OperationRecord = 'operationRecord', // 操作记录
  BatchDelete = 'batchDelete', // 批量删除
  Export = 'export', // 导出
  CustomColumn = 'customColumn', // 自定义列
}

type ActionType = 'copy' | 'edit' | 'detail' | 'delete'

enum TableActionType {
  Copy = 'copy', // 复制
  Edit = 'edit', // 编辑
  Detail = 'detail', // 详情
  Delete = 'delete', // 删除
}
interface Props {
  fetchUrl: string;
  columns?: any[] | (({ itemData, setItemData }) => any[]);
  lang?: string;
  actions?: (React.ReactElement | string | ((record: Record<string, any>) => React.ReactElement | null))[]; // 可选的操作类型，copy | edit | detail | delete | React.ReactElement
  btnGroup?: any[]; // 表格右上角操作按钮，create | operationRecord | React.ReactElement
  title?: string | React.ReactElement; // 标题
  pageSize?: number;
  defaultParams?: any; // 默认固定传参
  filterConfig?: any; // 筛选项
  historyModalConfig?: IFlatHistoryModal
  | ((args: { itemData: any; historyModalVisible: boolean }) => IFlatHistoryModal); // 操作记录弹窗配置
  beforeEdit?: (record: any, index: number) => Promise<any>; // 编辑前触发的事件
  beforeDetail?: (record: any) => any; // 详情前触发的事件
  beforeCopy?: (record: any, index: number) => Promise<any>;
  onExport?: (params: Record<string, any>, setExportLoading: (loading: boolean) => void) => void; // 导出
  beforeCreate?: () => Record<string, any>;
  beforeHandleCreate?: (options: any) => Promise<{ values?: Record<string, any>, isSuc: boolean }>;
  beforeHandleUpdate?: (options: any) => Promise<{ values: Record<string, any>, isSuc: boolean }>;
  beforeFilter?: (options: any) => any;
  allowRowSelect?: boolean
  renderCheckCell?: (checked: any, record: any, index: any, originNode: any) => any;
  onBatchDelete?: (selectedRows: any[]) => Promise<boolean>;
  afterCreate?: (result: Recordable, submitData: Recordable) => void;
  afterUpdate?: () => void;
  isDisabledAction?: (type: ActionType, record: any) => boolean;
  graspBeforeCreate?: (beforeCreate: (temp: Recordable) => void) => void
  filterBeforeReset?: () => Recordable
  renderEditText?: (text: any, record: any, index: any) => any
  dataFetchAfter?: (res: Recordable) => void
  actionWidth?: number
  rowClassName?: (record: any, index: number) => string
  extra?: React.ReactNode
  customPagination?: boolean
}

function handlerTableColumnIndexObj(columns: any[]) {
  return columns.reduce((obj, item) => {
    obj[item.dataIndex] = item.index;
    return obj
  }, {})
};
function handlerTableColumnCheckedObj(columns: any[]) {
  return columns.reduce((obj, item) => {
    obj[item.dataIndex] = item.checked ?? true;
    return obj
  }, {})
};



export const useFlatTable = (props: Props): UseTableReturn => {
  const {
    title = '',
    fetchUrl,
    columns: _defaultCoumns = [],
    btnGroup = [],
    actions = [],
    defaultParams = {},
    filterConfig = {},
    historyModalConfig,
    beforeEdit,
    beforeCopy,
    beforeDetail,
    onExport,
    beforeCreate,
    beforeHandleCreate,
    beforeHandleUpdate,
    beforeFilter,
    allowRowSelect = false,
    renderCheckCell = null,
    onBatchDelete = () => { },
    afterCreate,
    afterUpdate,
    isDisabledAction,
    graspBeforeCreate,
    filterBeforeReset,

    dataFetchAfter,
    actionWidth = 150,
    customPagination
  } = props;
  // @ts-ignore

  const { localeData } = useContext(AppContext);
  const [sorterObj, setSorter] = useState<any>(null);
  const [retrieveModalVisible, setRetrieveModalVisible] = useState<boolean>(false);
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);
  const [historyModalVisible, setHistoryModalVisible] = useState<boolean>(false);
  const [createModalTitle, setCreateModalTitle] = useState<string>(localeData.Create);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const location = useLocation();
  const localStorage = useLocalStorage();
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    loading,
    pageIndex,
    setPageIndex,
    pageSize,
    setListData,
    listData,
    fetchData,
    setItemData,
    itemData,
    setPageSize,
    total,
    extraData,
  } = useQueryList({
    fetchUrl,
    pageSize: props.pageSize || 20,
  });


  const columns: any[] = isFunction(_defaultCoumns) ? _defaultCoumns({
    itemData,
    setItemData
  }) : _defaultCoumns;

  // 处理mapToData
  const transformDate = columns.find(it => it?.mapToData);
  if (Object.keys(defaultParams)?.length && transformDate) {
    defaultParamsMapToDataHandler(transformDate, defaultParams);
  };
  const [params, setParams] = useState<any>(defaultParams);


  let tableUseColumns = [...columns];

  let defaultTableColumnIndex = handlerTableColumnIndexObj(tableUseColumns.filter(it => !it.hidden));
  let defaultTableColumnIsChecked = handlerTableColumnCheckedObj(tableUseColumns.filter(it => !it.hidden));
  const [tableColumnIndex, setTableColumnIndex] = useState(localStorage.getStoredValue(`${location.pathname}_index_columns_${STORAGE_VERSION}`, defaultTableColumnIndex));
  const [tableColumnIsChecked, setTableColumnIsChecked] = useState(localStorage.getStoredValue(`${location.pathname}_checked_columns_${STORAGE_VERSION}`, defaultTableColumnIsChecked));

  tableUseColumns.forEach(it => {
    it.index = tableColumnIndex[it.dataIndex] ?? it.index ?? 0;
    it.checked = tableColumnIsChecked[it.dataIndex] ?? it.checked ?? true;
  });

  tableUseColumns = tableUseColumns.sort((a, b) => a.index - b.index);

  useEffect(() => {
    const filterParams = {
      ...defaultParams,
      ...params,
      pageSize,
      pageIndex,
      sorter: sorterObj,
    };
    const finParams = beforeFilter ? beforeFilter(filterParams) : filterParams;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      fetchData(finParams).then(res => {
        dataFetchAfter && dataFetchAfter(res);
      });
    }, 50);
  }, [params, pageSize, pageIndex, sorterObj]);

  const onTableChange = (pagination: any, filters: any, sorter: any) => {
    if (!customPagination) {
      setPageSize(pagination.pageSize);
      setPageIndex(pagination.current);
    }

    if (sorter.order) {
      const tmpSorter = [sorter.columnKey, sorter.order.replace('end', '')].join(',');
      setSorter(tmpSorter);
    } else {
      setSorter(null);
    }
  };

  const onPaginationChange = (newPage, newPageSize) => {
    if (pageSize !== newPageSize) {
      setPageIndex(1);
      setPageSize(newPageSize);
    } else {
      setPageIndex(newPage);
    }
  }

  const handleCreate = async (options: any) => {
    let paramsData = Object.assign({}, options);
    if (isFunction(beforeHandleCreate)) {
      const { isSuc, values } = await beforeHandleCreate(paramsData);
      if (!isSuc) { return };
      paramsData = values;
    }
    // @ts-ignore
    const result = (await request.post(fetchUrl, paramsData, { 'X-showMessage': true })) as any;
    if (result.isSuccess) {
      setCreateModalVisible(false);
      defaultFetchHangler();
      afterCreate && afterCreate(result, paramsData);
    };
    return result;
  };

  const handleUpdate = async (options: any, itemId?: number) => {
    let paramsData = Object.assign({}, options);
    if (isFunction(beforeHandleUpdate)) {
      const { isSuc, values } = await beforeHandleUpdate(paramsData);
      if (!isSuc) { return };
      paramsData = values;
    }
    // @ts-ignore
    const result = (await request.put(`${fetchUrl}/${itemId || itemData.id}`, paramsData, { 'X-showMessage': true })) as any;
    if (result.isSuccess) {
      setUpdateModalVisible(false);
      defaultFetchHangler();
      afterUpdate && afterUpdate();
    }
    return result;
  };

  const clearRowSelect = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: any) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: renderCheckCell,
    fixed: true,
  };

  const defaultFetchHangler = (otherParams = {}) => {
    const filterParams = {
      ...defaultParams,
      pageSize,
      pageIndex,
      sorter: sorterObj,
      ...params,
    }
    const finParams = beforeFilter ? beforeFilter(filterParams) : filterParams
    fetchData({
      ...finParams,
      ...otherParams // 优先级最高
    });
    clearRowSelect();
  };

  const onCreate = (tempObj = {}) => {
    let tmpItemData = {
      ...tempObj
    };

    if (typeof beforeCreate === 'function') {
      tmpItemData = {
        ...tempObj,
        ...(beforeCreate() || {})
      };
    }

    setItemData(tmpItemData);

    setCreateModalTitle(localeData.Create);
    setCreateModalVisible(true);
  };

  const onEdit = async (record: any, index) => {
    let tmpRecord = { ...record };

    if (typeof beforeEdit === 'function') {
      tmpRecord = await beforeEdit(tmpRecord, index);
    }

    setItemData(tmpRecord);

    setUpdateModalVisible(true);
  }

  const onCopy = async (record: any, index) => {
    setCreateModalTitle(localeData.Copy);
    let tmpRecord = { ...record };

    if (typeof beforeCopy === 'function') {
      tmpRecord = await beforeCopy(tmpRecord, index);
    }
    setItemData(tmpRecord);
    setCreateModalVisible(true);
  }

  const rowSelectConfig = allowRowSelect
    ? {
      selectedRows,
      selectedRowKeys,
      rowSelection,
      clearRowSelect,
    }
    : {};


  const btnGroupNodes: (React.ReactElement | string)[] = [];
  const dropDownBtnGroups: (React.ReactElement)[] = [];

  for (const item of btnGroup) {
    if (typeof item === 'string') {
      if (item === BtnGroupType.BatchDelete) {
        btnGroupNodes.push(
          <OperateAccess.Destroy>
            <Popconfirm
              disabled={!selectedRows.length}
              title="Are you sure you want to delete?"
              onConfirm={async () => {
                const res = await onBatchDelete(selectedRows);
                if (res) {
                  defaultFetchHangler()
                }
              }}
              okText="Confirm"
              cancelText="Cancel"
            >
              <Button
                className='remove-btn'
                danger
                disabled={!selectedRows.length}
                icon={<DeleteOutlined />}
                onClick={() => {
                  // setHistoryModalVisible(true);

                }}
              >
                {localeData.BatchDelete}
              </Button>
            </Popconfirm>
          </OperateAccess.Destroy>

        );
      }
      if (item === BtnGroupType.Export) {
        dropDownBtnGroups.push(
          <OperateAccess.Export>
            <Button
              icon={<ExportOutlined />}
              type='link'
              loading={exportLoading}
              onClick={() => {
                if (!listData?.length) {
                  message.warn('No data to export');
                  return
                };
                const finParams = beforeFilter ? beforeFilter(params) : params

                const query = {
                  ...defaultParams,
                  ...finParams,
                };
                const filterColumns = columns.filter(it => !it.hidden && it.checked !== false)
                const columnObjs = filterColumns.reduce((acc, cur, index) => {
                  if (cur.dataIndex && cur.key && cur.key !== '_operate') {
                    acc[cur.dataIndex] = {
                      dataIndex: cur.dataIndex,
                      title: cur.export?.title || cur.title,
                      style: {
                        width: cur.width
                      },
                    };
                    if (cur.index) {
                      acc[cur.dataIndex].sortedIndex = cur.index;
                    }
                    if (cur.export?.mapping) {
                      acc[cur.dataIndex].mapping = cur.export?.mapping;
                    }
                    if (cur.export?.rel_key) {
                      acc[cur.dataIndex].rel_key = cur.export?.rel_key;
                    }
                    if (cur.export?.type) {
                      acc[cur.dataIndex].type = cur.export?.type;
                    }
                  }
                  return acc;
                }, {});
                onExport && onExport({
                  query,
                  columnObjs
                }, setExportLoading)
              }}
            >
              {localeData.Export}
            </Button>
          </OperateAccess.Export>
        );
      }
      if (item === BtnGroupType.Create) {
        btnGroupNodes.push(
          <OperateAccess.Create>
            <Button
              className='create-btn'
              icon={<PlusOutlined />}
              onClick={() => {

                if (graspBeforeCreate) {
                  graspBeforeCreate(onCreate)
                  return;
                }
                onCreate();

              }}
            >
              {localeData.Create}
            </Button>
          </OperateAccess.Create>
        );
      }
      if (item === BtnGroupType.OperationRecord) {
        dropDownBtnGroups.push(
          <OperateAccess.Read>
            <Button
              type='link'
              icon={<FileSearchOutlined />}
              onClick={() => {
                setHistoryModalVisible(true);
              }}
            >
              {localeData.OperationRecord}
            </Button>
          </OperateAccess.Read>
        );
      }
      if (item === BtnGroupType.CustomColumn) {
        btnGroupNodes.push(
          <CustomColumn columns={tableUseColumns.filter(it => (!it.hidden && !it.fixed))} onChange={(newColumn, { from, to }, type) => {
            if (type === ChangeType.SORTED) {
              const indexObj = handlerTableColumnIndexObj(newColumn.map((it, idx) => ({
                ...it,
                index: idx
              })));
              localStorage.setStoredValue(`${location.pathname}_index_columns_${STORAGE_VERSION}`, JSON.stringify(indexObj))

              setTableColumnIndex(indexObj)
              return;
            }
            if (type === ChangeType.CHECKED) {
              const hiddenObj = handlerTableColumnCheckedObj(newColumn);
              localStorage.setStoredValue(`${location.pathname}_checked_columns_${STORAGE_VERSION}`, JSON.stringify(hiddenObj))

              setTableColumnIsChecked(hiddenObj)
              return
            }

          }} />
        )
      }

    } else if (isFunction(item)) {
      btnGroupNodes.push(item(selectedRows));
    } else {
      if (item) {
        btnGroupNodes.push(item);
      }
    }
  }

  if (dropDownBtnGroups.length > 0) {
    const dropdownBtnInfo = dropDownBtnGroups.map((ele, index) => ({
      key: index,
      label: ele
    }));
    const dropdownComp = (
      <Dropdown menu={{ items: dropdownBtnInfo }}>
        <Button style={{ width: 'auto' }} icon={<EllipsisOutlined />}></Button>
      </Dropdown>
    )
    if (btnGroup.includes(BtnGroupType.CustomColumn)) {
      btnGroupNodes.splice(btnGroupNodes.length - 1, 0, dropdownComp)
    } else {
      btnGroupNodes.push(dropdownComp)
    }
  }

  if (actions?.length > 0 && !tableUseColumns.find(it => it.key === '_operate')) {
    tableUseColumns?.push({
      title: localeData.Operate,
      key: '_operate',
      align: 'center',
      dataIndex: '_operate',
      width: actionWidth,
      fixed: 'right',
      textWrap: 'word-break',
      render: (text, record: any, index: number) => {
        const actionNodes: (React.ReactElement | string | null)[] = [];
        for (const item of actions) {
          if (typeof item === 'string') {
            if (item === TableActionType.Copy) {
              const deleteIsDisabled = isDisabledAction ? isDisabledAction(TableActionType.Copy, record) : false;

              actionNodes.push(
                <OperateAccess.Create>
                  <Button
                    key={localeData.Copy}
                    type='primary'
                    disabled={deleteIsDisabled}
                    onClick={() => {
                      onCopy(record, index)
                    }}
                  >
                    {localeData.Copy}
                  </Button>
                </OperateAccess.Create>

              );
            }

            if (item === TableActionType.Edit) {
              const deleteIsDisabled = isDisabledAction ? isDisabledAction(TableActionType.Edit, record) : false;
              const editText = props.renderEditText ? props.renderEditText(text, record, index) : localeData.Edit
              actionNodes.push(
                <OperateAccess.Update>
                  <Button
                    disabled={deleteIsDisabled}
                    key={localeData.Edit}
                    type='primary'
                    loading={record.loading}
                    onClick={async () => {
                      await onEdit(record, index)
                    }}
                  >
                    {editText}

                  </Button>
                </OperateAccess.Update>
              );
            }

            if (item === TableActionType.Detail) {
              const deleteIsDisabled = isDisabledAction ? isDisabledAction(TableActionType.Detail, record) : false;

              actionNodes.push(
                <Button
                  key={3}
                  disabled={deleteIsDisabled}
                  onClick={() => {
                    let tmpRecord = { ...record };

                    if (typeof beforeDetail === 'function') {
                      tmpRecord = beforeDetail(tmpRecord);
                    }

                    setItemData(tmpRecord);
                    setRetrieveModalVisible(true);
                  }}
                >
                  {localeData.Detail}
                </Button>,
              );
            }

            if (item === TableActionType.Delete) {
              const deleteIsDisabled = isDisabledAction ? isDisabledAction(TableActionType.Delete, record) : false;
              actionNodes.push(
                <OperateAccess.Destroy>
                  <Popconfirm
                    disabled={deleteIsDisabled}
                    title="Are you sure you want to delete?"
                    onConfirm={async () => {
                      const result = (await request.delete(`${fetchUrl}/${record.id}`, {
                        'X-showMessage': true,
                      })) as any;
                      if (result.isSuccess) {
                        defaultFetchHangler()
                      }
                    }}
                    okText="Confirm"
                    cancelText="Cancel"
                  >

                    <Button
                      disabled={deleteIsDisabled}
                      danger
                      key={4}
                    >
                      {localeData.Delete}
                    </Button>
                  </Popconfirm>
                </OperateAccess.Destroy>
              );
            }

          } else if (isFunction(item)) {
            actionNodes.push(item(record));
          } else {
            if (item) {
              actionNodes.push(item);
            }
          }
        }
        return (
          <Space>
            {actionNodes?.map((item, index) => {
              return <div className='action-btn' key={`action-${index}`}>{item}</div>;
            })}
          </Space>
        );
      },
    });
  }
  const fetchDataHandler = (params: Record<string, any> = {}) => {
    if (isObject(params)) {
      const filterParams = params;
      const finParams = beforeFilter ? beforeFilter(filterParams) : filterParams
      fetchData(finParams);
      return;
    }
    fetchData(params);
  }


  return {
    fetchData: fetchDataHandler,
    loading,

    setListData,
    listData,
    setItemData,
    itemData,
    total,
    extraData,
    params,
    setParams,
    sorterObj,
    setSorter,
    retrieveModalVisible,
    setRetrieveModalVisible,
    createModalVisible,
    setCreateModalVisible,
    updateModalVisible,
    setUpdateModalVisible,
    historyModalVisible,
    setHistoryModalVisible,
    createModalTitle,
    setCreateModalTitle,
    handleCreate,
    handleUpdate,
    onTableChange,
    onPaginationChange,
    title,
    defaultParams,
    defaultFetchHangler,
    ...rowSelectConfig,
    ...props, // 后面的属性是经过处理的，需要放在...props之后，避免被覆盖
    btnGroup: btnGroupNodes,
    columns,
    tableUseColumns,
    filterConfig,
    historyModalConfig,
    filterBeforeReset,

    onCreate,
    onEdit,

    submitDisabled,
    setSubmitDisabled,

    pageIndex,
    setPageIndex,
    pageSize,
    setPageSize,
    customPagination

  };
};
