.flat-table-container-col {
  display: flex;
  flex-direction: column;

  .self-creator-clue-row td {
    background-color: #f2f2f2;
  }
}

.section-item {
  border-radius: 8px;
  margin-bottom: 8px;

  &:last-child {
    margin-right: 0;
  }

  .ant-pagination {
    margin: 10px 0 0 0;
  }

  .ant-divider-horizontal {
    width: calc(100% + 16px);
    margin: 12px 0 8px -8px;
  }



}

.flat-table-main-header {
  .operation-bar {
    position: sticky;
    top: 0;
    z-index: 3;
  }
}

.operation-bar {
  padding-top: 12px;
  padding-bottom: 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;

  background-color: #fff;

  h2 {
    font-size: 15px;
    font-weight: bold;
    flex: 1 1 0%;
    text-align: left;
  }
}

.flat-table-main-header {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  padding: 0 16px 12px !important;
  background-color: #Fff;
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;


  .ant-table-wrapper {
    flex: 1 1 0%;
  }

  .ant-btn {
    height: 28px !important;
  }

  .action-btn {
    .ant-btn {
      padding: 0 10px;
    }
  }

  .create-btn,
  .ant-btn-primary {
    border-color: var(--ant-primary-color);
    color: var(--ant-primary-color);
    background-color: transparent;

    &:hover {
      background-color: inherit;
      color: var(--ant-primary-color);
    }
  }

  // .remove-btn {
  //   color: var();
  // }
}
