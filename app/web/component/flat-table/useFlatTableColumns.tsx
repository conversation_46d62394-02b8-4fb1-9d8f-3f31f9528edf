import { getColumnKey } from 'antd/lib/table/util';
import moment from 'moment-timezone';
import { ColumnSetting } from './types';

export default (
  columns: (ColumnSetting | string)[],
  localeData: Record<'Fields', any> & Record<string, any>,
): ColumnSetting[] => {
  if (!Array.isArray(columns)) {
    throw new Error('columns must be an array');
  }

  if (!localeData?.Fields) {
    throw new Error('i18n localeData is required');
  }

  return columns
    .filter((item) => item)
    .map((col) => {
      const { title, dataIndex, key, formatter, create, update } = col as ColumnSetting;

      // TODO, 需要支持数组，待优化
      if (Array.isArray(dataIndex)) {
        throw new Error('dataIndex must be a string');
      }

      let _key = key;
      if (typeof col === 'string') {
        _key = col;
      } else {
        // TODO, 如果传入的 key = 'key'，也就是数据库的字段名，就是 key，则可能有问题。待排查。
        _key = getColumnKey(col, String(Math.random()));
        Reflect.deleteProperty(col as ColumnSetting, 'key');
      }

      let _title = title;
      let stringTitle = typeof title === 'string' && !!title ? title : localeData.Fields[_key as string];
      if (typeof col === 'string') {
        _title = localeData.Fields[col];
      } else if (typeof col === 'object' && !title) {
        _title = localeData.Fields[_key as string];
        Reflect.deleteProperty(col as ColumnSetting, 'title');
      }

      let _update = update;
      if (create && update && typeof update === 'boolean') {
        _update = create;
        Reflect.deleteProperty(col as ColumnSetting, 'update');
      }

      let _dataIndex = dataIndex || _key;
      if (typeof col === 'object') {
        Reflect.deleteProperty(col as ColumnSetting, 'dataIndex');
      }

      const defaultColConfig: ColumnSetting = {
        title: _title,
        stringTitle,
        dataIndex: _dataIndex as any,
        key: _key,
        update: _update,
        render: (val) => (val || val === 0 || val === false ? String(val) : '-'),
      };

      if (formatter) {
        // 同时兼容 13 位的时间戳。
        Object.assign(defaultColConfig, {
          render: (val: number) =>
            val ? moment(val.toString().length === 13 ? val : val * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
        });

        Reflect.deleteProperty(col as ColumnSetting, 'formatter');
      }
      if (typeof col === 'string') {
        return defaultColConfig;
      }

      if (typeof col === 'object') {
        return {
          ...defaultColConfig,
          ...col,
        };
      }
      return col;
    });
};
