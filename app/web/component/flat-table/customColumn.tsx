import { Button, Checkbox, Popover, Space } from 'antd'
import React, { useEffect, useState } from 'react'
import DraggableList from '../draggable-list'
import { HolderOutlined, InsertRowAboveOutlined, SettingOutlined } from '@ant-design/icons'
import './customColumn.scss'
import { isFunction } from '@@/lib/tool'
import { ColumnSetting } from './types'

interface ICheckedCustomColumn {
  id: string
  title: string
  index: number
  checked: boolean
}

export enum ChangeType {
  CHECKED,
  SORTED
}

interface IProps {
  columns: (ColumnSetting & { checked?: boolean })[]
  onChange?: (checkedValues: ICheckedCustomColumn[], indexInfo: { from?: number, to?: number }, type: ChangeType) => void;

}

export default function CustomColumn({
  columns,
  onChange
}: IProps) {
  const [dataSource, setDataSource] = useState<ICheckedCustomColumn[]>([]);

  useEffect(() => {
    const result = columns.map(it => ({
      id: it.dataIndex,
      dataIndex: it.dataIndex,
      title: it.title,
      index: it.index,
      checked: it.checked ?? true
    }));
    setDataSource(result as ICheckedCustomColumn[])
  }, [columns])
  const handleCheckedDragChange = (newDataSource, indexInfo) => {
    const newCheckedList = newDataSource.map(i => ({
      ...i,
      isDragDisabled: i.isDragDisabled || false
    }));
    setDataSource(newCheckedList);
    isFunction(onChange) && onChange(newCheckedList, indexInfo, ChangeType.SORTED);

  }
  return (
    <Popover placement='bottomRight' trigger="click" content={(
      <DraggableList
        dataSource={dataSource}
        onDragChange={handleCheckedDragChange}
        wrapperStyle={{
          height: '100%'
        }} renderItem={
          (item: any, draggableStateSnapshot, index) => {
            return (
              <Space size={8} className={`dragging-space-item  ${draggableStateSnapshot?.isDragging ? 'dragging' : ''}`}>
                <HolderOutlined />
                <Checkbox onChange={(e) => {
                  const checked = e.target.checked;
                  dataSource[index].checked = checked;
                  setDataSource([...dataSource]);
                  isFunction(onChange) && onChange(dataSource, {}, ChangeType.CHECKED);
                }} checked={item.checked}>{item.title}</Checkbox>
              </Space>
            )
          }
        }></DraggableList>
    )}>
      <Button icon={<InsertRowAboveOutlined />}>
      </Button>
    </Popover>
  )
}
