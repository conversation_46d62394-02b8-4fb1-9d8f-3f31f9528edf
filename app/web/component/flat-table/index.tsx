import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { Table, Row, Col, Space, TableProps, Pagination } from 'antd';

import { __ } from '@/modules/format';

import { RetrieveModal, CreateModal, UpdateModal, HistoryModal, Filter } from '@/component';
import './index.scss';
import { UseTableReturn } from './types';
import { defaultParamsMapToDataHandler } from './tool';


const FlatTable = (props: UseTableReturn, ref) => {
  const filterRef: any = useRef(null);
  const createRef: any = useRef(null);
  const updateRef: any = useRef(null);
  const {
    title,
    columns,
    tableUseColumns,
    params,
    setParams,
    setPageIndex,
    btnGroup,
    retrieveModalVisible,
    itemData,
    setRetrieveModalVisible,
    createModalVisible,
    createModalTitle,
    setCreateModalVisible,
    handleCreate,
    updateModalVisible,
    setUpdateModalVisible,
    handleUpdate,
    historyModalVisible,
    setHistoryModalVisible,
    loading,
    listData,
    total,
    pageIndex,
    pageSize,
    onTableChange,
    onFilterChange,
    filterBeforeReset,
    defaultParams,
    filterConfig,
    rowSelection,
    hiddenFilter = false,
    createModalClose,
    updateModalClose,
    clearRowSelect,
    filterColumnNum = 4,
    rowClassName,

    submitDisabled,
    onPaginationChange,

    customPagination


  } = props;

  const tableProps: TableProps<any> = {
    columns: tableUseColumns?.filter(v => !v.hidden && v.checked !== false),
    dataSource: listData,
    loading,
    scroll: { x: '100%', scrollToFirstRowOnChange: true },
    pagination: {
      total,
      current: pageIndex,
      defaultPageSize: pageSize,
      showTotal: total => __('TotalItems', total.toString()),
    },
    onChange: onTableChange,
    sortDirections: ['descend', 'ascend'],
    rowSelection,
    rowKey: 'id',
    sticky: {
      offsetHeader: 40,
      offsetScroll: 2,
      // @ts-ignore
      getContainer: () => document.querySelector('.main-content'),
    },
  };
  if (rowClassName) {
    tableProps.rowClassName = rowClassName
  }
  if (customPagination) {
    tableProps.pagination = false;
  }


  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    filterForm: filterRef?.current?.form,
    createForm: createRef?.current?.form,
    updateForm: updateRef?.current?.form,
  }));
  const transformDate = (columns || []).find(it => it?.mapToData);



  return (
    <>
      <Row gutter={24} style={{ height: '100%' }}>
        <Col span={24} className='flat-table-container-col'>
          {
            !hiddenFilter && (
              <Filter
                params={params}
                columns={columns}
                hideSpread
                filterColumnNum={filterColumnNum}
                ref={filterRef}
                onChange={(values: any, type) => {
                  const v = onFilterChange ? onFilterChange(values) : values;
                  if (type === 'reset') {
                    const params = filterBeforeReset ? filterBeforeReset() : {};
                    if (transformDate) {
                      defaultParamsMapToDataHandler(transformDate, params);
                    }
                    setParams(params);
                  } else {
                    const newParams = { ...v };
                    setParams(newParams);
                  }
                  clearRowSelect && clearRowSelect();
                  setPageIndex(1);
                }}
                {...filterConfig}
              />
            )
          }
          <section className="section-item flat-table-main-header mb-0">
            <div className="operation-bar">
              <h2>{title || ''}</h2>
              <Space>
                {btnGroup?.map((item, index) => {
                  return <span key={`button-${index}`}>{item}</span>;
                })}
              </Space>
            </div>
            {
              // @ts-ignore
              <Table {...tableProps} />
            }
            {
              customPagination && (
                <div style={{ marginTop: 10, textAlign: 'right' }}>
                  <Pagination
                    current={pageIndex}
                    total={Number(total)}
                    pageSize={pageSize}
                    showTotal={total => `Total ${total} items`}
                    onChange={(newPage, newPageSize) => {
                      onPaginationChange(newPage, newPageSize)
                    }}
                  />
                </div>
              )
            }
          </section>
        </Col>
      </Row>
      <RetrieveModal
        columns={columns}
        itemData={itemData}
        visible={retrieveModalVisible}
        setVisible={setRetrieveModalVisible}
      />
      <CreateModal
        modalType={props.modalType}
        title={createModalTitle}
        columns={columns}
        itemData={itemData}
        ref={createRef}
        visible={createModalVisible}
        submitDisabled={submitDisabled}
        setVisible={() => {
          createModalClose && createModalClose();
          setCreateModalVisible(false)
        }}
        onChange={handleCreate}
        width={props.modalWidth || 900}
        layout={props.layout}
        style={{ top: '10px' }}
        isHiddenConfirm={props.isHiddenConfirm}
        customBtnCreate={props.customBtnCreate}
        confirmText={props.confirmText}
        customRenderFormContent={props.customRenderFormContent}
        chattingRecordsRender={props.chattingRecordsRender}
        modelWrapClassName={props.modelWrapClassName}
        maskStyle={props.maskStyle}
        extra={props.extra}
        formListConfig={props.formListConfig}

      />
      <UpdateModal
        modalType={props.modalType}
        columns={columns}
        itemData={itemData}
        ref={updateRef}
        visible={updateModalVisible}
        submitDisabled={submitDisabled}
        setVisible={() => {
          updateModalClose && updateModalClose();
          setUpdateModalVisible(false)
        }}
        onChange={handleUpdate}
        width={props.modalWidth || 900}
        layout={props.layout}
        style={{ top: '10px' }}
        isHiddenConfirm={props.isHiddenConfirm}
        customBtnCreate={props.customBtnCreate}
        confirmText={props.confirmText}
        customRenderFormContent={props.customRenderFormContent}
        chattingRecordsRender={props.chattingRecordsRender}
        extra={props.extra}
        modelWrapClassName={props.modelWrapClassName}
        maskStyle={props.maskStyle}
      />
      <HistoryModal visible={historyModalVisible} setVisible={setHistoryModalVisible} />
    </>
  );
};

export default React.forwardRef(FlatTable);
