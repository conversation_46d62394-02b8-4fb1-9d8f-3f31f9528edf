import { FormElementProps } from "@flat-design/components-pc/es/flat-form-modal/type";
import { IFlatHistoryModal } from "@flat-design/components-pc/es/flat-history-modal";
import { DatePickerProps, FormInstance, FormItemProps } from "antd";
import { ColumnType } from "antd/lib/table";
import { GetRowKey } from "antd/lib/table/interface";

enum FormModalType {
  Create = 'create',
  Update = 'update',
  Retrieve = 'retrieve',
}


export interface UseTableReturn {
  columns?: any[];
  tableUseColumns?: any[];
  loading: boolean;
  pageIndex: number;
  setPageIndex: React.Dispatch<React.SetStateAction<number>>;
  pageSize: number;
  setListData: React.Dispatch<React.SetStateAction<any[]>>;
  listData: any[];
  setItemData: React.Dispatch<React.SetStateAction<any>>;
  itemData: any;
  setPageSize: React.Dispatch<React.SetStateAction<number>>;
  total: number;
  extraData: any;
  params: any;
  setParams: React.Dispatch<React.SetStateAction<any>>;
  sorterObj: any;
  setSorter: React.Dispatch<React.SetStateAction<any>>;
  retrieveModalVisible: boolean;
  setRetrieveModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  createModalVisible: boolean;
  setCreateModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  updateModalVisible: boolean;
  setUpdateModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  historyModalVisible: boolean;
  setHistoryModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  createModalTitle: string;
  setCreateModalTitle: React.Dispatch<React.SetStateAction<string>>;
  handleCreate: (options: any) => Promise<void>;
  handleUpdate: (options: any, itemId?: number) => Promise<void>;
  onTableChange: (pagination: any, filters: any, sorter: any) => void;
  onPaginationChange: (page: number, pageSize: number) => void;
  btnGroup: Array<React.ReactElement | string | null>;
  title: string | React.ReactElement;
  fetchData: any;
  filterBeforeReset?: () => void
  onFilterChange?: (options: any) => any;
  defaultParams?: any; // 默认固定传参
  filterConfig?: any;
  historyModalConfig?:
  | IFlatHistoryModal
  | ((args: { itemData: any; historyModalVisible: boolean }) => IFlatHistoryModal); // 操作记录弹窗配置
  modalType?: 'drawer' | 'modal'
  modalWidth?: number
  layout?: { labelCol: { span: number }, layout: 'vertical' | 'inline' | 'horizontal' }
  rowSelection?: {
    selectedRowKeys: React.Key[];
    onChange: (newSelectedRowKeys: React.Key[], selectedRows: any) => void;
  };
  defaultFetchHangler: (otherParams?: Recordable) => void
  hiddenFilter?: boolean
  confirmText?: string
  isHiddenConfirm?: boolean
  customBtnCreate?: (type: 'create' | 'update', form: FormInstance, onFinish: (values: Record<string, any>) => Promise<Recordable>, loading: boolean) => React.ReactElement | null
  customRenderFormContent?(type: 'create' | 'update', form: FormInstance): React.ReactElement | React.ReactElement[] | null
  createModalClose?: () => void
  updateModalClose?: () => void
  clearRowSelect?: () => void
  filterColumnNum?: number
  chattingRecordsRender?: null | ((type: 'create' | 'update', form: FormInstance) => React.ReactElement)
  onCreate: (options: any) => void
  onEdit: (options: any, index: number) => void
  rowClassName?: (record: any, index: number) => string

  submitDisabled?: boolean
  setSubmitDisabled?: React.Dispatch<React.SetStateAction<boolean>>
  modelWrapClassName?: string
  maskStyle?: React.CSSProperties
  extra?: React.ReactNode
  formListConfig?: {
    addBtnName?: string
    default?: Recordable
    beforeCreate?: (form: FormInstance) => void
  }
  customPagination?: boolean
}

export interface FilterSetting {
  type: string;
  options?: any[];
  showTime?: boolean;
  title?: string | React.ReactElement;
  hidden?: boolean;
  required?: boolean;
  mode?: any;
  initialValues?: any;
  index?: number;
  picker?: DatePickerProps['picker'];
  allowClear?: boolean
  multiple?: boolean
  dropdownMatchSelectWidth?: number
  placeholder?: string
  maxTagCount?: number
  disabled?: boolean
}

interface FormFieldSetting {
  type: 'Input' | 'InputNumber' | 'Select' | 'TextArea' | 'Image' | 'DatePicker' | 'RangePicker' | 'TreeSelect' | 'Radio' | 'Component' | 'Text' | 'password' | 'AutoComplete' | 'Cascader' | 'Title' | 'Checkbox';
  index?: number;
  group?: string;
  uploadButtonTips?: React.ReactNode;
  render?: (val?: any, record?: any, itemName?: string, isListIndex?: number) => React.ReactNode;
  addonBefore?: string | React.ReactNode;
  addonAfter?: string | React.ReactNode;
  formItemProps?: FormItemProps | ((itemData: any, form: FormInstance) => FormItemProps);
  formElementProps?: FormElementProps | ((itemData: any, form: FormInstance) => FormElementProps);
  span?: number
  min?: number
  max?: number
  precision?: number
  labelCol?: { span: number, offset?: number }
  options?: { label: string, value: any }[] | { label: string, options: ICommonRecord[] }[]
  pureComponent?: boolean
  maxLength?: number
  showCount?: boolean
  disabled?: boolean
  hidden?: boolean
  dropdownRender?: (val?: any, record?: any) => React.ReactNode
  mode?: 'multiple' | 'tags' | 'default'
  optionType?: 'default' | 'button'
  showTime?: boolean
  maxTagCount?: number
  onChange?: (val: any, form: FormInstance) => void
  tooltip?: string
  rules?: any[]
}


export interface ColumnSetting extends ColumnType<any> {
  [FormModalType.Create]?: FormFieldSetting | ((itemData: any, form: FormInstance) => FormFieldSetting | undefined); // 新增弹窗字段配置
  [FormModalType.Update]?:
  | boolean
  | FormFieldSetting
  | ((itemData: any, form: FormInstance) => FormFieldSetting | undefined); // 编辑弹窗字段配置
  [FormModalType.Retrieve]?: FormFieldSetting | ((itemData: any, form: FormInstance) => FormFieldSetting | undefined); // 详情弹窗字段配置
  filter?: FilterSetting; // 筛选项字段配置
  group?: string; // 分组名，一般用于表单弹窗内部
  hidden?: boolean; // 是否隐藏
  formatter?: boolean; // 是否需要格式化
  stringTitle?: string; // 纯字符串列标题，导出时会用到
  index?: number
  showCount?: boolean
  export?: {
    mapping?: Record<string, any>
    title?: string
  }
  checked?: boolean
  renderString?: (val: any, record: Record<string, any>) => string; // render 函数，只输出字符串，可用于导出
}

