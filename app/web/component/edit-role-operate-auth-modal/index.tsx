import { Checkbox, Col, Modal, ModalProps, notification, Row } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { isFunction, isString } from '../../../lib/tool';
import { useQueryList } from '@/hooks';
import AuthTreeWithCRUD, { AuthTreeWithCRUDProps, TreeNode } from '@/component/auth-tree-with-CRUD';
import request from '@/modules/request';

export interface EditRoleOperateAuthModalProps {
  visible: boolean;
  auths: Array<{
    id: number;
    auth_code: string;
    operate_auth: string;
  }>;
  title?: ModalProps['title'];
  onOk?: () => void;
  onCancel?: ModalProps['onCancel'];
}

export default function EditRoleOperateAuthModal(props: EditRoleOperateAuthModalProps) {
  const {
    title = '角色操作权限',
    visible = false,
    auths,
    onOk,
    onCancel
  } = props;
  const {
    loading,
    setLoading,
    listData,
    fetchData: fetchDataOrigin
  } = useQueryList({
    fetchUrl: '/authority',
    pageSize: 100
  });

  const checkedAuthsRef = useRef<Array<{
    id: number;
    auths: string[]
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      const result = await request.put(`/roleAuth/bulkUpdate`, {
        data: checkedAuthsRef.current.map(item => ({
          id: item.id,
          operate_auth: item.auths.join(',')
        })),
        updateOnDuplicate: ['operate_auth'],
      }, { 'X-showMessage': true });
      isFunction(onOk) && onOk();
    } catch (e) {
      notification.error({
        message: '角色操作权限配置失败',
        description: e
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthTreeChange: AuthTreeWithCRUDProps['onChange'] = (value) => {
    checkedAuthsRef.current = value;
  };

  const treeData = useMemo(() => {
    const menu = listData.menu || [];
    const result: TreeNode[] = [];
    menu.forEach(item => {
      result.push(traveral(item));
    });

    function traveral(entry: any) {
      const node: TreeNode = {
        id: entry.code,
        title: entry.remark,
        key: entry.code,
        value: entry.code,
        path: entry.path,
        children: [],
        disabled: false,
        operateAuths: [],
        isLeaf: false,
        roleAuthId: 0
      };

      const authItem = auths.find(i => i.auth_code === entry.code);
      if (!authItem) {
        node.disabled = true;
      } else {
        node.operateAuths = isString(authItem.operate_auth) ? authItem.operate_auth.split(',') : [];
        node.roleAuthId = authItem.id;
      }
      if (Array.isArray(entry.children) && entry.children.length > 0) {
        entry.children.forEach(e => {
          e && node.children.push(traveral(e));
        });
      } else {
        node.isLeaf = true;
      }
      return node;
    }

    return result;
  }, [listData, auths]);

  useEffect(() => {
    fetchDataOrigin();
    checkedAuthsRef.current = [];
  }, [visible]);

  return (
    <Modal
      open={visible}
      title={title}
      centered
      onOk={handleConfirm}
      onCancel={onCancel}
      confirmLoading={isLoading}
      cancelButtonProps={{
        disabled: isLoading
      }}
      width={850}
    >
      <AuthTreeWithCRUD
        treeData={treeData}
        onChange={handleAuthTreeChange}
      />
    </Modal>
  );
}
