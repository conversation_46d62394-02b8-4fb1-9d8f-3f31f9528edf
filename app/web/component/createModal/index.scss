.group-section {

  .group-title {
    font-weight: bold;
    color: #666;
    font-size: 15px;
    position: relative;
    left: -6px;
  }

  .group-content {
    // background: #fbfbfb;
    // border-radius: 4px;
    // border: 1px solid #f0f3f7;
    // padding: 12px 12px 0 12px;
    // margin-top: 10px;
  }

  .modal-title-type {
    margin-bottom: 4px;

    .ant-form-item-control {
      display: none;
    }
  }

}

.handler-modal-container {
  padding-right: 30px;
  max-height: 74.5vh;
  overflow-y: scroll;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, .5) transparent;
}

.handler-modal-btns {
  .ant-btn {
    height: 34px;
  }

  .ant-btn-primary {
    background-color: var(--soy-primary-color);
    color: #fff;
  }

}

#control-hooks .ant-col-24 .ant-form-item-label.ant-col-3,
#control-hooks .ant-col-24 .ant-form-item-label.ant-col-4 {
  padding-right: 3px;
}

#control-hooks .ant-col-12 .ant-form-item-control-input-content {
  margin-left: 3px;
}
