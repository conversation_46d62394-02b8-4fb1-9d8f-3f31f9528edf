import React, { useImperative<PERSON>andle, useState, useContext, forwardRef } from 'react';
import { Modal, Form, Upload, Input, Button, Select, Space, Tooltip, DatePicker, TreeSelect, InputNumber, Row, Col, Radio, TimePicker, Switch, Drawer, AutoComplete, Cascader, Checkbox, Divider } from 'antd';
import { DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { groupByKey, rangePickerRangesNotToday } from '@@/lib/tool';
import { AppContext } from '@/store';
import { __ } from '@/modules/format';
import './index.scss';
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker: TimeRangePicker } = TimePicker;

const defaultLayout = {
  labelCol: { span: 6 }
};

const taillayout = {
  wrapperCol: {
    offset: 0,
    span: 24
  }
};

function CreateModal(props, ref) {
  if (!props.visible) {
    return null;
  }
  const { localeData } = useContext(AppContext);
  let filterColumns = [] as any;
  props.columns.forEach(item => {
    if (item.create && item.create.type) {
      const newItem = Object.assign({}, item);
      newItem.title = item.create.title || item.title;
      newItem.group = item.create.group || '';
      for (let key in item.create.formItemProps || {}) {
        if (Object.prototype.hasOwnProperty.call(item.create.formItemProps || {}, key)) {
          if (!item.create.formItemProps?.[key]) { continue }
          newItem[key] = item.create.formItemProps[key] || null;
        }
      }
      Reflect.deleteProperty(item, 'checked');
      filterColumns.push(newItem);
    }
  });
  // filterColumns = filterColumns.sort((a, b) => a.create.index - b.create.index);
  filterColumns = groupByKey(filterColumns, 'group');
  filterColumns.forEach(it => {
    it.data.sort((a, b) => a.create.index - b.create.index);
  })
  let form = props.formInstance;
  if (!form) {
    const [useform] = Form.useForm();
    form = useform;
  }
  const [loading, setLoading] = useState(false);
  useImperativeHandle(ref, () => ({
    // changeVal 就是暴露给父组件的方法
    setForm: newVal => {
      form.setFieldsValue(newVal);
    },
    form
  }));
  const onFinish = async values => {
    const tempObj = {};
    const cloneValues = { ...values };
    for (const key in values) {
      if (typeof values[key] === 'undefined') {
        tempObj[key] = '';
      }
    }
    const allColumns = filterColumns.map(it => it.data).flat(2);
    const transformDate = allColumns.filter(it => it?.mapToData);
    if (transformDate?.length) {
      transformDate.forEach(it => {
        const [startKey, endKey, format] = it.mapToData;
        const dateItem = cloneValues[it.key];
        if (dateItem) {
          cloneValues[startKey] = dateItem[0].format(format || 'YYYY-MM-DD');
          cloneValues[endKey] = dateItem[1].format(format || 'YYYY-MM-DD');
        }
        Reflect.deleteProperty(cloneValues, it.key);

      })
    }
    setLoading(true);
    try {
      const result = await props.onChange(Object.assign({}, cloneValues, tempObj));
      return {
        values,
        result: result?.data
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const handleOk = () => {
    props.setVisible(false);
  };

  const handleCancel = () => {
    props.setVisible(false);
  };
  const onReset = () => {
    form.resetFields();
  };
  const renderFormItem = (colum, isListIndex?: number) => {
    let labelStyle: any = { fontWeight: 'normal', display: 'flex', alignItems: 'center' };
    if (colum.create.breakword) {
      labelStyle = { fontWeight: 'normal', whiteSpace: 'break-spaces', lineHeight: '1.2em', textAlign: 'left', display: 'flex', alignItems: 'center' };
    }
    let label = <span style={labelStyle}>{colum.title}</span>;
    if (colum.create.tooltip) {
      label = (
        <div style={labelStyle}>
          {colum.title}&nbsp;
          <Tooltip title={colum.create.tooltip}>
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      );
    }
    colum.create.rules = colum.create.rules || [];
    const hidden = colum.create.hidden;
    const taillayout = colum.create.taillayout || {};
    const labelCol = colum.create.labelCol;
    const isListMode = (isListIndex || isListIndex === 0);
    const itemName = isListMode ? [isListIndex, colum.dataIndex] : colum.dataIndex

    if (hidden) {
      return null;
    }
    const colon = colum.create.colon === 'false' ? false : true;
    const isDisabled = colum.create.disabled || props.disabled || false;
    let rules: any = [
      {
        required: colum.required,
        message: colum.create.placeholder || `${localeData.Input}${colum.title}`
      }
    ];
    switch (colum.create.type) {
      case 'Title':
        return (
          <Form.Item className='modal-title-type' required={colum.required} label={label} labelCol={labelCol} rules={rules}></Form.Item>
        )
      case 'Input':
        const InputProps = {
          placeholder: isDisabled ? '' : ` ${__('Please', 'Input')}${colum.title}`,
          onPressEnter: e => e.preventDefault(),
          // 默认事件： 失去焦点移除前后空格，移除事件可上层重置该方法
          onBlur: (e: any) => {
            const value = e.target.value;
            const newVal = value.trim();
            if (newVal !== value) {
              const itemKey = isListMode ? ['list', isListIndex, colum.dataIndex] : colum.dataIndex;
              form.setFieldValue(itemKey, newVal);
            }
          },
          ...colum.create,
          onChange: (value, option) => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form, option);
            }
          }
        };
        if (colum.create.inputType === 'password') {
          InputProps.type = 'password';
        }

        if (colum.create.rules) {
          rules = rules.concat(colum.create.rules);
        }
        Reflect.deleteProperty(InputProps, 'required'); // 删除 Input 组件上的 required 属性，避免使用原生的校验
        return (
          <Form.Item {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            rules={rules}
            labelCol={labelCol}
            extra={colum.extra}
          >
            <Input {...InputProps} />
          </Form.Item>
        );
      case 'InputNumber':
        const InputNumberProps = {
          onPressEnter: e => e.preventDefault(),
          style: { width: '100%' },
          placeholder: isDisabled ? '' : ` ${__('Please', 'Input')}${colum.title}`,

          ...colum.create
        };
        Reflect.deleteProperty(InputNumberProps, 'required'); // 删除 Input 组件上的 required 属性，避免使用原生的校验
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            <InputNumber {...InputNumberProps} />
          </Form.Item>
        );
      case 'Select':
        const options = colum.create.options || [];
        const SelectProps = {
          disabled: colum.disabled || props.disabled || false,
          mode: colum.mode, // 多选：multiple
          optionFilterProp: 'label',
          showArrow: true,
          allowClear: true,
          showSearch: true,
          placeholder: isDisabled ? '' : `${__('Please', 'Select')} ${colum.title}`,
          ...colum.create,
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.create.onSearch) {
              colum.create.onSearch(value, form);
            }
          },
          onSelect: value => {
            if (colum.create.onSelect) {
              colum.create.onSelect(value, form);
            }
          },
          onDeselect: value => {
            if (colum.create.onDeselect) {
              colum.create.onDeselect(value, form);
            }
          }
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            required={colum.required}
            // validateTrigger={['onChange', 'onSearch']}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')} ${colum.title}`
              }
            ].concat(colum.create.rules)}
          >
            <Select {...SelectProps}>
              {options.map(item => {
                return (
                  <Option key={item.value} value={item.value} label={item.label}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        );
      case 'Cascader':
        const cascaderOptions = {
          disabled: colum.disabled || props.disabled || false,
          mode: colum.mode, // 多选：multiple
          optionFilterProp: 'label',
          showArrow: true,
          allowClear: true,
          showSearch: true,
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          ...colum.create,
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.create.onSearch) {
              colum.create.onSearch(value, form);
            }
          },
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}

            // validateTrigger={['onChange', 'onSearch']}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ].concat(colum.create.rules)}
          >
            <Cascader {...cascaderOptions} >
            </Cascader>
          </Form.Item>
        );
      case 'AutoComplete':
        if (colum.create.rules) {
          rules = rules.concat(colum.create.rules);
        }
        const autoCompleteProps = {
          placeholder: isDisabled ? '' : colum.title,
          allowClear: true,
          disabled: isDisabled,
          filterOption: (inputValue, option) => {
            return option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          },
          ...colum.create
        };
        return (
          <Form.Item
            name={itemName}
            label={label}
            extra={colum.extra}
            rules={rules}
          >
            <AutoComplete {...autoCompleteProps} />
          </Form.Item>
        );
      case 'TextArea':
        const TextAreaStyle = {
          width: '100%',
          position: 'relative',
          border: '1px solid #d9d9d9',
          padding: '6px 10px',
          borderRadius: '6px'
        };
        if (colum.create.style) {
          Object.assign(TextAreaStyle, colum.create.style);
        }
        const TextAreaProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Input')}${colum.title}`,
          autoSize: colum.create.style ? false : { minRows: 3 },
          style: TextAreaStyle,
          spellCheck: false,
          ...colum.create,
          // 默认事件： 失去焦点移除前后空格，移除事件可上层重置该方法
          onBlur: (e: any) => {
            const value = e.target.value;
            const newVal = value.trim();
            if (newVal !== value) {
              const itemKey = isListMode ? ['list', isListIndex, colum.dataIndex] : colum.dataIndex;
              form.setFieldValue(itemKey, newVal);
            }
          },
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          }
        };
        Reflect.deleteProperty(TextAreaProps, 'required');
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            <TextArea {...TextAreaProps} />
          </Form.Item>
        );
      case 'DatePicker':
        const DatePickerProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          showTime: true,
          style: { width: '100%' },
          ...colum.create
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <DatePicker {...DatePickerProps} />
          </Form.Item>
        );
      case 'RangePicker':
        const { RangePicker } = DatePicker;
        const RangePickerProps = {
          showTime: false,
          style: { width: '100%' },
          ranges: rangePickerRangesNotToday,
          ...colum.create
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <RangePicker {...RangePickerProps} />
          </Form.Item>
        );
      case 'TimeRangePicker':
        const timeRangePickerProps = {
          showTime: true,
          style: { width: '100%' },
          ...colum.create
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <TimeRangePicker {...timeRangePickerProps} />
          </Form.Item>
        );
      case 'TreeSelect':
        const TreeSelectProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          showSearch: true,
          filterTreeNode: 'title',
          style: { width: '100%' },
          dropdownStyle: { maxHeight: 400, overflow: 'auto' },
          allowClear: true,
          ...colum.create,
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          }
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <TreeSelect {...TreeSelectProps} />
          </Form.Item>
        );
      case 'Radio':
        const radioOptions = colum.create.options || [];
        const radioGroupProps = {
          disabled: colum.disabled || false,
          name: itemName,
          ...colum.create,
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.create.onSearch) {
              colum.create.onSearch(value, form);
            }
          }
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            labelCol={labelCol}
            // validateTrigger={['onChange', 'onSearch']}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ].concat(colum.create.rules)}
          >
            <Radio.Group {...radioGroupProps}>
              {radioOptions.map(item => (
                <Radio key={item.value} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        );
      case 'Switch':
        if (hidden) {
          return null;
        }
        const SwitchProps = {
          ...colum.create
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={itemName}
            labelCol={labelCol}
            label={label}
            valuePropName="checked">
            <Switch {...SwitchProps} />
          </Form.Item>
        );
      case 'Checkbox':
        if (hidden) {
          return null;
        }
        const checkProps = {
          ...colum.create
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={itemName}
            labelCol={labelCol}
            valuePropName="checked">
            <Checkbox {...checkProps}>
              {label}
            </Checkbox>
          </Form.Item>
        );
      case 'Component':
        if (colum.create?.pureComponent) {
          return (
            colum.create.render(form, props.itemData[colum.key], itemName, isListIndex)
          )
        }
        return (
          <Form.Item
            {...taillayout}
            labelCol={labelCol}
            key={colum.dataIndex}
            name={itemName}
            label={label}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            {colum.create.render(form, props.itemData[colum.key], itemName, isListIndex)}
          </Form.Item>
        );
      default:
        break;
    }
  };
  const renderFields = (list, isListIndex?: number) => {
    const children: any = [];
    for (let i = 0; i < list.length; i++) {
      const hidden = list[i].create?.hidden;
      if (hidden) {
        continue
      }
      const spanNum = list[i].create.span || 12;
      let colStyle = {};
      if (i % 2 !== 0 && !list[i].create.span && spanNum === 24) {
        colStyle = {
          paddingLeft: 0,
          paddingRight: '24px'
        };
      }
      children.push(
        <Col span={spanNum} key={i} style={colStyle}>
          {renderFormItem(list[i], isListIndex)}
        </Col>
      );

    }
    return children;
  };

  const renderListFields = (list: any) => {
    return (
      <Form.List name="list">
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => {
              const fieldChildren = renderFields(list, index)
              return (
                (
                  <div key={field.key} style={{ position: 'relative' }}>
                    {fieldChildren}
                    <Divider style={{ margin: '10px 0 20px' }} />
                    {
                      index > 0 && (
                        <DeleteOutlined style={{ position: 'absolute', right: 34, top: 7, fontSize: 18 }} onClick={() => {
                          remove(field.name)
                        }} />
                      )
                    }
                  </div>
                )
              )
            })}

            {
              props.title !== 'Copy' && (
                <Col span={24}>
                  <Form.Item label="  " labelCol={{ span: 4 }} colon={false} >
                    <Button type="dashed" onClick={() => {
                      if (props.formListConfig.beforeCreate) {
                        props.formListConfig.beforeCreate(form)
                      }
                      add(props.formListConfig.default || {})
                    }} block >{props.formListConfig.addBtnName}</Button>
                  </Form.Item>
                </Col>
              )
            }
          </>
        )}
      </Form.List>
    )
  }

  // 自定义底部按钮
  let footerBtns: any = null;
  if (props.footerBtns) {
    footerBtns = props.footerBtns;
    // delete props.footerBtns;
  }

  const ModalProps = {
    title: props.title || localeData.Create,
    open: props.visible,
    onOk: handleOk,
    onCancel: handleCancel,
    onClose: handleCancel,
    centered: true,
    footer: null,
    width: 900,
    maskClosable: false,
    ...props
  };


  Reflect.deleteProperty(ModalProps, 'visible');

  const layout = props.layout || defaultLayout;
  let footerBtnDom: any = null;
  const modalType = props.modalType
  if (footerBtns) {
    footerBtnDom = (
      <Space size="middle">
        {footerBtns.map((item: any, index: number) => {
          const { type, label, onBtnClick } = item;
          let btnDom: any = null;
          switch (type) {
            case 'confirm':
              btnDom = (
                <Button key="f_confirm_btn" id="f_confirm_btn" disabled={props.submitDisabled} loading={loading} type="primary" htmlType="submit" onClick={() => {
                  if (modalType !== 'drawer') { return };
                  const values = form.validateFields();
                  onFinish(values);
                }}>
                  {label}
                </Button>
              );
              break;
            case 'cancel':
              btnDom = (
                <Button key="f_cancel_btn" id="f_cancel_btn" htmlType="button" onClick={handleCancel}>
                  {label}
                </Button>
              );
              break;
            case 'custom':
              btnDom = (
                <Button
                  key={`f_custom_btn_${index}`}
                  id={`f_custom_btn_${index}`}
                  loading={loading}
                  htmlType="button"
                  type="primary"
                  onClick={() => {
                    if (onBtnClick) {
                      onBtnClick();
                    }
                  }}
                >
                  {label}
                </Button>
              );
              break;
            default:
              break;
          }
          return btnDom;
        })}
      </Space>
    );
  } else {
    footerBtnDom = (
      <Space size="middle" className='handler-modal-btns'>
        <Button htmlType="button" onClick={handleCancel}>
          {localeData.Cancel}
        </Button>
        {
          !props.isHiddenConfirm && (
            <Button loading={loading} disabled={props.submitDisabled} type="primary" htmlType="submit" onClick={async () => {
              if (modalType !== 'drawer') { return };
              try {
                const values = await form.validateFields();
                onFinish(values);
              } catch (err: any) {
                const names = err?.errorFields?.[0]?.name;
                if (!names) { return };
                form.scrollToField(names, {
                  behavior: 'smooth',
                  block: 'center'
                })
              }
            }}>
              {props.confirmText || localeData.Confirm}
            </Button>
          )
        }
        {
          props.customBtnCreate && <>{props.customBtnCreate('create', form, async (coverValues: Recordable = {}) => {
            try {
              const values = await form.validateFields();
              return onFinish({
                ...values,
                ...coverValues
              });
            } catch (err: any) {
              const names = err?.errorFields?.[0]?.name;
              if (!names) { return };
              form.scrollToField(names, {
                behavior: 'smooth',
                block: 'center'
              })
            }
          }, loading)}</>

        }
      </Space>
    );
  }

  if (props.modalType === 'drawer') {
    return (
      <Drawer {...ModalProps} keyboard={false} className={` ${props.chattingRecordsRender ? 'drawer-container-need-chat' : ''} `} maskClosable={false} style={{ top: 0 }} footer={props.disabled ? null : footerBtnDom} footerStyle={{ textAlign: 'right' }}>
        <div className='drawer-container'>
          <div className='drawer-container-form'>
            <Form {...layout} disabled={props.disabled || false} colon={false} form={form} initialValues={props.itemData} name="control-hooks" onFinish={onFinish}>
              <div >
                {filterColumns.map(groupItem => {
                  return (
                    <div key={groupItem.group} className="group-section">
                      {groupItem.group ? <div className="group-title">{groupItem.group}</div> : null}
                      <div className="group-content">
                        <Row gutter={24}>{props.formListConfig ? renderListFields(groupItem.data) : renderFields(groupItem.data)}</Row>
                      </div>
                    </div>
                  );
                })}
                {
                  props.customRenderFormContent && props.customRenderFormContent('create', form)
                }
              </div>
            </Form>
          </div>
          {
            props.chattingRecordsRender && (
              <div className='drawer-container-chat'>
                {props.chattingRecordsRender('create', form)}
              </div>
            )
          }
        </div>

      </Drawer>
    )
  }

  return (
    <>
      <Modal {...ModalProps} wrapClassName={props.modelWrapClassName} maskStyle={props.maskStyle}>
        <Form {...layout} colon={false} form={form} initialValues={props.itemData} name="control-hooks" onFinish={onFinish}>
          <div className='handler-modal-container'>
            {filterColumns.map(groupItem => {
              return (
                <div key={groupItem.group} className="group-section">
                  {groupItem.group ? <div className="group-title">{groupItem.group}</div> : null}
                  <div className="group-content">
                    <Row gutter={24}>{props.formListConfig ? renderListFields(groupItem.data) : renderFields(groupItem.data)}</Row>
                  </div>
                </div>
              );
            })}
            {
              props.customRenderFormContent && props.customRenderFormContent('create', form)
            }
          </div>
          {
            props.disabled ? null : (
              <Form.Item {...taillayout} style={{ textAlign: 'right', marginBottom: '0', paddingTop: '8px', paddingBottom: '4px' }}>
                {footerBtnDom}
              </Form.Item>
            )
          }
        </Form>
      </Modal>
    </>
  );
}

export default forwardRef(CreateModal);
