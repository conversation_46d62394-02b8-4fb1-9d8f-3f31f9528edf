import React, { useEffect, useState } from 'react';
import { Checkbox, Col, Drawer, Form, message, Popover, Row, Select, Tag, Tooltip } from 'antd'
import FlatTable from '@/component/flat-table'
import { BtnGroupType, useFlatTable } from '@/component/flat-table/useFlatTable'
import './index.scss';
import {
  FlatTableActionType,
  FlatTableBtnGroupType,
  FlatSwitchButton,
} from '@flat-design/components-pc';
import UploadInput from '../upload-file';
import moment from 'moment';
import { CUSTOMER_STATUS_MAP, FOLLOW_UP_KEY_INFO, FOLLOW_UP_RECORD_TYPE, GET_COOP_BUS_TO_SIMPLIFY, userIsMarketer } from '@@/lib/constant';
import { CUSTOMER_COOP_TYPE } from '@/page/customerManage/hook/contact';
import Editor from '../editor';
import useFlatTableColumns from '../flat-table/useFlatTableColumns';
import request from '@/modules/request';
import useSelector from '@/hooks/useSelector';
import useAppStore from '@/store/app.store';
import useUserStore from '@/store/user.store';
import { beforeHandleCreate } from '@/page/clueManage/followUpRecord/hook';


interface IProps {
  visible: boolean
  onClose: () => void
  type: 'clue' | 'client'
  itemData: Record<string, any>
}
export default function FollowUpRecord(props: IProps) {
  const [clueList, setClueList] = useState<Record<string, any>[]>([]);
  const [companyClientInfo, setCompanyClientInfo] = useState({
    client_status: '',
    coop_bus: ''
  });
  const curUserInfo = useUserStore(state => state);
  const userIsMKT = userIsMarketer(curUserInfo);
  const { ADV_BD_USERS, ADV_OP_USERS, PUB_BD_USERS, PUB_OP_USERS } = useAppStore(useSelector(['ADV_BD_USERS', 'PUB_BD_USERS', 'ADV_OP_USERS', 'PUB_OP_USERS']));
  const followUpUsers = [
    ...ADV_BD_USERS,
    ...ADV_OP_USERS,
    ...PUB_BD_USERS,
    ...PUB_OP_USERS
  ].map(it => ({
    label: it.label,
    value: it.label
  }))
  const columns = useFlatTableColumns([
    {
      key: 'date',
      hidden: true,
      filter: {
        type: 'RangePicker',
        // @ts-ignore
        allowClear: false,
        picker: "date",
        title: 'Date'
      },
      // @ts-ignore
      mapToData: ['startDate', 'endDate', 'YYYY-MM'],
      update: true,
      checked: false
    },

    {
      key: 'id',
      width: 60,
      index: 1,
    },
    {
      key: 'company_name',
      index: 2,
      width: 250,
      filter: {
        type: 'Select',
        options: clueList,
        mode: 'multiple',
        maxTagCount: 3,
        // options: clueList.map((item) => ({
      },
      create: {
        type: 'Component',
        pureComponent: true,
        span: 24,
        render(form, value, itemName, isListIndex) {
          return (
            <Form.Item label="Company" required labelCol={{ span: 4 }}>
              <Row gutter={[16, 16]}>
                <Col span={18}>
                  <Form.Item name={itemName} rules={[{ required: true, message: 'Please enter the company name' }]}>
                    <Select disabled={true} options={clueList} showSearch optionFilterProp="label" placeholder="Please select company" onChange={(value, option: any) => {
                      const crmClient = option.crm_client;

                      setCompanyClientInfo({
                        client_status: CUSTOMER_STATUS_MAP[crmClient?.client_status] || '',
                        coop_bus: GET_COOP_BUS_TO_SIMPLIFY(crmClient?.coop_bus || '') || ''
                      })
                    }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={[isListIndex || 0, 'key_info']} valuePropName="checked">
                    <Checkbox >
                      Key Info
                    </Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ marginTop: 4 }}>
                <Col span={8}>
                  {companyClientInfo.client_status ? `Customer Status: ${companyClientInfo.client_status}` : ''}
                </Col>
                <Col span={8} style={{ whiteSpace: 'nowrap' }}>
                  {companyClientInfo.coop_bus ? `Cooperation Type: ${companyClientInfo.coop_bus}` : ''}
                </Col>
              </Row>
            </Form.Item>
          )
        }
      },
      update: {
        type: 'Component',
        pureComponent: true,
        span: 24,
        render(form, value) {
          return (
            <Form.Item label="Company" required labelCol={{ span: 4 }}>
              <Row gutter={[16, 16]}>
                <Col span={18}>
                  <Form.Item name={'company_name'} rules={[{ required: true, message: 'Please enter the company name' }]}>
                    <Select disabled={true} options={clueList} showSearch optionFilterProp="label" placeholder="Please select company" onChange={(value, option: any) => {
                      const crmClient = option.crm_client;

                      setCompanyClientInfo({
                        client_status: CUSTOMER_STATUS_MAP[crmClient?.client_status] || '',
                        coop_bus: GET_COOP_BUS_TO_SIMPLIFY(crmClient?.coop_bus || '') || ''
                      })
                    }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={'key_info'} valuePropName="checked">
                    <Checkbox >
                      Key Info
                    </Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ marginTop: 4 }}>
                <Col span={8}>
                  {companyClientInfo.client_status ? `Customer Status: ${companyClientInfo.client_status}` : ''}
                </Col>
                <Col span={8} style={{ whiteSpace: 'nowrap' }}>
                  {companyClientInfo.coop_bus ? `Cooperation Type: ${companyClientInfo.coop_bus}` : ''}
                </Col>
              </Row>
            </Form.Item>
          )
        }
      },
      render(value, record) {
        return `${record.clue_id}:${value}`
      }
    },
    {
      key: 'type',
      hidden: true,
      filter: {
        type: 'Select',
        options: FOLLOW_UP_RECORD_TYPE
      },
      create: {
        span: 24,
        labelCol: { span: 4 },
        type: 'Select',
        options: FOLLOW_UP_RECORD_TYPE,
        formItemProps: {
          required: true,
        }
      },
      update: true
    },

    {
      key: 'content',
      index: 3,
      width: 200,
      filter: {
        type: 'Input',
      },
      create: {
        type: 'Component',
        span: 24,
        labelCol: { span: 4 },
        formItemProps: {
          required: true,
        },
        render(form, value, itemName) {
          return <Editor value={value} onChange={(val) => {
            form.setFieldValue(itemName, val)
          }} />
        }
      },
      update: true,
      ellipsis: true,
      render(content, record) {
        return (
          <div>
            {record.key_info === 1 ? <Tag color="red">Key</Tag> : null}
            <Popover placement="topLeft" content={<div dangerouslySetInnerHTML={{ __html: content }}></div>}>
              {content}
            </Popover>
          </div>
        )
      }
    },
    {
      key: 'key_info',
      hidden: true,
      filter: {
        type: 'Select',
        options: FOLLOW_UP_KEY_INFO
      }
    },
    {
      key: 'creator',
      index: 4,
      width: 160,
      filter: {
        type: 'Select',
        options: followUpUsers,
        mode: 'multiple',
        maxTagCount: 3
      }
    },
    {
      key: 'utime',
      index: 5,
      width: 120,
      render(time) {
        return moment(time).format('YYYY-MM-DD')
      }
    },

    {
      key: 'client_status',
      width: 170,
      index: 6,
      filter: {
        type: 'Select',
        options: Object.entries(CUSTOMER_STATUS_MAP).map(([key, value]) => ({
          label: value,
          value: key
        }))
      },
      render(text, record) {
        if (record.record_type !== 'client') { return };
        return CUSTOMER_STATUS_MAP[record.client?.client_status]
      }
    },
    {
      key: 'client_type',
      index: 7,
      width: 180,
      filter: {
        hidden: userIsMKT,
        type: 'Cascader',
        multiple: true,
        options: CUSTOMER_COOP_TYPE,
        maxTagCount: 3
      },
      render(text, record) {
        if (record.record_type !== 'client') { return };
        return GET_COOP_BUS_TO_SIMPLIFY(record.client?.coop_bus || '')
      }
    },
    {
      key: 'attachment',
      index: 8,
      width: 200,
      ellipsis: true,
      create: {
        type: 'Component',
        span: 24,
        labelCol: { span: 4 },
        render(form, value, itemName) {
          return (
            <UploadInput
              label="attachment"
              form={form}
              itemData={tableProps.itemData}
              setItemData={tableProps.setItemData}
              isLargeMode={false}
              accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.png,.jpeg"
              onChange={(fileList) => {
                form.setFieldValue(itemName, fileList)
              }}
            />
          )
        }
      },
      update: true,
      render(attachment) {
        if (!attachment) { return null };
        const urls = attachment.split(',')
        if (urls.length === 1) {
          const name = urls[0].split('/').pop();
          return (
            <a href={urls[0]} target="_blank">
              <Tooltip title={name}>
                {name}
              </Tooltip>
            </a>
          )
        }

        return (
          <Popover content={urls.map(it => <a style={{ display: 'block' }} href={it} target="_blank">{it.split('/').pop()}</a>)}>
            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: '180px' }}>
              {urls.map(it => it.split('/').pop()).join(',')}
            </div>
          </Popover>
        )
      }
    }

  ],
    {
      Fields: {
        id: 'ID',
        data: 'Date',
        company_name: 'Company',
        content: 'Content',
        creator: 'Creator',
        utime: 'Update Time',
        client_status: 'Customer Status',
        client_type: 'Cooperation Type',
        attachment: 'Attachment',

        type: 'Type',
        key_info: 'Key Info'
      }
    }
  );

  const getCompanyName = () => {
    const oriCompanyName = props.itemData.company_name;
    const clueDetail = clueList.find(item => item.company_name === oriCompanyName);
    return `${clueDetail?.id}:${oriCompanyName}`
  }

  const handlerEditData = (record: Recordable) => {
    const newRecord = { ...record }
    newRecord.key_info = !!newRecord.key_info;
    const oriCompanyName = newRecord.company_name;
    const clueDetail = clueList.find(item => item.company_name === oriCompanyName);
    newRecord.company_name = `${clueDetail?.id}:${oriCompanyName}`;
    if (newRecord.attachment) {
      newRecord.attachment = newRecord.attachment.split(',').map((src, idx) => {
        const name = src.split('/').pop();
        return {
          url: src,
          name: name,
          uid: idx,
          status: 'done'
        }
      })
    }

    setCompanyClientInfo({
      client_status: CUSTOMER_STATUS_MAP[clueDetail?.crm_client?.client_status] || '',
      coop_bus: GET_COOP_BUS_TO_SIMPLIFY(clueDetail?.crm_client?.coop_bus || '') || ''
    })

    return newRecord
  }

  const tableProps = useFlatTable({
    title: 'Follow Up List',
    fetchUrl: '/followUpRecord',
    pageSize: 20,
    columns,
    defaultParams: {
      record_type: props.type || 'clue',
      relation_id: props.itemData.id
    },
    actionWidth: 200,

    actions: [
      FlatTableActionType.Edit,
      FlatTableActionType.Copy,
      FlatTableActionType.Delete,
    ],
    btnGroup: [
      FlatTableBtnGroupType.Create,
      BtnGroupType.Export,
    ],

    beforeCreate() {
      const companyName = getCompanyName();
      return {
        list: [
          {
            company_name: companyName,
          }
        ]
      }
    },

    async beforeHandleCreate(params) {
      return beforeHandleCreate({
        list: params.list,
        clueList
      })
    },
    async beforeCopy(record) {
      const newRecord = handlerEditData(record);
      const res: any = {
        list: [{
          company_name: newRecord.company_name,
          type: newRecord.type,
          content: newRecord.content,
          key_info: newRecord.key_info,
          attachment: newRecord.attachment
        }]
      }
      res.attachment = newRecord.attachment

      return res;
    },
    async beforeEdit(record) {

      const newRecord = handlerEditData(record)
      return newRecord
    },
    async beforeHandleUpdate(params) {
      if (!params.content) {
        message.error('content is empty');
        return {
          isSuc: false,
          values: params
        }
      }
      const [clueId, companyName] = params.company_name.split(':');
      const clueDetail = clueList.find(item => Number(item.id) === Number(clueId))!;
      if (params.attachment) {
        params.attachment = params.attachment.map(it => it.url).join(',')
      }
      params.clue_id = clueId;
      params.record_type = clueDetail.crm_client ? 'client' : 'clue'
      params.relation_id = clueDetail.crm_client ? clueDetail.crm_client.id : clueId
      params.company_name = companyName;
      return {
        isSuc: true,
        values: params
      }
    },
    async onExport(params, setExportLoading) {
      try {
        setExportLoading(true);
        const res = await request.post('/followUpRecord/export', {
          ...params,
          sheetName: 'Follow Up Record',
        }, {
          responseType: 'blob'
        });
        const a = document.createElement('a');
        a.download = 'follow_up_record.xlsx';
        a.href = window.URL.createObjectURL(res);
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(a.href);
        message.success(`Export complete`);
      } finally {
        setExportLoading(false);
      }
    },
  });

  const getClueCompany = async () => {
    const res = await request.get('/clue/getClueCompany');
    const clueList = res.data || [];
    setClueList(clueList.map((item) => ({
      ...item,
      label: `${item.id}:${item.company_name}`,
      value: `${item.id}:${item.company_name}`,
    })));
  }

  useEffect(() => {
    getClueCompany();
  }, []);

  useEffect(() => {
    tableProps.setParams({
      record_type: props.type || 'clue',
      relation_id: props.itemData.id
    })
  }, [props.itemData.id])

  useEffect(() => {
    if (props.visible) {
      tableProps.fetchData({
        ...tableProps.params,
        ...tableProps.defaultParams,
        record_type: props.type || 'clue',
        relation_id: props.itemData.id
      });
    }
  }, [props.visible])

  return (
    <Drawer width={900} style={{ zIndex: 1002 }} className='follow-up-record-drawer' title={`Follow Up Record (${props.itemData?.company_name})`} onClose={props.onClose} open={props.visible} contentWrapperStyle={{
      backgroundColor: 'rgb(247, 250, 252)'
    }}>
      <section className="business-list-wrap">
        <FlatTable {...tableProps} hiddenFilter={true} formListConfig={{
          addBtnName: 'Add More',
          default: {
            company_name: getCompanyName(),
          },
          beforeCreate: () => {
            tableProps.setItemData({
              ...tableProps.itemData,
              attachment: []
            })
          }
        }} modelWrapClassName='follow-up-record-modal' maskStyle={{
          zIndex: 1001
        }} />
      </section>
    </Drawer>
  )
}
