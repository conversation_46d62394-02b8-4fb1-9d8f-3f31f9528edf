.history-modal-container {
  .ant-modal-body {
    background-color: rgb(247, 250, 252);
  }
}

.content-diff-content {
  position: relative;

  .radioGroup {
    display: flex;
    margin: 10px 0;
    justify-content: flex-end;
    align-items: center;
    padding-top: 10px;
  }

  .name-content {
    position: absolute;
    display: inline-flex;
    justify-content: space-between;
    width: calc(100% - 40px);
    bottom: 2px;
    color: gainsboro;

    .left {
      width: 50%;
      text-align: right;
      padding-right: 10px;
    }
  }

  .content {
    background-color: rgba(255, 255, 255, 0.2);
    max-height: 660px;
    overflow-y: auto;
  }

  .color {
    color: black;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
  }
}

.normal {
  color: black;
}

.add {
  background-color: rgb(236, 253, 240);
}

.removed {
  background-color: rgb(251, 233, 235);
}

.pre {
  margin: 0;
  vertical-align: top;
  display: inline-block;
}

.button {
  margin-top: 5px;
  margin-bottom: 5px;
  text-align: center;
  color: brown;
  border: 1px solid brown;
}

.outerPre {
  width: calc(100% - 88px);
  display: inline-block;
}

.spanWidth {
  width: 20px;
  display: inline-block;
  white-space: pre-wrap;
}

.innerPre {
  margin: 0;
  word-wrap: break-word;
  word-break: brea k-all;
  white-space: normal;
}

.line {
  color: rgba(27, 31, 35, 0.3);
  width: 88px;
}

.colLeft {
  display: inline-block;
  color: #586069;
  width: 88px;
  background-color: #dbedff;
}

.splitWidth {
  width: 44px;
}

.collRight {
  display: inline-block;
  width: calc(100% - 88px);
  vertical-align: top;
}

.collRightSplit {
  width: calc(100% - 44px);
}

.colRContent {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 16px;
  background-color: #f1f8ff;
}

.octicon {
  fill: currentColor;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow:hover {
  color: #fff;
  background-color: rgb(3, 102, 214);
}

.cRHeight {
  height: 32px;
}

.sPart {
  display: inline-block;
  vertical-align: top;
  width: 50%;
}

.iBlock {
  display: inline-block;
  width: 50%;
}

.prBlock {
  width: 100%;
}

.splitLN {
  width: 44px;
  display: inline-block;
  white-space: pre-wrap;
  color: rgba(27, 31, 35, 0.3);
  font-family: "SFMono-Regular", "Liberation Mono", Menlo, Courier, monospace;
}

.splitCon {
  display: inline-block;
  white-space: pre-wrap;
  vertical-align: top;
  width: calc(100% - 44px);
}

.filled {
  width: calc(100% - 52px);
}

.lBorder {
  border-right: 1px solid #ddd;
}

.rBorder {
  border-left: 1px solid #ddd;
  position: relative;
  left: -1px;
}
