import { <PERSON>, Modal, Button } from "antd";
import * as React from "react";
import { useState, useEffect, useContext } from "react";
import { AppContext } from '@/store';
import './index.scss';
interface Props {
  content: string;   // 当前新内容
  oldContent: string; // 当前旧内容
  itemId: string; // 修改对应内容ID
  hideModal: (newIds?) => void; // 关闭弹窗
}
const SHOW_TYPE = {
  UNIFIED: 0,
  SPLITED: 1
}

const BLOCK_LENGTH = 5;
const DIFF_JS_ID = "muslim_oms_diff_js";
const RETRY_COUNT_MAX = 3; // 判断是否加载
let tryCount = 0;
/** 不同JSON 对比弹窗 */
function DiffModal(prop: Props) {
  const { hideModal, content, oldContent, itemId } = prop;
  const { localeData={} } = useContext(AppContext);
  const [diffArr, setDiffArr] = useState<any>({});
  const [lineGroup, setLineGroup] = useState<any>([]);
  const [showType, setShowType] = useState(SHOW_TYPE.SPLITED);

  useEffect(() => {
    initDiffJs();
    refreshDiffContent();
  }, []);

  /** 确认是否加载 diff JS 库 */
  function initDiffJs() {
    const diffJsDom: any = document.getElementById(DIFF_JS_ID);
    if (!diffJsDom) {
      const script: any = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `/public/lib/diff.js`;
      script.id = DIFF_JS_ID;
      document.getElementsByTagName('head')[0].appendChild(script);
    }
  }
  /** window.JsDiff 计算对比数据 */
  function refreshDiffContent() {
    if (tryCount < RETRY_COUNT_MAX) {
      // @ts-ignore
      if (content && oldContent && window.JsDiff) {
        tryCount = 0;
        try {
          const contentObj = JSON.parse(content);
          const oldContentObj = JSON.parse(oldContent);
          // @ts-ignore
          const diffJSON = new window.JsDiff.diffJson(oldContentObj, contentObj);
          setDiffArr(diffJSON);
          flashContent(diffJSON);
        } catch (error) {
          console.log(error);
        }
      } else {
        tryCount++;
        setTimeout(() => {
          refreshDiffContent();
        }, 800);
      }
    }
  }
  /** 初始化代码 */
  const flashContent = (newArr) => {
    if (typeof (newArr || diffArr) === 'string') return;
    const initLineGroup = (newArr || diffArr).map((item, index, originArr) => {
      let added, removed, value, count;
      added = item.added;
      removed = item.removed;
      value = item.value;
      count = item.count;
      const strArr = value?.split('\n').filter(item => item) || [];
      const type = (added && '+') || (removed && '-') || ' ';
      let head, hidden, tail;
      if (type !== ' ') {
        hidden = [];
        tail = [];
        head = strArr;
      } else {
        const strLength = strArr.length;
        if (strLength <= BLOCK_LENGTH * 2) {
          hidden = [];
          tail = [];
          head = strArr;
        } else {
          head = strArr.slice(0, BLOCK_LENGTH)
          hidden = strArr.slice(BLOCK_LENGTH, strLength - BLOCK_LENGTH);
          tail = strArr.slice(strLength - BLOCK_LENGTH);
        }
      }
      return {
        type,
        count,
        content: {
          hidden,
          head,
          tail
        }
      }
    });
    let lStartNum = 1;
    let rStartNum = 1;
    initLineGroup.forEach(item => {
      const { type, count } = item;
      item.leftPos = lStartNum;
      item.rightPos = rStartNum;
      lStartNum += type === '+' ? 0 : count;
      rStartNum += type === '-' ? 0 : count;
    })
    setLineGroup(initLineGroup);
  }
  const isSplit = () => {
    return showType === SHOW_TYPE.SPLITED;
  }
  /** 打开隐藏代码 */
  const openBlock = (type, index) => {
    const copyOfLG: any = lineGroup.slice();
    const targetGroup: any = copyOfLG[index];
    const { head, tail, hidden } = targetGroup.content;
    if (type === 'head') {
      targetGroup.content.head = head.concat(hidden.slice(0, BLOCK_LENGTH));
      targetGroup.content.hidden = hidden.slice(BLOCK_LENGTH);
    } else if (type === 'tail') {
      const hLenght = hidden.length;
      targetGroup.content.tail = hidden.slice(hLenght - BLOCK_LENGTH).concat(tail);
      targetGroup.content.hidden = hidden.slice(0, hLenght - BLOCK_LENGTH);
    } else {
      targetGroup.content.head = head.concat(hidden);
      targetGroup.content.hidden = [];
    }
    copyOfLG[index] = targetGroup;
    setLineGroup(copyOfLG);
  }
  /** 判断是否展示隐藏多行相似代码按钮 */
  const getHiddenBtn = (hidden, index) => {
    const isSingle = hidden.length < BLOCK_LENGTH * 2;
    return <div key='collapse' className={'cutWrapper'}>
      <div className={`colLeft ${isSplit() ? 'splitWidth' : ''}`}>
        {isSingle ? <div className="arrow" onClick={openBlock.bind(null, 'all', index)}>
          <svg className={'octicon'} viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"> <path fillRule="evenodd" d="M8.177.677l2.896 2.896a.25.25 0 01-.177.427H8.75v1.25a.75.75 0 01-1.5 0V4H5.104a.25.25 0 01-.177-.427L7.823.677a.25.25 0 01.354 0zM7.25 10.75a.75.75 0 011.5 0V12h2.146a.25.25 0 01.177.427l-2.896 2.896a.25.25 0 01-.354 0l-2.896-2.896A.25.25 0 015.104 12H7.25v-1.25zm-5-2a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5zM6 8a.75.75 0 01-.75.75h-.5a.75.75 0 010-1.5h.5A.75.75 0 016 8zm2.25.75a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5zM12 8a.75.75 0 01-.75.75h-.5a.75.75 0 010-1.5h.5A.75.75 0 0112 8zm2.25.75a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5z"></path></svg>
        </div>
          : <div>
            <div className={'arrow'} onClick={openBlock.bind(null, 'head', index)}>
              <svg className="octicon" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fillRule="evenodd" d="M8.177 14.323l2.896-2.896a.25.25 0 00-.177-.427H8.75V7.764a.75.75 0 10-1.5 0V11H5.104a.25.25 0 00-.177.427l2.896 2.896a.25.25 0 00.354 0zM2.25 5a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5zM6 4.25a.75.75 0 01-.75.75h-.5a.75.75 0 010-1.5h.5a.75.75 0 01.75.75zM8.25 5a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5zM12 4.25a.75.75 0 01-.75.75h-.5a.75.75 0 010-1.5h.5a.75.75 0 01.75.75zm2.25.75a.75.75 0 000-1.5h-.5a.75.75 0 000 1.5h.5z"></path></svg>
            </div>
            <div className={'arrow'} onClick={openBlock.bind(null, 'tail', index)}>
              <svg className={'octicon'} viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fillRule="evenodd" d="M7.823 1.677L4.927 4.573A.25.25 0 005.104 5H7.25v3.236a.75.75 0 101.5 0V5h2.146a.25.25 0 00.177-.427L8.177 1.677a.25.25 0 00-.354 0zM13.75 11a.75.75 0 000 1.5h.5a.75.75 0 000-1.5h-.5zm-3.75.75a.75.75 0 01.75-.75h.5a.75.75 0 010 1.5h-.5a.75.75 0 01-.75-.75zM7.75 11a.75.75 0 000 1.5h.5a.75.75 0 000-1.5h-.5zM4 11.75a.75.75 0 01.75-.75h.5a.75.75 0 010 1.5h-.5a.75.75 0 01-.75-.75zM1.75 11a.75.75 0 000 1.5h.5a.75.75 0 000-1.5h-.5z"></path></svg>
            </div>
          </div>
        }
      </div>
      <div className={`collRight ${isSplit() ? 'collRightSplit' : ''}`}>
        <div className={`colRContent ${isSingle ? '' : 'cRHeight'}`}>
          {`${localeData.CurrentlyHideContent}${hidden.length} ${localeData.Row}`}
        </div>
      </div>
    </div>
  }
  const getLineNum = (number) => {
    return ('     ' + number).slice(-5);
  }
  /** 获取有所差异的代码 */
  const getCombinePart = (leftPart, rightPart) => {
    const { type: lType, content: lContent, leftPos: lLeftPos, rightPos: lRightPos } = leftPart;
    const { type: rType, content: rContent, leftPos: rLeftPos, rightPos: rRightPos } = rightPart;
    const lArr = lContent?.head || [];
    const rArr = rContent?.head || [];
    const lClass = lType === '+' ? 'add' : 'removed';
    const rClass = rType === '+' ? 'add' : 'removed';
    return <div>
      <div className={'iBlock lBorder'}>{lArr.map((item, index) => {
        return <div className={`prBlock ${lClass}`} key={index}>
          {getLNPadding(lLeftPos + index)}
          {getPaddingContent('-  ' + item)}
        </div>
      })}</div>
      <div className={`iBlock ${lArr.length ? '' : 'rBorder'}`}>{rArr.map((item, index) => {
        return <div className={`prBlock ${rClass}`} key={index}>
          {getLNPadding(rRightPos + index)}
          {getPaddingContent('+  ' + item)}
        </div>
      })}</div>
    </div>
  }
  /** 获取合并一起的代码内容 */
  const getUnifiedRenderContent = () => {
    return lineGroup && lineGroup.map((item, index) => {
      const { type, content: { hidden = [] } } = item;
      const isNormal = type === ' ';
      return <div key={index}>
        {paintCode(item)}
        {hidden?.length && isNormal && getHiddenBtn(hidden, index) || null}
        {paintCode(item, false)}
      </div>
    })
  }
  const paintCode = (item, isHead = true) => {
    const { type, content: { head, tail, hidden }, leftPos, rightPos } = item;
    const isNormal = type === ' ';
    const cls = `normal ${type === '+' ? 'add' : ''} ${type === '-' ? 'removed' : ''}`;
    const space = "     ";
    return (isHead ? head : tail).map((sitem, sindex) => {
      let posMark = '';
      if (isNormal) {
        const shift = isHead ? 0 : (head.length + hidden.length);
        posMark = (space + (leftPos + shift + sindex)).slice(-5)
          + (space + (rightPos + shift + sindex)).slice(-5);
      } else {
        posMark = type === '-' ? getLineNum(leftPos + sindex) + space
          : space + getLineNum(rightPos + sindex);
      }
      return <div key={(isHead ? 'h-' : 't-') + sindex} className={cls}>
        <pre className={'pre line'}>{posMark}</pre>
        <div className={'outerPre'}>
          <div className={'splitCon'}>
            <div className={`spanWidth`}>{' ' + type + ' '}</div>
            {getPaddingContent(sitem)}
          </div>
        </div>
      </div>
    })
  }
  /** 获取分开对比代码的内容 */
  const getSplitContent = () => {
    const length = lineGroup?.length;
    const contentList: any = [];
    for (let i = 0; i < length; i++) {
      const targetBlock = lineGroup[i];
      const { type, content: { hidden = [] } } = targetBlock;
      if (type === ' ') {
        contentList.push(<div key={i}>
          {getSplitCode(targetBlock)}
          {hidden.length && getHiddenBtn(hidden, i) || null}
          {getSplitCode(targetBlock, false)}
        </div>)
      } else if (type === '-') {
        const nextTarget: any = lineGroup[i + 1] || { content: {} };
        const nextIsPlus = nextTarget && nextTarget?.type === '+';
        contentList.push(<div key={i}>
          {getCombinePart(targetBlock, nextIsPlus ? nextTarget : {})}
        </div>)
        nextIsPlus ? i = i + 1 : void 0;
      } else if (type === '+') {
        contentList.push(<div key={i}>
          {getCombinePart({}, targetBlock)}
        </div>)
      }
    }
    return <div>
      {contentList}
    </div>
  }
  //  获取split下的页码node
  const getLNPadding = (origin) => {
    const item = ('     ' + origin).slice(-5);
    return <div className={'splitLN'}>{item}</div>
  }
  //  获取split下的内容node
  const getPaddingContent = (item) => {
    return <div className={'splitCon'}>{item}</div>
  }
  /** 获取 split 的代码 */
  const getSplitCode = (targetBlock, isHead = true) => {
    const { type, content: { head, hidden, tail }, leftPos, rightPos } = targetBlock;
    return (isHead ? head : tail).map((item, index) => {
      const shift = isHead ? 0 : (head.length + hidden.length);
      return <div key={(isHead ? 'h-' : 't-') + index}>
        <div className={'iBlock lBorder'}>{getLNPadding(leftPos + shift + index)}{getPaddingContent('    ' + item)}</div>
        <div className={'iBlock'}>{getLNPadding(rightPos + shift + index)}{getPaddingContent('    ' + item)}</div>
      </div>
    })
  }

  return (
    <Modal
      width={1200}
      title={`Content Diff(${itemId})`}
      wrapClassName="filters-modal add-modal"
      visible={true}
      maskClosable={false}
      onCancel={() => { hideModal(null); }}
      onOk={() => { hideModal(null); }}
      style={{ maxWidth: '90vw' }}
    >
      <div className="content-diff-content">
        <div className={'radioGroup'}>
          <Radio.Group value={showType} size='small' onChange={(e) => setShowType(e.target.value)}>
            <Radio.Button value={SHOW_TYPE.UNIFIED}>{localeData.Unified}</Radio.Button>
            <Radio.Button value={SHOW_TYPE.SPLITED}>{localeData.Split}</Radio.Button>
          </Radio.Group>
        </div>

        <div className="content">
          {isSplit() && (
            <div className="name-content">
              <div className="left">{localeData.OldContent}</div>
              <div className="right">{localeData.CurrentContent}</div>
            </div>
          )}
          <div className="color">
            {isSplit() ? getSplitContent() : getUnifiedRenderContent()}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DiffModal;