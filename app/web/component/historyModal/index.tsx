import React, { useEffect, useState, useContext } from 'react';
import { Modal, Row, Col, Button, Tooltip } from 'antd';
import { AppContext } from '@/store';
import { useQueryList } from '@/hooks';
import { CustomTable, Filter, TableMain } from '@/component';
import { formatDate } from '@/modules/format';
import request from '@/modules/request';
import * as moment from 'moment';
import DiffPop from './diff-pop';

function HistoryModal(props: any) {
  const { localeData = {} } = useContext(AppContext);
  const [params, setParams] = useState({
    pathname: window.location.pathname
  });
  const [operatorOption, setOperatorOption] = useState([]) as any;
  const [actionOption, setActionOption] = useState([]) as any;
  const [diffPopObj, setDiffPopObj] = useState<any>({
    showPop: false,
    content: "",
    oldContent: "",
    itemId: "",
  });

  const {
    loading,
    pageIndex,
    setPageIndex,
    pageSize,
    listData,
    fetchData: fetchDataOrigin,
    setPageSize,
    total
  } = useQueryList({
    fetchUrl: '/log',
    pageSize: 14
  });

  const getFilters = async () => {
    const result = (await request.get(`/log/getFilters`, { 'X-showMessage': true })) as any;
    if (result.isSuccess) {
      const { operatorList, actionList } = result.data;
      const newOperatorOption: any = [];
      const newActionOption: any = [];
      for (const item of operatorList) {
        newOperatorOption.push({
          value: item.operator,
          label: item.operator
        });
      }
      for (const item of actionList) {
        newActionOption.push({
          value: item.action,
          label: item.action
        });
      }
      setOperatorOption(newOperatorOption);
      setActionOption(newActionOption);
    }
  };

  useEffect(() => {
    if (props.visible) {
      if (listData.length === 0) {
        getDataList();
      }
      if (operatorOption.length === 0) {
        getFilters();
      }
    }
  }, [props.visible]);

  /** 日志列表查询 */
  function getDataList(opt?: any) {
    let reqParams: any = { ...params };
    if (opt) {
      reqParams = { ...reqParams, ...opt };
    }
    if (reqParams.hasOwnProperty('ctime')) {
      const ctimeList = reqParams['ctime'];
      if (ctimeList?.length > 1) {
        reqParams.startTime = ctimeList[0].startOf('day').format("YYYY-MM-DD HH:mm:ss");
        reqParams.endTime = ctimeList[1].add(1, 'day').startOf('day').format("YYYY-MM-DD HH:mm:ss");
      }
      delete reqParams.ctime;
    }
    fetchDataOrigin(reqParams);
  }

  const handleOk = () => {
    props.setVisible(false);
  };
  const handleCancel = () => {
    props.setVisible(false);
  };
  const onTableChange = pagination => {
    if (pageSize !== pagination.pageSize) {
      setPageSize(pagination.pageSize);
      getDataList({
        pageSize: pagination.pageSize,
        pageIndex,
      });
    }
    if (pageIndex !== pagination.current) {
      setPageIndex(pagination.current);
      getDataList({
        pageSize,
        pageIndex: pagination.current,
      });
    }
  };

  const columns: any[] = [
    {
      title: localeData.ItemId,
      dataIndex: 'item_id',
      key: 'item_id',
      width: 100,
      filter: {
        type: "Input",
        placeholder: localeData.CommasTips,
      },
    },
    {
      title: localeData.Time,
      dataIndex: 'ctime',
      key: 'ctime',
      width: 160,
      render: value => formatDate(value, 'yyyy-MM-dd hh:mm:ss'),
      filter: {
        type: 'RangePicker',
        ranges: {
          [localeData.Today]: [moment(), moment()],
          [localeData.Last3Days]: [moment().add(-3, 'day'), moment().add(-1, 'day')],
          [localeData.Last7Days]: [moment().add(-7, 'day'), moment().add(-1, 'day')],
          [localeData.LastMonth]: [moment().add(-30, 'day'), moment().add(-1, 'day')],
          [localeData.Last3Month]: [moment().add(-30 * 3, 'day'), moment().add(-1, 'day')],
          [localeData.Last6Month]: [moment().add(-30 * 6, 'day'), moment().add(-1, 'day')],
        }
      },
    },
    {
      title: localeData.Operator,
      dataIndex: 'operator',
      key: 'operator',
      width: 80,
      filter: {
        type: 'Select',
        options: operatorOption
      }
    },
    {
      title: localeData.OperationType,
      dataIndex: 'action',
      key: 'action',
      width: 90,
      filter: {
        type: 'Select',
        options: actionOption,
        breakword: 1,
      }
    },
    {
      title: localeData.OldContent,
      dataIndex: 'old_record',
      key: 'old_record',
      width: 200,
      render: (text) => {
        return <Tooltip placement='top' title={text}>
          <div className="goal-text-overflow" style={{ width: '200px' }}>{text}</div>
        </Tooltip>
      }
    },
    {
      title: localeData.CurrentContent,
      dataIndex: 'record',
      key: 'record',
      width: 200,
      render: (text) => {
        return <Tooltip placement='top' title={text}>
          <div className="goal-text-overflow" style={{ width: '200px' }}>{text}</div>
        </Tooltip>
      }
    },
    {
      title: localeData.Operator,
      dataIndex: 'ip',
      key: 'ip',
      width: 100,
      render: (text, record) => {
        return (<Button
          type='link'
          onClick={() => {
            setDiffPopObj({
              showPop: true,
              content: record.record,
              oldContent: record.old_record,
              itemId: record.item_id,
            });
          }}
        >
          {localeData.showContentDiff}
        </Button>);
      }
    }
  ];
  const modalWidth = document.body.clientWidth * 0.85;

  return (
    <>
      <Modal title={localeData.OperationRecord} wrapClassName='history-modal-container' open={props.visible} onOk={handleOk} onCancel={handleCancel} width={modalWidth} centered={true} footer={null} style={{ maxWidth: '90vw' }} {...props}>
        <Row style={{}}>
          <Col span={24}>
            <Filter
              columns={columns}
              filterColumnNum={4}
              onChange={(values: any, type: string) => {
                setParams(values);
                setPageIndex(1);
                let reqParams: any = {
                  pageSize,
                  pageIndex: 1,
                  ...values
                };
                if (type === 'reset') {
                  reqParams = {
                    item_id: '',
                    ctime: [],
                    operator: '',
                    action: '',
                    pageSize,
                    pageIndex: 1,
                  };
                }
                getDataList(reqParams);
              }}
            />
          </Col>
        </Row>
        <TableMain
          title={localeData.OperationLogList}
          btnGroup={<></>}
          table={
            <CustomTable
              columns={columns}
              dataSource={listData}
              loading={loading}
              scroll={{ x: 'max-content', y: '500px' }}
              pagination={{ total, current: pageIndex, defaultPageSize: pageSize }}
              onChange={onTableChange}

            />}
        />
      </Modal>
      {
        diffPopObj.showPop ? (
          <DiffPop
            hideModal={() => {
              setDiffPopObj({
                showPop: false,
                content: "",
                oldContent: "",
                itemId: "",
              });
            }}
            content={diffPopObj.content}
            oldContent={diffPopObj.oldContent}
            itemId={diffPopObj.itemId}
          />
        ) : null
      }
    </>
  );
}

export default HistoryModal;
