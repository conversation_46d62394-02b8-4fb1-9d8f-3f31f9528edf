import { AUTH_MAP } from '@/router/routeMap';
import React, { FunctionComponent } from 'react';
import { KeepAlive } from 'react-activation';

export default function BasePageHoc(PageComponent: FunctionComponent) {

  return function Component(props) {
    const location = props.location;
    const isKeepAlive = AUTH_MAP[location.pathname]?.keepAlive
    if (!isKeepAlive) {
      return <PageComponent {...props} />
    }
    return (

      <KeepAlive cacheKey={location.pathname} name={location.pathname}>
        <PageComponent {...props} />
      </KeepAlive>
    )
  }

}
