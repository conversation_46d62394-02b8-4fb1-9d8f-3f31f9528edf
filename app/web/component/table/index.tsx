import React, { useContext } from 'react';
import { Table, Tooltip } from 'antd';
import { AppContext } from '@/store';
import { __ } from '@/modules/format';

function CustomTable(props) {
  const { localeData } = useContext(AppContext);

  const { columns, pagination = {}, ...tableParams } = props;
  const { showTotal } = pagination;
  const paginationProps = { ...pagination };
  // if (typeof hideOnSinglePage !== 'boolean') {
  //   paginationProps.hideOnSinglePage = true;
  // }
  // 默认展示条数
  if (typeof showTotal !== 'function') {
    paginationProps.showTotal = (total) => __('TotalItems', total.toString());
  }
  // 关闭
  if (showTotal === false) {
    delete paginationProps.showTotal;
  }
  const listColumns = columns.filter((item: any) => {
    if (!item.hidden) {
      // 展示一行
      item.ellipsis = true;
      if (!item.noShowTdTitle) {
        // todo 有render需要再处理
        if (!item.render) {
          item.render = text => {
            return (
              <Tooltip placement="topLeft" title={text}>
                {text}
              </Tooltip>
            );
          };
        }
      }

      // 操作表头居中 和 序号默认居中
      if ((item.title === localeData.Operate && !item.align) || (item.title === localeData.SerialNumber && !item.align)) {
        item.align = 'center';
      }
    }
    return !item.hidden;
  });

  return <Table {...tableParams} pagination={paginationProps} columns={listColumns} scroll={{ y: tableParams?.scroll?.y || '62vh' }} />;
}

export default CustomTable;
