import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { noop } from '@@/lib/tool';

const { confirm } = Modal;

export default function confirmModal(data: any, onOk, options?) {
  const { title, onCancel, content } = options || { title: '删除操作', onCancel: noop, content: data.name || '' };
  confirm({
    title,
    icon: <ExclamationCircleOutlined />,
    content,
    okType: title.indexOf('删除') > -1 || title.indexOf('Delete') > -1 ? 'danger' : 'primary',
    onOk() {
      if (typeof onOk === 'function') {
        onOk(data);
      }
    },
    onCancel() {
      if (typeof onCancel === 'function') {
        onCancel();
      }
    }
  });
}
