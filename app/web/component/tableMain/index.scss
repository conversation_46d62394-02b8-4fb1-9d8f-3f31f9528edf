.flat-table-main {
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
  background-color: #fff;
  border-color: rgba(239, 239, 245, 1);
  padding: 12px 16px !important;
  flex: 1 1 0%;
  margin-bottom: 0 !important;

  .ant-btn {
    height: 28px !important;
  }

  .create-btn,
  .ant-btn-primary {
    border-color: var(--ant-primary-color);
    color: var(--ant-primary-color);
    background-color: transparent;

    &:hover {
      background-color: inherit;
      color: var(--ant-primary-color);
    }
  }

  // .remove-btn {
  //   color: var();
  // }
}
