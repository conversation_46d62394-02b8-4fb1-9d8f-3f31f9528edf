import React from 'react';
import { Divider } from 'antd';
import './index.scss'

interface Props {
  btnGroup: React.ReactElement; // 列表的按钮
  table: React.ReactElement; // table
  title: string; // 标题
}

function TableMain(props: Props) {
  const { btnGroup, table, title } = props;
  return (
    <section className="section-item flat-table-main">
      <div className="operation-bar">
        <h2>{title || ''}</h2>
        {btnGroup}
      </div>
      {table}
    </section>
  );
}

export default TableMain;
