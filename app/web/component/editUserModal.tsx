import React, { useEffect, useState, useContext } from 'react';
import { useModal } from '@/hooks';
import request from '@/modules/request';
import { Form, Input, Modal, Row, Col, Select, Switch } from 'antd';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min.js';
import { deduplicateArray, isPasswordValid } from '@@/lib/tool';
import { __ } from '@/modules/format';

import { AppContext } from '@/store';
import { CONTRACT_TYPE, userIsBDLeader, userIsOPLeader, userIsProgrammaticOp } from '@@/lib/constant';
import useAppStore from '@/store/app.store';
import useSelector from '@/hooks/useSelector';

interface BaseDataItem {
  text: string;
  value: number | string;
}

interface ModalProps {
  title: string;
  name: string;
  onSuccess?: (data: any) => void;
  adsUserList: ICommonRecord[]
}

const validatePhone = (_, value) => {
  if (!value) {
    return Promise.resolve();
  }
  if (!/^1\d{10}$/.test(value)) {
    return Promise.reject('Please enter the correct phone number!');
  }
  return Promise.resolve();
};

function EditModal({ title, name, onSuccess, adsUserList }: ModalProps) {
  const { BD_USERS, OP_USERS, PROGRAMMATIC_OP_USERS } = useAppStore(useSelector(['BD_USERS', 'OP_USERS', 'PROGRAMMATIC_OP_USERS']));

  const { visible, setVisible, type, emit, data, confirmLoading, setConfirmLoading } = useModal();
  const [ruleForm, setRuleForm] = useState<any>({});
  const [form] = Form.useForm();

  const [code, setCode] = useState('');
  const [encrypt, setEncrypt] = useState<any>();
  const { publicKey, localeData } = useContext(AppContext);
  const allowApprovalTypeWatch = Form.useWatch('crm_audit_is_open', form);
  // 钉钉openId的特征
  const dingFeature = 'iEiE';
  let isBdLeader = false;
  let isOpLeader = false;
  let isProgrammaticOp = false;
  if (data) {
    const roles = data.crm_oms_user_roles;
    const roleCode = roles.map(it => it.role_code)
    isBdLeader = userIsBDLeader({
      roleCode
    });
    isOpLeader = userIsOPLeader({
      roleCode
    });
    isProgrammaticOp = userIsProgrammaticOp({
      roleCode
    })
  }
  // 提供外部访问
  EditModal.prototype.open = emit;

  useEffect(() => {
    if (data) {
      const { password, ...otherData } = data;
      setRuleForm(otherData);
      setCode(otherData.id);
      form.setFieldsValue(otherData);
    } else {
      setCode('');
      setRuleForm({});
      form.resetFields();
    }
  }, [data]);

  useEffect(() => {
    if (Object.keys(ruleForm).length === 0) {
      form.resetFields();
    }
  }, [ruleForm]);

  useEffect(() => {
    const enc = new JSEncrypt();
    enc.setPublicKey(publicKey);
    setEncrypt(enc);

  }, []);

  useEffect(() => {

    if (!visible) {
      setTimeout(() => {
        setRuleForm({});
      }, 300)
    }
  }, [visible])

  async function handleOk() {
    const validData = await form.validateFields().catch(console.error);
    let result: any = null;
    if (validData) {
      if (validData.bd_member) {
        validData.bd_member = validData.bd_member.join(',');
      }
      if (validData.op_member) {
        validData.op_member = validData.op_member.join(',');
      }
      if (validData.crm_audit_is_open) {
        validData.crm_audit_type = validData.crm_audit_type.join(',');
      } else {
        validData.crm_audit_type = ''
      }
      setConfirmLoading(true);

      const url = type === 'add' ? '/omsUser' : `/${name}/${data.id}`;
      const method = type === 'add' ? 'post' : 'put';
      if (validData.password) {
        validData.password = encrypt.encrypt(validData.password);
        if (type === 'add') {
          validData.option = 'create';
        }
      } else {
        delete validData.password;
      }
      // @ts-ignore
      result = await request[method](url, validData, { 'X-showMessage': true }).catch(err => {
        console.error(err);
        setConfirmLoading(false);
      });
      if (result) {
        setVisible(false);
        if (onSuccess) {
          onSuccess(type === 'add' ? result.data : { id: data.id, ...validData });
        }
      }
      setConfirmLoading(false);
    }
  }

  /**
   * 获取弹层标题
   */
  function getModalTitle() {
    const typeName = title;
    let actionName = localeData.Edit;
    switch (type) {
      case 'add':
        actionName = localeData.Create;
        break;
      case 'edit':
        actionName = localeData.Edit;
        break;
      case 'copy':
        actionName = localeData.Copy;
        break;
      default:
        break;
    }
    return `${actionName} ${typeName}${code ? ` - ${code}` : ''}`;
  }

  function handleValuesChange(values) {
    setRuleForm(Object.assign({}, ruleForm, values));
  }

  return (
    visible ? (
      <Modal
        width={900}
        getContainer={false}
        title={getModalTitle()}
        open={visible}
        onOk={handleOk}
        okText={'Confirm'}
        confirmLoading={confirmLoading}
        wrapClassName="edit-modal-wrap"
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form form={form} labelCol={{ span: 9 }} className="login-form" onValuesChange={handleValuesChange}>
          <Row >
            <Col span={12}>
              <Form.Item name="pub_name" label={'User Name'} rules={[{ required: true, message: __('Please', 'Input') }]}>
                <Input placeholder={__('Please', 'Input')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="password"
                label={'Password'}
                rules={[
                  { required: type === 'add', message: __('Input') },
                  { min: 8, max: 20, message: 'length limit 8 - 20' }
                ]}
              >
                <Input type="password" placeholder={__('Please', 'Input')} />
              </Form.Item>
            </Col>
          </Row>
          <Row >
            <Col span={12}>
              <Form.Item rules={[
                { required: true, type: 'email' }
              ]} name="email" label={'Email'}>
                <Input placeholder="email" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Mobile" name="mobile" rules={[
                { required: true, message: 'please input mobile' },
                { validator: validatePhone }
              ]}>
                <Input placeholder="Used for notification on Dingtalk" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Allow approval type"  >
                <Form.Item name="crm_audit_is_open" valuePropName='checked'>
                  <Switch />
                </Form.Item>
                {
                  allowApprovalTypeWatch && (
                    <Form.Item name="crm_audit_type">
                      <Select mode='multiple' allowClear options={[{ label: 'All', value: 'all' }, ...CONTRACT_TYPE]} onChange={(value: string[]) => {
                        if (value.includes('all')) {
                          form.setFieldsValue({ crm_audit_type: CONTRACT_TYPE.map(it => it.value) });
                        }

                      }}></Select>
                    </Form.Item>
                  )
                }

              </Form.Item>
            </Col>
          </Row>

          <Row >
            {
              isBdLeader && (
                <Col span={12}>
                  <Form.Item label="BD Member" name="bd_member">
                    <Select
                      optionFilterProp="label"
                      showSearch
                      mode="multiple"
                      placeholder="Please select BD Member"
                      // 过滤掉自己
                      options={BD_USERS.filter(it => Number(it.value) !== Number(data?.id))}
                    >
                    </Select>
                  </Form.Item>
                </Col>
              )
            }
          </Row>

          <Row >
            {
              isOpLeader && (
                <Col span={12}>
                  <Form.Item label="OP Member" name="op_member">
                    <Select
                      optionFilterProp="label"
                      showSearch
                      mode="multiple"
                      placeholder="Please select OP Member"
                      // 过滤掉自己
                      options={deduplicateArray([...OP_USERS, ...PROGRAMMATIC_OP_USERS], 'value').filter(it => Number(it.value) !== Number(data?.id))}
                    >
                    </Select>
                  </Form.Item>
                </Col>
              )
            }
          </Row>

          <Row>
            {
              isProgrammaticOp && (
                <Col span={12}>
                  <Form.Item label="Ads User" name="ads_user" rules={[{ required: true }]}>
                    <Select
                      optionFilterProp="label"
                      showSearch
                      placeholder="Please select Ads User"
                      options={adsUserList}
                    >

                    </Select>
                  </Form.Item>
                </Col>
              )
            }
          </Row>

        </Form>
      </Modal>
    ) : null
  );
}

export default EditModal;
