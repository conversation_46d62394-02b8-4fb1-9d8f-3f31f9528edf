import { PropsWith<PERSON>hildren, ReactChildren, ReactNode, useContext, useLayoutEffect, useMemo, useRef } from 'react';
import useUserStore from '../../../store/user.store';
import { Tooltip } from 'antd';
import React from 'react';
import { AppContext } from '@/store';
import { isPureObject } from '@@/lib/tool';

export type OperateAccessProps = PropsWithChildren<{
  operate: string[],
  errorTips?: string;
  fallbackWay?: 'disable' | 'unmount',
  locationPath?: string
}>;

export interface OperateAccessFactory {
  (props: OperateAccessProps): JSX.Element;
  Create: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Read: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Update: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Destroy: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Assign: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Export: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
  Transfer: (props: Omit<OperateAccessProps, 'operate'>) => JSX.Element;
}

const OperateAccess: OperateAccessFactory = (props: OperateAccessProps) => {
  const { localeData } = useContext(AppContext);

  const {
    fallbackWay = 'disable',
    operate,
    errorTips = localeData.AccessErrorTips,
    children
  } = props;

  const pathnameRef = useRef<string>('');
  const routeOperateAuths = useUserStore(state => state.routeOperateAuths);
  const renderNode = useMemo(() => {
    // if (!pathnameRef.current) return null;
    const operateAuths = routeOperateAuths.get(props.locationPath || window.location.pathname) || [];
    const hasAuth = operate.length > 0 ? operate.every(i => operateAuths.includes((i))) : false;

    if (!hasAuth) {
      if (fallbackWay === 'disable') {
        const childrenEl = children as JSX.Element;
        if (isPureObject(childrenEl.props.children)) {
          childrenEl.props.children.props.disabled = true;
        }
        // 处理被Antd Tooltips包裹的元素
        if (Reflect.has(childrenEl.props, 'destroyTooltipOnHide')) {
          childrenEl.props = {
            ...childrenEl.props,
            open: false
          };
          childrenEl.props.children.props = {
            ...childrenEl.props.children.props,
            disabled: true,
          };
        } else {
          childrenEl.props = {
            ...childrenEl.props,
            disabled: true
          };
        }
        return (
          <Tooltip title={errorTips} arrowPointAtCenter={true} showArrow={false}>
            {children}
          </Tooltip>
        );
      } else {
        return null;
      }
    }
    return children;

  }, [operate, fallbackWay, children, routeOperateAuths]);

  useLayoutEffect(() => {
    pathnameRef.current = props.locationPath || window.location.pathname;
  }, []);

  return <>
    {renderNode}
  </>;
};

OperateAccess.Create = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['C']} {...restProps}>
      {children}
    </OperateAccess>
  );
};

OperateAccess.Read = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['R']} {...restProps}>
      {children}
    </OperateAccess>
  );
};
OperateAccess.Export = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['E']} {...restProps}>
      {children}
    </OperateAccess>
  );
};

OperateAccess.Update = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['U']} {...restProps}>
      {children}
    </OperateAccess>
  );
};

OperateAccess.Destroy = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['D']} {...restProps}>
      {children}
    </OperateAccess>
  );
};
OperateAccess.Assign = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['P']} {...restProps}>
      {children}
    </OperateAccess>
  );
};

OperateAccess.Transfer = (props: Omit<OperateAccessProps, 'operate'>) => {
  const { children, ...restProps } = props;
  return (
    <OperateAccess operate={['T']} {...restProps}>
      {children}
    </OperateAccess>
  );
};

export default OperateAccess;
