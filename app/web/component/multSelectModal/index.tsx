import React, { useEffect, useState } from 'react';
import { Input, Modal, Button, Tooltip, message } from 'antd';
import {
  SearchOutlined, DeleteOutlined, CheckOutlined
} from '@ant-design/icons';
import './index.scss';

const { TextArea } = Input;

function MultSelectModal({visible, setVisible, title, data, submitColumn}) {
  const [ columnList, setColumnList ] = useState<any[]>([]); // 可选字段列表
  const [ filterColumnList, setFilterColumnList ] = useState<any[]>([]); // 筛选后字段列表（当前显示列表）
  const [ selectedList, setSelectedList ] = useState<any[]>([]); // 已选字段列表
  const [ showCustomPop, setShowCustomPop ] = useState(false); // 显示编辑自定义字段弹层
  const [ inputCustom, setInputCustom ] = useState(false); // 自定义字段弹层 输入状态
  const [ customVal, setCustomVal ] = useState(''); // 自定义字段信息

  useEffect(() => {
    if (visible) {
      const { records, value } = data;
      const newSelectedList: any = [];
      // console.log('data:', data);
      if (value && value.length > 0) {
        for (let i = 0; i < value.length; i++) {
          const val = value[i];
          let getSuccess = false;
          for (let j = 0; j < records.length; j++) {
            const recordData = records[j];
            if (Object.is(val, recordData.value)) {
              getSuccess = true;
              newSelectedList.push(recordData);
              break;
            }
          }
          if (!getSuccess) {
            if (!Object.is(val, undefined)) {
              newSelectedList.push({value: val, text: val});
            }
          }
        }
      }
      setSelectedList(newSelectedList);
      setColumnList(records);
      setFilterColumnList(records);
    }
  }, [visible]);

  async function handleOk() {
    const { column } = data;
    const newVals = selectedList.map((sData) => {
      return sData.value;
    });
    submitColumn({ column, newVals });
    setVisible(false);
  }
  /**
   * 更新选中内容
   * @param txt
   */
  function updateSelectedList(columnData: {text: string, value: string}, hasSelected: boolean) {
    let newSelectedList = [...selectedList];
    const { text, value } = columnData;
    if (!hasSelected) {
      if (!selectedListHasVal(value)) {
        newSelectedList.push({text, value});
      }
    } else {
      newSelectedList = newSelectedList.filter((selectData) => {
        const { value: sVal } = selectData;
        return (value !== sVal);
      });
    }
    setSelectedList(newSelectedList);
  }
  /**
   * 选中列表中是否包含指定值
   * @param value
   */
  function selectedListHasVal(value) {
    let hasVal = false;
    for (let i = 0; i < selectedList.length; i++) {
      const listItem = selectedList[i];
      const { value: listVal } = listItem;
      if (value === listVal) {
        hasVal = true;
        break;
      }
    }
    return  hasVal;
  }

  /**
   * 获取已添加列表项
   * @param selectedList
   */
  function getSelectedDom(selectedList) {
    let selectedDom: any = null;
    if (selectedList.length > 0) {
      selectedDom = selectedList.map((columnData, sIndex) => {
        return (<li
          className="list-s-item"
          key={sIndex}
        >
          <Tooltip
            mouseEnterDelay={1}
            title={columnData.text}
            placement="top"
          >
            {columnData.text}
          </Tooltip>
          <DeleteOutlined onClick={() => {
            let newSelectedList = [...selectedList];
            newSelectedList = newSelectedList.filter((sData, fIndex) => {
              return (fIndex !== sIndex);
            });
            setSelectedList(newSelectedList);
          }}/>
        </li>);
      });
    } else {
      selectedDom = (<li className="c-c-selected-tips">请从左侧列表选择添加或快捷输入</li>);
    }
    return selectedDom;
  }
  /**
   * 添加左侧全部字段值
   */
  function setAllLeft() {
    const newSelectedList = [...selectedList];
    for (let i = 0; i < filterColumnList.length; i++) {
      const columnData = filterColumnList[i];
      const { text, value } = columnData;
      if (!selectedListHasVal(value)) {
        newSelectedList.push({text, value});
      }
    }
    setSelectedList(newSelectedList);
  }
  /**
   * 获取输入的自定义字段信息
   */
  function submitCustomColumn() {
    if (!customVal) {
      message.warn('请输入字段信息！');
    } else {
      const newSelectedList = [...selectedList];
      let valList = customVal.split('\n');
      valList = Array.from(new Set(valList)); // 去重处理
      if (valList.length > 500) {
        valList = valList.slice(0, 500);
      }
      for (let i = 0; i < valList.length; i++) {
        const val = valList[i];
        if (val && !selectedListHasVal(val)) {
          newSelectedList.push({value: val, text: val});
        }
      }
      setSelectedList(newSelectedList);
      setShowCustomPop(false);
    }
  }

  return (
    <Modal
      getContainer={false}
      mask={false}
      title={`${title ? title : ''} 多选条件编辑`}
      visible={visible}
      width={800}
      wrapClassName="bi-common-pop edit-search-column-modal"
      onOk={handleOk}
      onCancel={() => {
        setVisible(false);
      }}
    >
      {
        data ? (<div className="column-content">
          <div className="c-c-l">
            <div className="c-c-top">
              <Input
                className="c-c-input"
                prefix={<SearchOutlined />}
                placeholder="输入名称搜索"
                onChange={(e) => {
                  const newVal = e.target.value;
                  let newFilterColumnList: any = [];
                  if (!newVal) {
                    newFilterColumnList = [...columnList];
                  } else {
                    newFilterColumnList = columnList.filter((columnData) => {
                      let isTarget = false;
                      const { text } = columnData;
                      if (text) {
                        isTarget = text.toLocaleLowerCase().includes(newVal.toLocaleLowerCase());
                      }
                      return isTarget;
                    });
                  }
                  setFilterColumnList(newFilterColumnList);
                }}
              />
            </div>
            <ul className="c-c-list">
              {
                filterColumnList.map((columnData, fIndex) => {
                  let hasSelected = false;
                  hasSelected = selectedListHasVal(columnData.value);
                  return (<li
                    className="list-f-item"
                    key={fIndex}
                    onClick={updateSelectedList.bind(null, columnData, hasSelected)}
                  >
                    { hasSelected ? <CheckOutlined /> : null}
                    <Tooltip
                      mouseEnterDelay={1}
                      title={columnData.text}
                      placement="top"
                    >
                      {columnData.text}
                    </Tooltip>
                  </li>);
                })
              }
            </ul>
            <div className="c-c-bottom">
              <Button className="c-c-all-btn" onClick={setAllLeft.bind(null)}>添加左侧全部字段值</Button>
            </div>
          </div>
          <div className="c-c-hr"></div>
          <div className="c-c-r">
            <div className="c-c-top">
              <div className="c-c-custom-input">
                <div>已添加({selectedList.length})</div>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setCustomVal('');
                    setInputCustom(false);
                    setShowCustomPop(true);
                  }}
                >
                    快捷输入
                </Button>
              </div>
            </div>
            <ul className="c-c-list">
              {
                getSelectedDom(selectedList)
              }
            </ul>
            <div className="c-c-bottom">
              <div className="c-c-custom-input">
              <div>已添加{selectedList.length}条</div>
                <Button type="primary" size="small" onClick={() => { setSelectedList([]); }}>清空</Button>
              </div>
            </div>
          </div>
        </div>
        ) : (
        <div>
          there are not data;
        </div>)
      }
      {
        showCustomPop && (<div className="custom-column-pop">
          <div
            className="c-c-p-top"
            onClick={() => {
              setInputCustom(true);
            }}
          >
            {
              inputCustom ? (
              <TextArea
                value={customVal}
                onChange={(e) => {
                  const newVal = e.target.value;
                  setCustomVal(newVal);
                }}
                rows={17} />
              ) : (
              <div className="custom-tips">
                <div>请一行填一个</div>
                <div>最多添加500个</div>
                <div>(识别录入时会自动过滤重复选型和已经添加过的选项)</div>
              </div>)
            }
          </div>
          <div className="c-c-p-bottom">
            <Button className="c-c-p-btn" onClick={() => { setShowCustomPop(false); }}>取消</Button>
            <Button type="primary" className="c-c-p-btn" onClick={submitCustomColumn.bind(null)}>识别并添加</Button>
          </div>
        </div>)
      }
    </Modal>
  );
}

export default MultSelectModal;
