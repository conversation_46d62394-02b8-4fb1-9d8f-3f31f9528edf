.edit-search-column-modal {
  text-align: left;

  .column-content {
    display: flex;
    justify-content: space-around;
  }

  // .c-c-input {
  //   border-left: 0px;
  //   border-right: 0px;
  // }
  .c-c-hr {
    width: 1%;
    height: 430px;
    border-right: 1px solid #f0f0f0;
  }

  .c-c-l {
    width: 49.5%;
    box-sizing: border-box;
    padding-right: 5px;
  }

  .c-c-top {
    height: 32px;

    .c-c-custom-input {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 7px;
    }
  }

  .c-c-bottom {
    height: 32px;

    .c-c-custom-input {
      display: flex;
      justify-content: space-between;
      border-top: 1px solid #f0f0f0;
      padding-top: 7px;
    }
  }

  .c-c-list {
    width: 100%;
    height: 350px;
    overflow-y: scroll;
    padding-left: 10px;
    padding-top: 10px;

    .list-s-item,
    .list-f-item {
      display: block;
      position: relative;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #333;
      white-space: nowrap;
      list-style: none;
      width: 100%;
      cursor: pointer;

      &:hover {
        background-color: #f8f8fa;
        color: #2153d4;
        opacity: 1;
      }
    }

    .list-s-item {
      justify-content: space-between;
      display: flex;
      align-items: center;
      padding: 0 15px;
    }

    .list-f-item {
      padding-left: 25px;

      .anticon-check {
        color: #2f66ff;
        position: absolute;
        top: 7px;
        left: 2px;
      }
    }

    .list-f-item:hover::after {
      content: '+';
      position: absolute;
      top: 0;
      right: 8px;
    }
  }

  .c-c-selected-tips {
    line-height: 350px;
    width: 100%;
    display: block;
    text-align: center;
  }

  .c-c-all-btn {
    width: 100%;
  }

  .c-c-r {
    width: 49.5%;
    box-sizing: border-box;
    padding-left: 20px;
  }

  .custom-column-pop {
    position: absolute;
    width: 250px;
    height: 450px;
    top: 20px;
    right: -30px;
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    z-index: 10;

    .custom-tips {
      width: 100%;
      text-align: center;
      padding: 160px 20px 0;
      line-height: 1.8em;
    }
  }

  .c-c-p-top {
    width: 100%;
    height: 400px;
  }

  .c-c-p-bottom {
    width: 100%;
    height: 50px;
    text-align: center;
  }

  .c-c-p-btn:first-child {
    margin-right: 20px;
  }
}
