import React, { useContext, useState, useEffect, useImperativeHandle } from 'react';
import {
  Form, Row, Col, Input, Button, Select, DatePicker, Divider,
  Space, Tooltip,
} from 'antd';
import { AppContext } from '@/store';
import { PlusOutlined, MinusOutlined, LoadingOutlined, CloseCircleOutlined } from '@ant-design/icons';
import request from '@/modules/request';
import MultSelectModal from '@/component/multSelectModal/index';
import './index.scss';

const { RangePicker } = DatePicker;
const defaultFilterColumnNum = 4;

/** 通用筛选组件2 - FBI 风格 */
function FilterMult(props, ref) {
  const { localeData } = useContext(AppContext);
  const filterColumnNum = props.filterColumnNum || defaultFilterColumnNum;
  const { handleSpread, hideSpread, centered } = props;
  const filterColumns = [] as any;
  const associatedColumns = [] as any; // 关联关键列更新 联动更新的列
  const initialValues = {} as any;
  const [spread, setSpread] = useState<any>({});
  const [loadingMap, setLoadingMap] = useState<any>({}); // 下拉选框，单级 loading 标识变量
  const [listMap, setListMap] = useState<any>({}); // 下拉选框，列表数据
  const [showEditPop, setShowEditPop] = useState(false); // 是否显示编辑弹层
  const [editPopObj, setEditPopObj] = useState<any>({ records: [], value: '', column: '', columnChineseName: '' });
  const [titleMap, setTitleMap] = useState<any>({});

  props.columns.map(item => {
    const itemDataIndex = item.dataIndex;
    if (item.filter && item.filter.type) {
      const newItem = Object.assign({}, item);
      newItem.title = item.filter.title || item.title;
      newItem.required = item.filter.required || false;
      newItem.mode = item.filter.mode || null;
      filterColumns.push(newItem);
    }
    // 两类型: 日期选择、下拉选择
    if (item.filter) {
      if (item.filter.type === 'Select') {
        if (item.filter.defaultValue) {
          initialValues[itemDataIndex] = item.filter.defaultValue;
        } else {
          initialValues[itemDataIndex] = undefined;
        }
        if (itemDataIndex !== props.associatedColumns) {
          associatedColumns.push(itemDataIndex);
        }
      } else if (item.filter.type === 'RangePicker') {
        initialValues[itemDataIndex] = item.filter.defaultValue;
      }
    }
  });
  useEffect(() => {
    // 初始化列 对应名称 map
    const titleMap: any = {};
    props.columns.forEach((item: any) => {
      if (item.filter && item.filter.type) {
        const dataIndex = item.dataIndex;
        const title = item.filter.title || item.title;
        titleMap[dataIndex] = title;
      }
    });
    setTitleMap(titleMap);
  }, []);
  useEffect(() => {
    const newAssociatedValue = props.associatedValue;
    if (newAssociatedValue) {
      form.setFieldsValue({[props.associatedColumns]: newAssociatedValue});
    }
  }, [props.associatedValue]);

  const [form] = Form.useForm();
  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    updateListMap: newObj => {
      updateListMap(newObj);
    },
    setFieldsValue: newObj => {
      form.setFieldsValue(newObj);
    }
  }));
  const renderFormItem = colum => {
    const dataIndex = colum.dataIndex;
    const columnTmp = {...colum.filter};
    switch (colum.filter.type) {
      case 'Input':
        columnTmp.labelstyle = colum.filter.labelStyle;
        columnTmp.valuestyle = colum.filter.valueStyle;
        delete columnTmp.labelStyle;
        delete columnTmp.valueStyle;
        const InputProps = {
          placeholder: colum.title,
          ...columnTmp
        };
        return (
          <Form.Item
            className="filter-bi-form-item"
            name={dataIndex}
            rules={[
              {
                required: colum.required,
                message: `Input ${colum.title}!`
              }
            ]}
          >
            <>
              <div className="item-label" style={columnTmp.labelstyle || {}}>{colum.title}:</div>
              <Input style={columnTmp.valuestyle || {}} {...InputProps} />
            </>
          </Form.Item>
        );
      case 'Select':
        let speadDom: any = null;
        const disabled = colum.filter.disabled;
        const defaultValue = colum.filter.defaultValue;
        const textVals = form.getFieldValue(dataIndex);
        if (spread[dataIndex]) {
          if (disabled) {
            speadDom = (
              <MinusOutlined/>
            );
          } else {
            speadDom = (
              <Tooltip title={localeData.CollapsedColumn}>
                <MinusOutlined
                  onClick={() => {
                    const speadObj = { key: dataIndex, spead: false };
                    updateSpread({[dataIndex]: false});
                    handleSpread(speadObj);
                  }}
                />
              </Tooltip>
            );
          }
        } else {
          if (disabled) {
            speadDom = (<PlusOutlined/>);
          } else {
            speadDom = (
              <Tooltip title={localeData.ExpandColumn}>
                <PlusOutlined
                  onClick={() => {
                    const speadObj = { key: dataIndex, spead: true };
                    updateSpread({[dataIndex]: true});
                    handleSpread(speadObj);
                  }}
                />
              </Tooltip>
            );
          }
        }
        let suffixDom: any = (<span />);
        if (disabled) {
          suffixDom = <span />;
        } else if (loadingMap[dataIndex]) {
          suffixDom = <LoadingOutlined />;
        } else if (textVals) {
          suffixDom = (
            <CloseCircleOutlined
              onClick={async function() {
                form.setFieldsValue({[dataIndex]: undefined});
                updateLoadingMap({ [dataIndex]: false });
                // 如果是联动列 刷新所有列表
                // if (dataIndex === props.associatedColumns) {
                //   await refreshColumnList('');
                // }
              }}
              style={{ cursor: 'pointer', color: 'rgba(0,0,0,.45)' }}
            />
          );
        }
        columnTmp.labelstyle = colum.filter.labelStyle;
        columnTmp.valuestyle = colum.filter.valueStyle;
        delete columnTmp.labelStyle;
        delete columnTmp.valueStyle;
        const SelectProps: any = {
          // className: 'ant-select-field',
          className: !hideSpread ? 'ant-select-field' : 'ant-select-field-hidden',
          readOnly: true,
          value: textVals,
          placeholder: localeData.SelectMult,
          suffix: suffixDom,
          onClick: () => {
            if (!listMap[dataIndex]) {
              updateLoadingMap({ [dataIndex]: true });
              getSelectList({column: dataIndex}, form);
            } else {
              showEditColumnPop({ column: dataIndex });
            }
          },
          ...columnTmp
        };
        const spreadBtn = !hideSpread ? <div className={disabled ? 'select-tail disabled-icon' : 'select-tail'}>{speadDom}</div> : null;
        return (
          <Form.Item className="filter-bi-form-item" key={dataIndex} name={dataIndex} rules={[{ required: colum.required }]}>
            <>
              <div className="item-label" style={columnTmp.labelstyle || {}}>{colum.title}:</div>
              <Input style={columnTmp.valuestyle || {}} {...SelectProps} />
              {spreadBtn}
            </>
          </Form.Item>
        );
      case 'RangePicker':
        columnTmp.labelstyle = colum.filter.labelStyle;
        columnTmp.valuestyle = colum.filter.valueStyle;
        delete columnTmp.labelStyle;
        delete columnTmp.valueStyle;
        const DateProps = {
          onChange: (time) => {
            form.setFieldsValue({[dataIndex]: time});
          },
          ...columnTmp
        };
        return (
          <Form.Item className="filter-bi-form-item" key={dataIndex} name={dataIndex}>
            <>
              <div className="item-label" style={columnTmp.labelstyle || {}}>{colum.title}:</div>
              <RangePicker
                className="ant-picker-input"
                style={columnTmp.valuestyle || {}}
                {...DateProps}
              />
            </>
          </Form.Item>
        );
        break;
      default:
        break;
    }
  };
  const renderFields = () => {
    const count = filterColumns.length;
    const children = [] as any;
    let spanNum = Math.floor(24 / filterColumnNum);
    for (let i = 0; i < count; i++) {
      if (filterColumns[i]?.filter?.span) {
        spanNum = filterColumns[i].filter.span;
      }
      children.push(
        <Col span={spanNum} key={i}>
          {renderFormItem(filterColumns[i])}
        </Col>
      );
    }
    return children;
  };
  const onFinish = values => {
    props.onChange(values);
  };
  /** 更新展开折叠变量 */
  function updateSpread(newObj: any) {
    const newEntity: any = { ...spread, ...newObj };
    setSpread(newEntity);
  }
  /** 更新loading...弹层显示变量 */
  function updateLoadingMap(newObj: any) {
    const newEntity: any = { ...loadingMap, ...newObj };
    setLoadingMap(newEntity);
  }
  /** 更新筛选项数据列表 */
  function updateListMap(newObj: any) {
    const newEntity: any = { ...listMap, ...newObj };
    setListMap(newEntity);
  }
  /** 异步请求，获取下拉选项数据 */
  async function getSelectList(opt, form) {
    const { column } = opt;
    const { associatedColumns, columnListUrl } = props;
    const params: any = { column };
    if (associatedColumns && associatedColumns !== column) {
      let associateVal = form.getFieldValue(associatedColumns);
      if (associateVal && Array.isArray(associateVal)) {
        associateVal = associateVal.join(',');
      }
      params[associatedColumns] = associateVal;
    }
    if (props.onBeforeGetSelectList) {
      Object.assign(params, await props.onBeforeGetSelectList(Object.assign({}, params), column, form));
    }
    const columnListRes: any = await request.post(columnListUrl, params);
    const { list } = columnListRes;
    updateListMap({ [column]: list });
    updateLoadingMap({ [column]: false });
    showEditColumnPop({ column, list });
    if (props.onAfterGetSelectList) {
      props.onAfterGetSelectList(column, form);
    }
  }
  /** 显示多选筛选项弹层 */
  function showEditColumnPop(opt) {
    const { column, list } = opt;
    const records = [];
    if (props.onHandlePopList) {
      records.push.apply(records, props.onHandlePopList(column, list, form));
    } else {
      let listData: any = [];
      if (list) {
        listData = list;
      } else {
        listData = listMap[column];
      }
      records.push.apply(records, listData.map((item: string) => {
        return {
          value: item,
          text: item,
        };
      }));
    }

    let value = form.getFieldValue(column);
    const columnChineseName = titleMap[column]; // 获取列名称
    if (!Array.isArray(value) && value?.split) {
      value = value.split(',');
    }
    const newEditPopObj = { ...editPopObj, records, value, column, columnChineseName };
    setEditPopObj(newEditPopObj);
    setShowEditPop(true);
  }
  /** 更新多选条件 */
  async function updateColumn(opt: { column: string; newVals: any[] }) {
    const { column, newVals } = opt;
    let textVals = null as any;
    if (newVals?.length > 0) {
      textVals = newVals.join(',');
    }
    form.setFieldsValue({[column]: textVals});
    if (column === props.associatedColumns) {
      await refreshColumnList(textVals);
    }
  }
  /** 联动列更新，所有列表项更新 */
  async function refreshColumnList(textVals) {
    const newListMap = { ...listMap };
    const params: any = {};
    params[props.associatedColumns] = textVals;
    for (const assColumn of associatedColumns) {
      params.column = assColumn;
      updateLoadingMap({ [assColumn]: true });
      const columnListRes: any = await request.get('/mediabuy/putData/columnList', params);
      const { list } = columnListRes;
      newListMap[assColumn] = list;
      updateLoadingMap({ [assColumn]: false });
    }
    updateListMap(newListMap);
  }

  return (
    <Form
      form={form}
      initialValues={initialValues}
      name="advanced_search"
      className="ant-advanced-search-form section-item section-bi-filter"
      onFinish={onFinish}
      style={{ paddingBottom: '4px' }}
    >
      <div className="operation-bar">
        <h2>{localeData.FiterStr}</h2>
        <Space>
          <Button type="primary" htmlType="submit">
            {localeData.Query}
          </Button>
        </Space>
      </div>
      <Divider />
      <Row gutter={24} style={{ marginTop: '15px' }}>{renderFields()}</Row>
      {showEditPop && <MultSelectModal visible={showEditPop} setVisible={setShowEditPop} title={editPopObj?.columnChineseName || ''} data={editPopObj} submitColumn={updateColumn} />}
    </Form>
  );
}

export default React.forwardRef(FilterMult);
