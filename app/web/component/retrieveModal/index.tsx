import React, { useContext } from 'react';
import { Modal, Form, Image, Tooltip, Space, Button, Input, Row, Col } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { groupByKey } from '@@/lib/tool';
import { __ } from '@/modules/format';
import { AppContext } from '@/store';
import { RightOutlined, LeftOutlined } from '@ant-design/icons';
import Avatar from 'antd/lib/avatar/avatar';
import './index.scss';
const { TextArea } = Input;
const isFalsy = (value: unknown) => (value === 0 ? false : !value);

const defaultLayout = {
  labelCol: { span: 4 }
};

const defaultNext = {
  enable: false,
  previousVisible: true,
  nextVisible: true,
  previousClick: () => { },
  nextClick: () => { }
};

const taillayout = {
  wrapperCol: {
    offset: 0,
    span: 24
  }
};

function RetrieveModal(props: any) {
  if (!props.visible) {
    return null;
  }
  let filterColumns = [] as any;
  const { localeData } = useContext(AppContext);
  props.columns.map(item => {
    if (item.retrieve && item.retrieve.type) {
      const newItem = Object.assign({}, item);
      newItem.title = item.retrieve.title || item.title;
      newItem.required = item.retrieve.required || false;
      newItem.mode = item.retrieve.mode || null;
      newItem.group = item.retrieve.group || '';
      filterColumns.push(newItem);
    }
  });
  filterColumns = filterColumns.sort((a, b) => a.retrieve.index - b.retrieve.index);
  filterColumns = groupByKey(filterColumns, 'group');

  const [form] = Form.useForm();

  const handleOk = () => {
    props.setVisible(false);
  };

  const handleCancel = () => {
    props.setVisible(false);
  };

  const renderFormItem = colum => {
    let label = <span style={{ fontWeight: 'normal' }}>{colum.title}</span>;
    if (colum.retrieve.tooltip) {
      label = (
        <>
          <span style={{ fontWeight: 'bold' }}>{colum.title}</span>&nbsp;
          <Tooltip title={colum.retrieve.tooltip}>
            <QuestionCircleOutlined />
          </Tooltip>
        </>
      );
    }
    const labelCol = colum.retrieve.labelCol
    switch (colum.retrieve.type) {
      case 'Text':
        const TextProps = {
          ...colum.retrieve
        };
        let text = props.itemData[colum.dataIndex];
        if (colum.retrieve.options) {
          for (const item of colum.retrieve.options) {
            if (item.value === text) {
              text = item.label;
              break;
            }
          }
        }
        if (colum.retrieve.render && typeof colum.retrieve.render === 'function') {
          return (
            <Form.Item key={colum.dataIndex} label={colum.title} labelCol={labelCol}>
              {colum.retrieve.render(props.itemData)}
            </Form.Item>
          );
        }
        return (
          <Form.Item key={colum.dataIndex} label={label}>
            <span {...TextProps}>{isFalsy(text) ? '-' : text}</span>
          </Form.Item>
        );
      case 'Image':
        const ImageProps = {
          src: props.itemData[colum.dataIndex],
          ...colum.retrieve
        };
        return (
          <Form.Item labelCol={labelCol} key={colum.dataIndex} label={label}>
            <Image {...ImageProps} />
          </Form.Item>
        );
      case 'Avatar':
        const AvatarImageProps = {
          src: props.itemData[colum.dataIndex],
          ...colum.retrieve
        };
        return (
          <Form.Item labelCol={labelCol} key={colum.dataIndex} label={label}>
            <Avatar src={<Image  {...AvatarImageProps} />} />
          </Form.Item>
        );
      case 'TextArea':
        const TextAreaProps = {
          placeholder: `${__('Please', 'Input')}${colum.title}`,
          autoSize: { minRows: 3 },
          style: { width: '100%', position: 'relative', border: 'none' },
          disabled: true,
          ...colum.retrieve
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.retrieve.placeholder
              }
            ]}
          >
            <TextArea {...TextAreaProps} style={{ border: '1px solid #d9d9d9', padding: '6px 10px', borderRadius: '4px' }} />
          </Form.Item>
        );
      case 'Component':
        return (
          <Form.Item
            colon={colum.retrieve?.colon === 'false' ? false : true}
            {...taillayout}
            wrapperCol={colum.retrieve?.wrapperCol || { offset: 0, span: 24 }}
            labelCol={labelCol}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={colum.title ? label : null}
          >
            {colum.retrieve.render(props.itemData)}
          </Form.Item>
        );
      default:
        break;
    }
  };

  const renderFields = list => {
    const children: any = [];
    for (let i = 0; i < list.length; i++) {
      const spanNum = list[i].retrieve.span || 12;
      let colStyle = {};
      if (i % 2 !== 0 && !list[i].retrieve.span && spanNum === 24) {
        colStyle = {
          paddingLeft: 0,
          paddingRight: '24px'
        };
      }
      children.push(
        <Col span={spanNum} key={i} style={colStyle}>
          {renderFormItem(list[i])}
        </Col>
      );
    }
    return children;
  };
  const ModalProps = {
    title: localeData.Detail,
    open: props.visible,
    onOk: handleOk,
    onCancel: handleCancel,
    width: 900,
    centered: true,
    footer: null,
    ...props
  };
  Reflect.deleteProperty(ModalProps, 'visible');
  const layout = props.layout || defaultLayout;
  const next = Object.prototype.toString.call(props?.next) === '[object Object]' ? { ...defaultNext, ...props?.next } : null;
  const footerButtons = props.footerButtons;
  const formComponent = (
    <Form {...layout} form={form} initialValues={props.itemData} name="control-hooks">
      <div className='handler-modal-container'>
        {filterColumns.map(groupItem => {
          return (
            <div key={groupItem.group} className="group-section group-section-retrieve">
              {groupItem.group ? <div className="group-title">{groupItem.group}</div> : null}
              <div className="group-content">
                <Row gutter={24}>{renderFields(groupItem.data)}</Row>
              </div>
            </div>
          );
        })}
      </div>
      <Form.Item {...taillayout} style={{ textAlign: 'right', marginBottom: '0', paddingTop: '8px', paddingBottom: '4px' }}>
        <Space size="middle" className='handler-modal-btns'>
          {footerButtons || (
            <Button type="primary" onClick={handleOk}>
              {localeData.Close}
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  );

  return (
    <>
      <Modal {...ModalProps}>
        {next ? (
          <Row align={'middle'}>
            <Col span={2} style={{ textAlign: 'center' }}>
              {next.previousVisible ? <LeftOutlined style={{ fontSize: 40, opacity: 0.5 }} onClick={() => next?.previousClick(props?.itemData)} /> : null}
            </Col>
            <Col span={20}>{formComponent}</Col>
            <Col span={2} style={{ textAlign: 'center', opacity: 0.5 }}>
              {next.nextVisible ? <RightOutlined style={{ fontSize: 40, textAlign: 'center' }} onClick={() => next?.nextClick(props?.itemData)} /> : null}
            </Col>
          </Row>
        ) : (
          formComponent
        )}
      </Modal>
    </>
  );
}

export default RetrieveModal;
