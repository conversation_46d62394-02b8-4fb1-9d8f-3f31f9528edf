import React, { memo } from 'react';
import { Tabs } from 'antd'
import { LogoutOutlined, LockOutlined, MehOutlined, FullscreenOutlined, UsbOutlined, FullscreenExitOutlined, PartitionOutlined, TeamOutlined } from '@ant-design/icons';

import { useHistory } from 'react-router-dom';
import useRouterStore from '@/store/router.store';
import useSelector from '../../hooks/useSelector';
import { AUTH_MAP } from '@/router/routeMap';
import { isBrowser } from '@@/lib/tool';


function TabItem({ label, icon, index = 0 }) {
  return (
    <div className='tabs-svg-container'>
      <svg className="size-full svg-item" style={{ zIndex: index }}><defs><symbol id="geometry-left" viewBox="0 0 214 36"><path d="M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z"></path></symbol><symbol id="geometry-right" viewBox="0 0 214 36"><use xlinkHref="#geometry-left"></use></symbol><clipPath><rect width="100%" height="100%" x="0"></rect></clipPath></defs><svg width="51%" height="100%"><use xlinkHref="#geometry-left" width="214" height="36" fill="currentColor"></use></svg><g transform="scale(-1, 1)"><svg width="51%" height="100%" x="-100%" y="0"><use xlinkHref="#geometry-right" width="214" height="36" fill="currentColor"></use></svg></g></svg>

      <div className='tabs-detail'>
        {icon}
        <span className='tabs-detail-text'>{label}</span>
      </div>
    </div>
  )
}



export default memo(function NavTabbar() {
  const history = useHistory();
  const { routeHistory, deleteRouteHistory } = useRouterStore(
    useSelector(['routeHistory', 'deleteRouteHistory'])
  );
  const activeKey = isBrowser() ? window.location.pathname + window.location.search + window.location.hash : '';
  console.log('23131', routeHistory)
  const items = routeHistory.map((route, index) => ({
    label: <TabItem label={route.name} icon={AUTH_MAP[route.path]?.icon || <LockOutlined />} index={index} />,
    key: route.path,
  }));

  const handleChange = (key: string) => {
    history.push(key);
  };

  const handleEdit = (key, action) => {
    if (action === 'remove') {
      deleteRouteHistory(key);
      // 删除时卸载对应的缓存组件
      const { pathname } = new URL(key, window.location.origin);
      // keepAliveController.dropScope(pathname);
      // 当删除当前页时，自动跳转到浏览历史的最后一项
      if (pathname === activeKey) {
        const lastRoute = routeHistory[routeHistory.length - 1];
        lastRoute && history.push(lastRoute.path);
      }
    }
  };

  return (
    <Tabs
      hideAdd
      activeKey={activeKey}
      type="editable-card"
      onChange={handleChange}
      onEdit={handleEdit}
      items={items}
    ></Tabs>
  )
})
