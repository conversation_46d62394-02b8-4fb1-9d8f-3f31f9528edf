import React, { useImperative<PERSON><PERSON>le, useState, useContext, forwardRef } from 'react';
import { Modal, Form, Upload, Input, Button, Select, Space, Tooltip, DatePicker, TreeSelect, InputNumber, Row, Col, Radio, TimePicker, Switch, Drawer, AutoComplete, Cascader, Checkbox, Mentions } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { groupByKey, rangePickerRangesNotToday } from '@@/lib/tool';
import { AppContext } from '@/store';
import { __ } from '@/modules/format';
import './index.scss';
import Iconfont from '../iconfont';
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker: TimeRangePicker } = TimePicker;

const defaultLayout = {
  labelCol: { span: 6 }
};

const taillayout = {
  wrapperCol: {
    offset: 0,
    span: 24
  }
};

function UpdateModal(props: any, ref) {
  if (!props.visible) {
    return null;
  }
  const { localeData } = useContext(AppContext);
  let filterColumns = [] as any;
  props.columns.forEach(item => {
    if (item.update) {
      if (item.update === true && item?.create) {
        item.update = Object.assign({}, item?.create);
      }
      const newItem = Object.assign({}, item);
      newItem.title = item.update.title || item.title;
      for (let key in item.update.formItemProps) {
        if (Object.prototype.hasOwnProperty.call(item.update.formItemProps, key)) {
          if (!item.update.formItemProps?.[key]) { continue }
          newItem[key] = item.update.formItemProps[key] || null;
        }
      }

      newItem.group = item?.update?.group || '';
      filterColumns.push(newItem);
    }
  });
  // filterColumns = filterColumns.sort((a, b) => a.update.index - b.update.index);
  filterColumns = groupByKey(filterColumns, 'group', 'update');
  filterColumns.forEach(it => {
    it.data.sort((a, b) => a.update.index - b.update.index);
  })

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // 父组件通过
  useImperativeHandle(ref, () => ({
    // changeVal 就是暴露给父组件的方法
    setForm: newVal => {
      form.setFieldsValue(newVal);
    },
    form
  }));
  const onFinish = async values => {
    const tempObj = {};
    const cloneValues = { ...values };
    for (const key in values) {
      if (typeof values[key] === 'undefined') {
        tempObj[key] = '';
      }
    }

    const allColumns = filterColumns.map(it => it.data).flat(2);
    const transformDate = allColumns.filter(it => it?.mapToData);
    if (transformDate?.length) {
      transformDate.forEach(it => {
        const [startKey, endKey, format] = it.mapToData;
        const dateItem = cloneValues[it.key];
        if (dateItem) {
          cloneValues[startKey] = dateItem[0].format(format || 'YYYY-MM-DD');
          cloneValues[endKey] = dateItem[1].format(format || 'YYYY-MM-DD');
        }

        Reflect.deleteProperty(cloneValues, it.key);

      })
    }
    setLoading(true);
    try {
      await props.onChange(Object.assign({}, cloneValues, tempObj));
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  };

  const handleOk = () => {
    props.setVisible(false);
  };

  const handleCancel = () => {
    props.setVisible(false);
  };

  const onReset = () => {
    console.log('reset');
    // form.resetFields();
  };
  const renderFormItem = colum => {
    let labelStyle: any = { fontWeight: 'normal', display: 'flex', alignItems: 'center' };
    if (colum.update.breakword) {
      labelStyle = { fontWeight: 'normal', whiteSpace: 'break-spaces', lineHeight: '1.2em', textAlign: 'left', display: 'flex', alignItems: 'center' };
    }
    let label = <span style={labelStyle}>{colum.title}</span>;
    if (colum.update.tooltip) {
      label = (
        <div style={labelStyle}>
          {colum.title}&nbsp;
          <Tooltip title={colum.update.tooltip}>
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      );
    }

    const taillayout = colum.update.taillayout || {};
    const colon = colum.update.colon === 'false' ? false : true;
    const hidden = colum.update.hidden;
    const options = colum.update.options || [];

    const labelCol = colum.update.labelCol;

    const isDisabled = colum.update.disabled || props.disabled || false;
    let rules: any = [
      {
        required: colum.required,
        message: colum.update.placeholder || `${localeData.Input}${colum.title}`
      }
    ];

    switch (colum.update.type) {
      case 'Title':
        return (
          <Form.Item className='modal-title-type' required={colum.required} label={label} labelCol={labelCol} rules={rules}></Form.Item>
        )
      case 'Text':
        const TextProps = {
          ...colum.update
        };
        let text = props.itemData[colum.dataIndex];
        if (colum.update.options) {
          for (const item of colum.update.options) {
            if (item.value === text) {
              text = item.label;
              break;
            }
          }
        }
        if (colum.update.render && typeof colum.update.render === 'function') {
          return (
            <Form.Item {...taillayout} key={colum.dataIndex} labelCol={labelCol} name={colum.dataIndex} label={colum.title}>
              {colum.update.render(form, props.itemData, colum.dataIndex)}
            </Form.Item>
          );
        }
        return (
          <Form.Item {...taillayout} key={colum.dataIndex} labelCol={labelCol} name={colum.dataIndex} label={label}>
            <span {...TextProps}>{text}</span>
          </Form.Item>
        );
      case 'Input':
        const InputProps = {
          onPressEnter: e => e.preventDefault(),
          placeholder: isDisabled ? '' : `${__('Please', 'Input')}${colum.title}`,
          // 默认事件： 失去焦点移除前后空格，移除事件可上层重置该方法
          onBlur: (e: any) => {
            const value = e.target.value;
            const newVal = value.trim();
            if (newVal !== value) {
              form.setFieldsValue({ [colum.dataIndex]: newVal });
            }
          },
          ...colum.update,
          onChange: value => {
            if (colum.update.onChange) {
              colum.update.onChange(value, form);
            }
          }
        };
        if (colum.update.inputType === 'password') {
          InputProps.type = 'password';
        }

        if (colum.update.rules) {
          rules = rules.concat(colum.update.rules);
        }
        const iptItemValue = props.itemData[colum.dataIndex];

        return (
          <Tooltip title={isDisabled ? iptItemValue : null} placement="topLeft">
            <Form.Item {...taillayout} key={colum.dataIndex} labelCol={labelCol} name={colum.dataIndex} label={label} rules={rules} extra={colum.extra}>
              <Input {...InputProps} />
            </Form.Item>
          </Tooltip>

        );
      case 'InputNumber':
        const InputNumberProps = {
          onPressEnter: e => e.preventDefault(),
          ...colum.update
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            <InputNumber {...InputNumberProps} />
          </Form.Item>
        );
      case 'Select':
        const SelectProps = {
          mode: colum.mode,
          optionFilterProp: 'label',
          showArrow: true,
          allowClear: true,
          showSearch: true,
          placeholder: isDisabled ? '' : `${__('Please', 'Select')} ${colum.title}`,
          ...colum.update,
          onChange: value => {
            if (colum.update.onChange) {
              colum.update.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum?.update?.onSearch) {
              colum?.update?.onSearch(value, form);
            }
          },
          onSelect: value => {
            if (colum.update.onSelect) {
              colum.update.onSelect(value, form);
            }
          },
          onDeselect: value => {
            if (colum.update.onDeselect) {
              colum.update.onDeselect(value, form);
            }
          }
        };
        const selectOriValue = props.itemData[colum.dataIndex];
        const options = colum.update.options || [];
        const selectItemValue = options.find(item => item.value === selectOriValue)?.label || '';
        return (
          <Tooltip title={isDisabled ? selectItemValue : null} placement="topLeft">
            <Form.Item
              {...taillayout}
              key={colum.dataIndex}
              name={colum.dataIndex}
              label={label}
              labelCol={labelCol}
              rules={[
                {
                  required: colum.required,
                  message: colum.update.placeholder || `${__('Please', 'Select')} ${colum.title}`
                }
              ]}
            >
              <Select {...SelectProps}>
                {options.map(item => {
                  return (
                    <Option key={item.value} value={item.value} label={item.label}>
                      {item.label}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Tooltip>
        );
      case 'Cascader':
        const cascaderOptions = {
          disabled: colum.disabled || false,
          mode: colum.mode, // 多选：multiple
          optionFilterProp: 'label',
          showArrow: true,
          allowClear: true,
          showSearch: true,
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          ...colum.update,
          onChange: value => {
            if (colum.update.onChange) {
              colum.update.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.update.onSearch) {
              colum.update.onSearch(value, form);
            }
          },
        };
        const cascaderOriValue = props.itemData[colum.dataIndex]
        const cascaderOptionsData = colum.update.options || [];
        let cascaderValue: any = [];
        if (cascaderOriValue && Array.isArray(cascaderOriValue)) {
          cascaderOriValue.reduce((prev, next) => {
            if (!prev) { return }
            const item = prev.find(item => item.value === next);
            if (item) {
              cascaderValue.push(item.label);
            }
            return item?.children
          }, cascaderOptionsData)
        }
        cascaderValue = cascaderValue.join(' / ')
        return (
          <Tooltip title={isDisabled ? cascaderValue : null} placement="topLeft">
            <Form.Item
              {...taillayout}
              key={colum.dataIndex}
              name={colum.dataIndex}
              label={label}
              labelCol={labelCol}

              // validateTrigger={['onChange', 'onSearch']}
              rules={[
                {
                  required: colum.required,
                  message: colum.update.placeholder || `${__('Please', 'Select')} ${colum.title}`
                }
              ]}
            >
              <Cascader {...cascaderOptions} >
              </Cascader>
            </Form.Item>
          </Tooltip>

        );
      case 'AutoComplete':
        const autoCompleteProps = {
          placeholder: isDisabled ? '' : colum.title,
          allowClear: true,
          disabled: isDisabled,
          filterOption: (inputValue, option) => {
            return option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          },
          ...colum.create
        };
        if (colum.create.rules) {
          rules = rules.concat(colum.create.rules);
        }
        return (
          <Form.Item
            name={colum.dataIndex}
            label={label}
            extra={colum.extra}
            rules={rules}
          >
            <AutoComplete {...autoCompleteProps} />
          </Form.Item>
        );
      case 'TextArea':
        const TextAreaStyle = {
          width: '100%',
          position: 'relative',
          border: '1px solid #d9d9d9',
          padding: '6px 10px',
          borderRadius: '6px'
        };
        if (colum.update.style) {
          Object.assign(TextAreaStyle, colum.update.style);
        }
        const TextAreaProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Input')}${colum.title}`,
          autoSize: colum.update.style ? false : { minRows: 3 },
          style: TextAreaStyle,
          spellCheck: false,
          ...colum.update
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            <TextArea {...TextAreaProps} />
          </Form.Item>
        );
      case 'DatePicker':
        const DatePickerProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          showTime: true,
          style: { width: '100%' },
          ...colum.update
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <DatePicker {...DatePickerProps} />
          </Form.Item>
        );
      case 'RangePicker':
        const { RangePicker } = DatePicker;
        const RangePickerProps = {
          showTime: false,
          style: { width: '100%' },
          ranges: rangePickerRangesNotToday,
          ...colum.update
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <RangePicker {...RangePickerProps} />
          </Form.Item>
        );
      case 'TimeRangePicker':
        const timeRangePickerProps = {
          showTime: true,
          style: { width: '100%' },
          ...colum.create
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <TimeRangePicker {...timeRangePickerProps} />
          </Form.Item>
        );
      case 'TreeSelect':
        const TreeSelectProps = {
          placeholder: isDisabled ? '' : `${__('Please', 'Select')}${colum.title}`,
          showSearch: true,
          filterTreeNode: 'title',
          style: { width: '100%' },
          dropdownStyle: { maxHeight: 400, overflow: 'auto' },
          allowClear: true,
          ...colum.update,
          onChange: value => {
            if (colum.update.onChange) {
              colum.update.onChange(value, form);
            }
          }
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <TreeSelect {...TreeSelectProps} />
          </Form.Item>
        );
      case 'Radio':
        const radioOptions = colum.create.options || [];
        const radioGroupProps = {
          disabled: colum.disabled || false,
          name: colum.dataIndex,
          ...colum.create,
          onChange: value => {
            if (colum.create.onChange) {
              colum.create.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.create.onSearch) {
              colum.create.onSearch(value, form);
            }
          }
        };
        return (
          <Form.Item
            {...taillayout}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            labelCol={labelCol}
            // validateTrigger={['onChange', 'onSearch']}
            rules={[
              {
                required: colum.required,
                message: colum.create.placeholder || `${__('Please', 'Select')}${colum.title}`
              }
            ]}
          >
            <Radio.Group {...radioGroupProps}>
              {radioOptions.map(item => (
                <Radio key={item.value} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        );
      case 'Switch':
        if (hidden) {
          return null;
        }
        const SwitchProps = {
          ...colum.create
        };
        return (
          <Form.Item key={colum.dataIndex} name={colum.dataIndex} label={label} labelCol={labelCol} valuePropName="checked">
            <Switch {...SwitchProps} />
          </Form.Item>
        );
      case 'Checkbox':
        if (hidden) {
          return null;
        }
        const checkProps = {
          ...colum.create
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={colum.dataIndex}
            labelCol={labelCol}
            valuePropName="checked">
            <Checkbox {...checkProps}>
              {label}
            </Checkbox>
          </Form.Item>
        );
      case 'Component':
        if (colum.update?.pureComponent) {
          return (
            colum.update.render(form, props.itemData[colum.key], colum.dataIndex)
          )
        }
        return (
          <Form.Item
            {...taillayout}
            wrapperCol={colum.update?.wrapperCol || { offset: 0, span: 24 }}
            labelCol={colum.update?.labelCol || { span: 3 }}
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
            rules={[
              {
                required: colum.required,
                message: colum.update.placeholder || `${localeData.Input}${colum.title}`
              }
            ]}
          >
            {colum.update.render(form, props.itemData, colum.dataIndex)}
          </Form.Item>
        );
      default:
        break;
    }
  };

  const renderFields = list => {
    const children: any = [];
    for (let i = 0; i < list.length; i++) {
      const hidden = list[i].update.hidden;
      if (hidden) {
        continue
      }
      const spanNum = list[i].update.span || 12;
      let colStyle = {};
      if (i % 2 !== 0 && !list[i].update.span) {
        colStyle = {
          paddingLeft: 0,
          paddingRight: '24px'
        };
      }
      children.push(
        <Col span={spanNum} key={i} style={colStyle}>
          {renderFormItem(list[i])}
        </Col>
      );
    }
    return children;
  };

  // 自定义底部按钮
  let footerBtns: any = null;
  if (props.footerBtns) {
    footerBtns = props.footerBtns;
    // delete props.footerBtns;
  }

  const ModalProps = {
    title: props.title || localeData.Edit,
    open: props.visible,
    onOk: handleOk,
    onCancel: handleCancel,
    onClose: handleCancel,
    footer: null,
    centered: true,
    width: 900,
    ...props
  };
  Reflect.deleteProperty(ModalProps, 'visible');

  const layout = props.layout || defaultLayout;
  let footerBtnDom: any = null;
  const modalType = props.modalType


  if (footerBtns) {
    footerBtnDom = (
      <Space size="middle">
        {footerBtns.map((item: any, index: number) => {
          const { type, label, onBtnClick } = item;
          let btnDom: any = null;
          switch (type) {
            case 'confirm':
              btnDom = (
                <Button key="f_confirm_btn" id="f_confirm_btn" loading={loading} type="primary" htmlType="submit" onClick={async () => {
                  if (modalType !== 'drawer') { return };
                  const values = await form.validateFields();
                  onFinish(values);
                }}>
                  {label}
                </Button>
              );
              break;
            case 'cancel':
              btnDom = (
                <Button key="f_cancel_btn" id="f_cancel_btn" htmlType="button" onClick={handleCancel}>
                  {label}
                </Button>
              );
              break;
            case 'custom':
              btnDom = (
                <Button
                  key={`f_custom_btn_${index}`}
                  id={`f_custom_btn_${index}`}
                  loading={loading}
                  htmlType="button"
                  type="primary"
                  onClick={() => {
                    if (onBtnClick) {
                      onBtnClick();
                    }
                  }}
                >
                  {label}
                </Button>
              );
              break;
            default:
              break;
          }
          return btnDom;
        })}
      </Space>
    );
  } else {
    footerBtnDom = (
      <Space size="middle" className='handler-modal-btns'>
        <Button htmlType="button" onClick={handleCancel}>
          {localeData.Cancel}
        </Button>
        {
          !props.isHiddenConfirm && (
            <Button loading={loading} disabled={props.submitDisabled} type="primary" htmlType="submit" onClick={async () => {
              if (modalType !== 'drawer') { return };
              try {
                const values = await form.validateFields();
                onFinish(values);
              } catch (err: any) {
                const names = err?.errorFields?.[0]?.name;
                if (!names) { return };
                form.scrollToField(names, {
                  behavior: 'smooth',
                  block: 'center'
                })
              }
            }}>
              {props.confirmText || localeData.Confirm}
            </Button>
          )
        }
        {
          props.customBtnCreate && <>{props.customBtnCreate('update', form, async (coverValues: Recordable = {}) => {
            try {
              const values = await form.validateFields();
              return onFinish({
                ...values,
                ...coverValues
              });
            } catch (err: any) {
              console.log('我报错了吗', err)
              const names = err?.errorFields?.[0]?.name;
              if (!names) { return };
              form.scrollToField(names, {
                behavior: 'smooth',
                block: 'center'
              })
            }
          }, loading)}</>

        }
      </Space>
    );
  };
  if (props.modalType === 'drawer') {
    return (
      <Drawer {...ModalProps} keyboard={false} className={` ${props.chattingRecordsRender ? 'drawer-container-need-chat' : ''} `} maskClosable={false} style={{ top: 0 }} footer={props.disabled ? null : footerBtnDom} footerStyle={{ textAlign: 'right' }} >
        <div className='drawer-container'>
          <div className='drawer-container-form'>
            <Form {...layout} disabled={props.disabled || false} colon={false} form={form} initialValues={props.itemData} name="control-hooks" onFinish={onFinish}>
              <div>
                {filterColumns.map(groupItem => {
                  return (
                    <div key={groupItem.group} className="group-section">
                      {groupItem.group ? <div className="group-title">{groupItem.group}</div> : null}
                      <div className="group-content">
                        <Row gutter={24}>{renderFields(groupItem.data)}</Row>
                      </div>
                    </div>
                  );
                })}
                {
                  props.customRenderFormContent && props.customRenderFormContent('update', form)
                }
              </div>
            </Form>
          </div>
          {
            props.chattingRecordsRender && (
              <div className='drawer-container-chat'>
                {props.chattingRecordsRender('update', form)}
              </div>
            )
          }
        </div>


      </Drawer>
    )
  };
  return (
    <>
      <Modal {...ModalProps} wrapClassName={props.modelWrapClassName} maskStyle={props.maskStyle}>
        <Form {...layout} colon={false} initialValues={props.itemData} form={form} name="control-hooks" onFinish={onFinish}>
          <div className='handler-modal-container'>
            {filterColumns.map(groupItem => {
              return (
                <div key={groupItem.group} className="group-section">
                  {groupItem.group ? <div className="group-title">{groupItem.group}</div> : null}
                  <div className="group-content">
                    <Row gutter={24}>{renderFields(groupItem.data)}</Row>
                  </div>
                </div>
              );
            })}
            {
              props.customRenderFormContent && props.customRenderFormContent('update', form)
            }
          </div>
          {
            props.disabled ? null : (
              <Form.Item {...taillayout} style={{ textAlign: 'right', marginBottom: '0', paddingTop: '8px', paddingBottom: '4px' }}>
                {footerBtnDom}
              </Form.Item>
            )
          }

        </Form>
      </Modal>
    </>
  );
}

export default forwardRef(UpdateModal);
