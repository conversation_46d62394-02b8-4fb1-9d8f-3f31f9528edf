import React, { useEffect, useState, useContext } from 'react';
import { useModal } from '@/hooks';
import request from '@/modules/request';
import { Form, Input, Modal, Row, Col } from 'antd';
import { UploadOutlined, LoadingOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import { __ } from '@/modules/format';

interface BaseDataItem {
  text: string;
  value: number | string;
}

interface ModalProps {
  title: string;
  name: string;
  onSuccess?: (data: any) => void;
}

function EditModal({ title, name, onSuccess }: ModalProps) {
  const { visible, setVisible, type, emit, data, confirmLoading, setConfirmLoading } = useModal();
  const [ruleForm, setRuleForm] = useState<any>({});
  const [form] = Form.useForm();
  const [code, setCode] = useState('');
  const {
    BaseData: { authType },
    localeData
  } = useContext(AppContext);

  // 提供外部访问
  EditModal.prototype.open = emit;

  useEffect(() => {
    if (data) {
      setRuleForm(data);
      setCode(data.code);
      form.setFieldsValue(data);
    } else {
      setCode('');
      setRuleForm({});
    }
  }, [data]);

  useEffect(() => {
    if (Object.keys(ruleForm).length === 0) {
      form.resetFields();
    }
  }, [ruleForm]);

  async function handleOk() {
    const validData = await form.validateFields().catch(console.error);
    let result: any = null;
    if (validData) {
      setConfirmLoading(true);
      const url = type === 'add' ? `/${name}` : `/${name}/${data.id}`;
      const method = type === 'add' ? 'post' : 'put';
      validData.parent = validData.parent || '0';

      // @ts-ignore
      result = await request[method](url, validData, { 'X-showMessage': true }).catch(err => {
        console.error(err);
        setConfirmLoading(false);
      });
      if (result) {
        setVisible(false);
        if (onSuccess) {
          onSuccess(type === 'add' ? result.data : { id: data.id, ...validData });
        }
      }
      setConfirmLoading(false);
    }
  }

  /**
   * 获取弹层标题
   */
  function getModalTitle() {
    const typeName = title;
    let actionName = localeData.Edit;
    switch (type) {
      case 'add':
        actionName = localeData.Create;
        break;
      case 'edit':
        actionName = localeData.Edit;
        break;
      case 'copy':
        actionName = localeData.Copy;
        break;
      default:
        break;
    }
    return `${actionName}${typeName}${code ? ` - ${code}` : ''}`;
  }

  function handleValuesChange(values) {
    setRuleForm(Object.assign({}, ruleForm, values));
  }

  function typeChange() {}

  return (
    <Modal
      width={900}
      getContainer={false}
      title={getModalTitle()}
      visible={visible}
      onOk={handleOk}
      confirmLoading={confirmLoading}
      wrapClassName="edit-modal-wrap"
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Form form={form} className="login-form" initialValues={ruleForm} onValuesChange={handleValuesChange}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="name" label={'名称'} rules={[{ required: true, message: __('Please', 'Input') }]}>
              <Input placeholder={__('Please', 'Input')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="remark" label={'备注'}>
              <Input placeholder={__('Please', 'Input')} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

export default EditModal;
