.section-filter {
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
  background-color: #fff;
  border-color: rgba(239, 239, 245, 1);
  padding: 12px 16px !important;
  margin-bottom: 16px !important;



  .ant-select-multiple .ant-select-selection-placeholder {
    top: 13px;
  }

  .ant-select-multiple .ant-select-arrow {
    top: 14px;
  }

  // .ant-select-multiple .ant-select-selection-item {
  //   margin-top: 1px;
  // }
  .ant-select-selection-overflow-item {
    margin-top: -1px;
  }

  .item-label {
    text-align: left;
    width: 70px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 30px;
    padding: 0 7px 0 0;
    margin-right: -1px;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
    font-size: 12px;
    vertical-align: middle;
    display: inline-block;
    text-align: right;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
  }

  .ant-form-item {
    .ant-select-field {
      width: calc(100% - 100px);
      vertical-align: middle;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .ant-select-field-hidden {
      width: calc(100% - 70px);
      vertical-align: middle;
    }

    .oper-select {
      width: 80px;
      vertical-align: middle;

      .ant-select-selector {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  .ant-form-item {
    .ant-select-field {
      width: calc(100% - 100px);
      vertical-align: middle;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .ant-select-field-hidde {
      width: calc(100% - 70px);
      vertical-align: middle;
    }

    .oper-select {
      width: 80px;
      vertical-align: middle;

      .ant-select-selector {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  .select-tail {
    box-sizing: border-box;
    border: 1px solid #d9d9d9;
    display: inline-block;
    padding: 0 7px;
    margin-right: -1px;
    border-radius: 0 2px 2px 0;
    margin-left: -1px;
    vertical-align: middle;

    span {
      cursor: pointer;

      &.anticon {
        color: #999;
      }
    }
  }

  .disabled-icon {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    box-shadow: none;
    opacity: 1;

    span {
      cursor: not-allowed;
    }
  }

  .ant-picker {
    border-radius: 6px;
  }

  .ant-picker-input {
    width: calc(100% - 70px);
  }

  .filter-bi-form-item {
    margin-bottom: 12px;
  }

  .date-picker {
    .ant-picker-input {
      width: 100%;
    }
  }

  .ant-form-item-label {
    overflow: unset;

    label {
      font-size: 12px;
    }
  }

}

.my-node-enter {
  opacity: 0;
}

.my-node-enter-active {
  opacity: 1;
  transition: opacity 200ms;
}

.my-node-exit {
  opacity: 1;
}

.my-node-exit-active {
  opacity: 0;
  transition: opacity 200ms;
}
