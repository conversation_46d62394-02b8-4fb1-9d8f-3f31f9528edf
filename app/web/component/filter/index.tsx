import React, { useState, useEffect, useContext, useImperativeHandle } from 'react';
import { Form, Row, Col, Input, Button, Select, DatePicker, Divider, Space, Tooltip, TimePicker, Cascader, AutoComplete } from 'antd';
import { PlusOutlined, MinusOutlined, LoadingOutlined, CloseCircleOutlined, SearchOutlined, RedoOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import request from '@/modules/request';
import MultSelectModal from '@/component/multSelectModal/index';
import moment from 'moment';
import './index.scss';
import { CSSTransition } from 'react-transition-group'

const { Option } = Select;
const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;

const defaultLayout = {
  labelCol: { span: 8 }
};
const defaultFilterColumnNum = 4;

function Filter(props, ref) {
  const { localeData } = useContext(AppContext);
  const [isShowFilter, setIsShowFilter] = useState(true);
  const { handleSpread, hideSpread, params } = props;
  const [loadingMap, setLoadingMap] = useState<any>({}); // 下拉选框，单级 loading 标识变量
  const [spread, setSpread] = useState<any>({});
  const [titleMap, setTitleMap] = useState<any>({});
  const [showEditPop, setShowEditPop] = useState(false); // 是否显示编辑弹层
  const [listMap, setListMap] = useState<any>({}); // 下拉选框，列表数据
  const [editPopObj, setEditPopObj] = useState<any>({ records: [], value: '', column: '', columnChineseName: '' });
  const associatedColumns = [] as any; // 关联关键列更新 联动更新的列
  const filterColumnNum = props.filterColumnNum || defaultFilterColumnNum;
  const filterColumns = [] as any;
  const initialValues = {} as any;
  props.columns.map(item => {
    if (item.filter && item.filter.type) {
      const newItem = Object.assign({}, item);
      newItem.title = item.filter.title || item.title;
      newItem.required = item.filter.required || false;
      newItem.mode = item.filter.mode || null;
      if (!item.filter.hidden) {
        filterColumns.push(newItem);
      }
    }
    if (item.filter && (item.filter.defaultValue || item.filter.initialValues)) {
      initialValues[item.dataIndex] = item.filter.defaultValue || item.filter.initialValues;
      delete item.filter.defaultValue;
      delete item.filter.initialValues;
    }
  });
  const [form] = Form.useForm();

  useEffect(() => {
    if (params) {
      form.setFieldsValue(params); // 解决 业务页筛选条件更新后 筛选组件UI 未同步问题
    }
  }, [params]);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    setFieldsValue: newObj => {
      form.setFieldsValue(newObj);
    }
  }));

  const renderFormItem = colum => {
    const dataIndex = colum.dataIndex;
    const columnTmp = { ...colum.filter };
    let labelStyle: any = { fontWeight: 'normal' };
    if (colum.filter.breakword) {
      labelStyle = { fontWeight: 'normal', whiteSpace: 'break-spaces', lineHeight: '1em', textAlign: 'left' };
    }
    let label = <span title={colum.title} style={labelStyle}>{colum.title}</span>;
    switch (colum.filter.type) {
      case 'Input':
        const InputProps = {
          placeholder: colum.filter.placeholder || colum.title,
          ...colum.filter
        };
        return (
          <Form.Item
            name={colum.dataIndex}
            label={label}
            rules={[
              {
                required: colum.required,
                message: `Input ${colum.title}!`
              }
            ]}
          >
            <Input {...InputProps} />
          </Form.Item>
        );
      case 'Select':
        const options = colum.filter.options || [];
        const selectProps = {
          mode: colum.mode,
          optionFilterProp: 'label',
          showArrow: true,
          showSearch: true,
          allowClear: true,
          placeholder: colum.title,
          getPopupContainer: () => document.querySelector('.ant-layout-content.main-content'),
          ...colum.filter
        };

        return (
          <Form.Item
            label={label}
            key={colum.dataIndex}
            name={colum.dataIndex}
            rules={[{ required: colum.required }]}
          >
            <Select {...selectProps}>
              {options.map(item => {
                return (
                  <Option key={item.value} value={item.value} label={item.label}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        );
      case 'AutoComplete':
        const autoCompleteProps = {
          placeholder: colum.title,
          allowClear: true,
          filterOption: (inputValue, option) => {
            return option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          },
          ...colum.filter
        };
        return (
          <Form.Item
            name={colum.dataIndex}
            label={label}
          >
            <AutoComplete {...autoCompleteProps} />
          </Form.Item>
        );
      case 'Cascader':
        const cascaderOptions = {
          disabled: colum.disabled || false,
          mode: colum.mode, // 多选：multiple
          optionFilterProp: 'label',
          showArrow: true,
          allowClear: true,
          showSearch: true,
          placeholder: colum.title,
          onChange: value => {
            if (colum.filter.onChange) {
              colum.filter.onChange(value, form);
            }
          },
          onSearch: value => {
            if (colum.filter.onSearch) {
              colum.filter.onSearch(value, form);
            }
          },
          ...colum.filter,
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}

          // validateTrigger={['onChange', 'onSearch']}

          >
            <Cascader {...cascaderOptions} >
            </Cascader>
          </Form.Item>
        );
      case 'MultipleSelect':
        let speadDom: any = null;
        const disabled = colum.filter.disabled;
        const defaultValue = colum.filter.defaultValue;
        const textVals = form.getFieldValue(dataIndex);
        if (spread[dataIndex]) {
          if (disabled) {
            speadDom = (
              <MinusOutlined />
            );
          } else {
            speadDom = (
              <Tooltip title={localeData.CollapsedColumn}>
                <MinusOutlined
                  onClick={() => {
                    const speadObj = { key: dataIndex, spead: false };
                    updateSpread({ [dataIndex]: false });
                    handleSpread(speadObj);
                  }}
                />
              </Tooltip>
            );
          }
        } else {
          if (disabled) {
            speadDom = (<PlusOutlined />);
          } else {
            speadDom = (
              <Tooltip title={localeData.ExpandColumn}>
                <PlusOutlined
                  onClick={() => {
                    const speadObj = { key: dataIndex, spead: true };
                    updateSpread({ [dataIndex]: true });
                    handleSpread(speadObj);
                  }}
                />
              </Tooltip>
            );
          }
        }
        let suffixDom: any = (<span />);
        if (disabled) {
          suffixDom = <span />;
        } else if (loadingMap[dataIndex]) {
          suffixDom = <LoadingOutlined />;
        } else if (textVals) {
          suffixDom = (
            <CloseCircleOutlined
              onClick={async function () {
                form.setFieldsValue({ [dataIndex]: undefined });
                updateLoadingMap({ [dataIndex]: false });
              }}
              style={{ cursor: 'pointer', color: 'rgba(0,0,0,.45)' }}
            />
          );
        }
        columnTmp.labelstyle = colum.filter.labelStyle;
        columnTmp.valuestyle = colum.filter.valueStyle;
        delete columnTmp.labelStyle;
        delete columnTmp.valueStyle;
        const multipleSelectProps: any = {
          className: !hideSpread ? 'ant-select-field' : 'ant-select-field-hidden',
          readOnly: true,
          value: textVals,
          placeholder: localeData.placeholder || '',
          suffix: suffixDom,
          onClick: () => {
            if (!listMap[dataIndex]) {
              updateLoadingMap({ [dataIndex]: true });
              getSelectList({ column: dataIndex }, form);
            } else {
              showEditColumnPop({ column: dataIndex });
            }
          },
          ...columnTmp
        };
        const spreadBtn = !hideSpread ? <div className={disabled ? 'select-tail disabled-icon' : 'select-tail'}>{speadDom}</div> : null;
        return (
          <Form.Item
            className="filter-bi-form-item"
            key={dataIndex}
            name={dataIndex}
            label={label}
            rules={[{ required: colum.required }]}
          >
            <>
              <Input style={columnTmp.valuestyle || {}} {...multipleSelectProps} />
              {spreadBtn}
            </>
          </Form.Item>
        );
      case 'RangePicker':
        const DateProps = {
          style: { width: '100%' },
          showTime: false,
          placeholder: [colum.title],
          ranges: colum.filter.picker !== 'month' ? {
            [localeData.Yesterday]: [moment().subtract(1, 'd').startOf('d'), moment().subtract(1, 'd').endOf('d')],
            [localeData.Today]: [moment().startOf('d'), moment().endOf('d')],
            [localeData.ThisWeek]: [moment().startOf('week'), moment().endOf('week')],
            [localeData.ThisMonth]: [moment().startOf('month'), moment().endOf('month')],
            [localeData.Last3Days]: [moment().add(-3, 'day'), moment().add(-1, 'day')],
            [localeData.Last7Days]: [moment().add(-7, 'day'), moment().add(-1, 'day')],
            [localeData.Last15Days]: [moment().add(-15, 'day'), moment().add(-1, 'day')],
            [localeData.Last30Days]: [moment().add(-30, 'day'), moment().add(-1, 'day')],
            [localeData.Last3Months]: [moment().add(-30 * 3, 'day'), moment().add(-1, 'day')],
            [localeData.Last6Months]: [moment().add(-30 * 6, 'day'), moment().add(-1, 'day')],
            [localeData.LastYear]: [moment().add(-30 * 12, 'day'), moment().add(-1, 'day')]
          } : {

            [localeData.ThisMonth]: [moment().startOf('month'), moment().endOf('month')],
            [localeData.Last3Months]: [moment().add(-30 * 3, 'day'), moment().add(-1, 'day')],
            [localeData.Last6Months]: [moment().add(-30 * 6, 'day'), moment().add(-1, 'day')],
            [localeData.LastYear]: [moment().add(-30 * 12, 'day'), moment().add(-1, 'day')]
          },
          ...colum.filter
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
          >
            {/* @ts-ignore */}
            <RangePicker className="date-picker" {...DateProps} />
          </Form.Item>
        );
      case 'TimeRangePicker':
        const TimeDateProps = {
          style: { width: '100%' },
          ...colum.filter
        };
        return (
          <Form.Item
            key={colum.dataIndex}
            name={colum.dataIndex}
            label={label}
          >
            <TimeRangePicker {...TimeDateProps} />
          </Form.Item>
        );
      default:
        break;
    }
  };

  const renderFields = () => {
    const count = filterColumns.length;
    const children = [] as any;
    let spanNum = Math.floor(24 / filterColumnNum);
    const btnSpan = 24 - count % filterColumnNum * spanNum;

    for (let i = 0; i < count; i++) {
      if (filterColumns[i]?.filter?.span) {
        spanNum = filterColumns[i].filter.span;
      }
      children.push(
        <Col span={spanNum} key={i}>
          {renderFormItem(filterColumns[i])}
        </Col>
      );
    }
    children.push(
      <Col span={btnSpan} className='operation-bar mb-0 flex-end' style={{ textAlign: 'right' }}>
        <Space>
          <Button
            icon={<RedoOutlined />}
            onClick={() => {
              form.resetFields();
              props.onChange({}, 'reset');
            }}
          >
            {localeData.Reset}
          </Button>
          <Button icon={<SearchOutlined />} htmlType="submit" className='filter-btn-query'>
            {localeData.Query}
          </Button>
        </Space>
      </Col>
    )
    return children;
  };

  const onChangeHandler = (values: Record<string, any>, type?: string) => {
    const transformDates = filterColumns.filter(it => it?.mapToData);
    if (!transformDates.length) {
      props.onChange(values);
      return;
    }
    transformDates.forEach(transformDate => {
      const {
        key,
        mapToData: [startKey, endKey, format]
      } = transformDate;
      const dateItem = values[key];
      if (dateItem) {
        values[startKey] = dateItem[0].format(format || 'YYYY-MM-DD');
        values[endKey] = dateItem[1].format(format || 'YYYY-MM-DD');
      }

      Reflect.deleteProperty(values, key);
    })

    props.onChange(values, type);
  };


  const onFinish = values => {
    onChangeHandler(values);
  };

  /** 更新展开折叠变量 */
  function updateSpread(newObj: any) {
    const newEntity: any = { ...spread, ...newObj };
    setSpread(newEntity);
  }
  /** 更新loading...弹层显示变量 */
  function updateLoadingMap(newObj: any) {
    const newEntity: any = { ...loadingMap, ...newObj };
    setLoadingMap(newEntity);
  }
  /** 更新筛选项数据列表 */
  function updateListMap(newObj: any) {
    const newEntity: any = { ...listMap, ...newObj };
    setListMap(newEntity);
  }
  /** 异步请求，获取下拉选项数据 */
  async function getSelectList(opt, form) {
    const { column } = opt;
    const { associatedColumns, columnListUrl } = props;
    const params: any = { column };
    if (associatedColumns && associatedColumns !== column) {
      let associateVal = form.getFieldValue(associatedColumns);
      if (associateVal && Array.isArray(associateVal)) {
        associateVal = associateVal.join(',');
      }
      params[associatedColumns] = associateVal;
    }
    if (props.onBeforeGetSelectList) {
      Object.assign(params, await props.onBeforeGetSelectList(Object.assign({}, params), column, form));
    }
    const columnListRes: any = await request.get(columnListUrl, params);
    const { list } = columnListRes;
    updateListMap({ [column]: list });
    updateLoadingMap({ [column]: false });
    showEditColumnPop({ column, list });
    if (props.onAfterGetSelectList) {
      props.onAfterGetSelectList(column, form);
    }
  }
  /** 显示多选筛选项弹层 */
  function showEditColumnPop(opt) {
    const { column, list } = opt;
    const records = [];
    if (props.onHandlePopList) {
      records.push.apply(records, props.onHandlePopList(column, list, form));
    } else {
      let listData: any = [];
      if (list) {
        listData = list;
      } else {
        listData = listMap[column];
      }
      records.push.apply(records, listData.map((item: string) => {
        return {
          value: item,
          text: item,
        };
      }));
    }

    let value = form.getFieldValue(column);
    const columnChineseName = titleMap[column]; // 获取列名称
    if (!Array.isArray(value) && value?.split) {
      value = value.split(',');
    }
    const newEditPopObj = { ...editPopObj, records, value, column, columnChineseName };
    setEditPopObj(newEditPopObj);
    setShowEditPop(true);
  }
  /** 更新多选条件 */
  async function updateColumn(opt: { column: string; newVals: any[] }) {
    const { column, newVals } = opt;
    let textVals = null as any;
    if (newVals?.length > 0) {
      textVals = newVals.join(',');
    }
    form.setFieldsValue({ [column]: textVals });
  }


  const layout = props.layout || defaultLayout;

  const onKeydownEnter = (e: KeyboardEvent) => {
    const key = e.key;
    if (key === 'Enter') {
      form.submit();
    }
  }

  useEffect(() => {
    window.addEventListener('keydown', onKeydownEnter);

    return () => {
      window.removeEventListener('keydown', onKeydownEnter);
    }
  }, [])

  return (
    <Form {...layout} colon={false} form={form} initialValues={initialValues} name="advanced_search" className="ant-advanced-search-form section-item section-filter" onFinish={onFinish} style={{ paddingBottom: '4px' }}>
      <div className="operation-bar">
        <h2>{localeData.FiterStr}</h2>
        <div>
          <Tooltip title={isShowFilter ? 'Hidden Filter' : 'Show Filter'}>
            <Button onClick={() => setIsShowFilter(!isShowFilter)} style={{ height: 28 }}
              icon={isShowFilter ? <UpOutlined /> : <DownOutlined />}></Button>
          </Tooltip>
        </div>
      </div>
      <Row style={{ display: isShowFilter ? 'flex' : 'none' }}>{renderFields()}</Row>

      {showEditPop && <MultSelectModal visible={showEditPop} setVisible={setShowEditPop} title={editPopObj?.columnChineseName || ''} data={editPopObj} submitColumn={updateColumn} />}
    </Form>
  );
}

export default React.forwardRef(Filter);
