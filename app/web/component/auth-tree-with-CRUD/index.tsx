import { Checkbox, Col, Row, Tree } from 'antd';
import './index.scss';
import { isFunction } from '@@/lib/tool';
import { useEffect, useRef, useState } from 'react';

export interface TreeNode {
  id: string;
  title: string;
  key: string;
  value: string;
  path: string;
  children: TreeNode[];
  disabled: boolean;
  operateAuths: string[];
  isLeaf: boolean;
  roleAuthId: number;
}

type TreeTitleProps = Omit<TreeNode, 'key' | 'onChange'> & {
  onChange: (nodeData: {
    id: number;
    auths: string[],
    path: string;
    title: string;
  }) => void
};

const CRUD_AUTH_OPTIONS = [
  { label: '创建', value: 'C' },
  { label: '浏览', value: 'R' },
  { label: '更新', value: 'U' },
  { label: '导出', value: 'E' },
  { label: '删除', value: 'D' },
];

function TreeTitle(props: TreeTitleProps) {
  const {
    title,
    path,
    disabled,
    isLeaf,
    operateAuths,
    onChange
  } = props;
  const [authValue, setAuthValue] = useState(disabled ? [] : operateAuths);

  const handleChange = (checkedValue: string[]) => {
    setAuthValue(checkedValue);
    isFunction(onChange) && onChange({
      id: props.roleAuthId,
      auths: checkedValue,
      path: props.path,
      title: props.title
    });
  };

  useEffect(() => {
    setAuthValue(disabled ? [] : operateAuths);
  }, [disabled, operateAuths]);
  let USE_CRUD_AUTH_OPTIONS = [...CRUD_AUTH_OPTIONS];
  if (['/operation/clue-manage/pool', '/operation/client-manage/pool'].includes(path)) {
    USE_CRUD_AUTH_OPTIONS.push({
      label: '指派',
      value: 'P'
    })
  }
  if (['/operation/client-manage/personal'].includes(path)) {
    USE_CRUD_AUTH_OPTIONS.push({
      label: '转让',
      value: 'T'
    })
  }
  return <Row className="auth-tree-with-crud-title" justify="space-between">
    <Col>
      <span className="auth-tree-with-crud-title-text">{title}</span>
      <span className="auth-tree-with-crud-title-subtext">{path}</span>
    </Col>
    <Col onClick={(e) => {
      e.stopPropagation();
    }}>
      {
        isLeaf ?
          <Checkbox.Group
            options={USE_CRUD_AUTH_OPTIONS}
            disabled={disabled}
            value={authValue}
            onChange={handleChange}
          /> : <></>
      }
    </Col>
  </Row>;
}

export interface AuthTreeWithCRUDProps {
  treeData: any[];
  onChange?: (value: Array<{
    id: number;
    auths: string[]
  }>) => void;
}

export default function AuthTreeWithCRUD(props: AuthTreeWithCRUDProps) {
  const { treeData, onChange } = props;

  const checkedAuthsRef = useRef<Array<{
    id: number;
    auths: string[]
  }>>([]);

  const handleChange: TreeTitleProps['onChange'] = (nodeData) => {
    let item = checkedAuthsRef.current.find(i => i.id === nodeData.id);
    if (!item) {
      item = {
        id: nodeData.id,
        auths: nodeData.auths
      };
      checkedAuthsRef.current.push(item);
    } else {
      item.auths = nodeData.auths;
    }
    isFunction(onChange) && onChange(checkedAuthsRef.current);

  };

  useEffect(() => {
    checkedAuthsRef.current = [];
  }, [treeData]);

  return (
    <div className="auth-tree-with-crud-wrapper">
      {/*<div className="auth-tree-with-crud-header">*/}

      {/*</div>*/}
      <div className="auth-tree-with-crud-main">
        <Tree
          showLine
          blockNode
          defaultExpandAll
          autoExpandParent
          treeData={treeData}
          titleRender={(nodeData => (
            <TreeTitle {...nodeData} onChange={handleChange} />
          ))}
        />
      </div>
    </div>
  );
}
