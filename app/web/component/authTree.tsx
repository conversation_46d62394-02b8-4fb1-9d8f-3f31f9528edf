import React, { useEffect, useContext, useState } from 'react';
import { useQueryList } from '@/hooks';
import { getTargetText } from '@@/lib/tool';
import { AppContext } from '@/store';
import { Checkbox } from 'antd';
import './authTree.scss';

const authTree = ({ onChange, name, checkedData, onfetchList }) => {
  const isUserRole = name === 'userRole';
  const {
    listData,
    fetchData: fetchDataOrigin
  } = useQueryList({
    fetchUrl: isUserRole ? '/role' : '/authority',
    pageSize: 20
  });
  const {
    BaseData: { authType }
  } = useContext(AppContext);
  const [checkList, setCheckList] = useState<any>([]);
  const [isInit, setIsInit] = useState<any>(false);

  useEffect(() => {
    fetchDataOrigin();
  }, []);

  useEffect(() => {
    if (listData.length > 0) {
      onfetchList && onfetchList(listData);
    }
  }, [listData]);

  useEffect(() => {
    if (checkedData && !isInit) {
      setCheckList(checkedData);
      setIsInit(true);
    }
  }, [checkedData]);

  useEffect(() => {
    if (typeof onChange === 'function') {
      onChange(checkList);
    }
  }, [checkList]);

  function findParentCodes(arr, currentCode) {
    let parentCodes = [];
    function findParent(arrSon, currentCode) {
      for (let item of arrSon) {
        if (item.code === currentCode) {
          if (item.parent) {
            // @ts-ignore
            parentCodes.push(item.parent);
            findParent(arr, item.parent);
          }
          break;
        } else if (item.children) {
          findParent(item.children, currentCode);
        }
      }
    }
    findParent(arr, currentCode);
    return parentCodes;
  }

  function handleItemChange(e, item) {
    const isChecked = e.target.checked;
    const codeArr = [];
    getCode(codeArr, item);
    const parentCode = !isUserRole ? findParentCodes(listData?.menu, item.code).filter(item => item !== '0' && item !== '1') : [];
    if (isChecked) {
      setCheckList(Array.from(new Set(codeArr.concat(checkList).concat(parentCode))));
    } else {
      // @ts-ignore
      setCheckList(checkList.filter(item => !codeArr.includes(item)));
    }
  }

  function getCode(codeArr, item) {
    codeArr.push(item.code);
    if (item.children && item.children.length) {
      item.children.forEach(sub => {
        getCode(codeArr, sub);
      });
    }
  }

  function renderAuthList(arr) {
    return arr && arr.length > 0 ? (
      <Checkbox.Group key={arr.length} style={{ width: '100%' }} value={checkList}>
        <ul className="auth-list">
          {arr.map(item => {
            const { type, remark } = item;
            let textStr: any = null;
            if (type === 'menu') {
              textStr = <span>{item['path']}<small>{remark}</small></span>;
            } else {
              textStr = item['name'];
            }
            return (<li key={item.id}>
              <div className="auth-item">
                <p className="auth-name">
                  <Checkbox
                    value={item.code}
                    onChange={e => {
                      handleItemChange(e, item);
                    }}
                  >
                    {textStr}
                    {!isUserRole && <small>({getTargetText(item.type, authType)})</small>}
                  </Checkbox>
                </p>
              </div>
              {renderAuthList(item.children)}
            </li>);
          })}
        </ul>
      </Checkbox.Group>
    ) : null;
  }
  return <div className="auth-tree-wrap">{isUserRole ? renderAuthList(listData) : authType.map(item => renderAuthList(listData[item.value]))}</div>;
};

export default authTree;
