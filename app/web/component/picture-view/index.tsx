import React from 'react';
import { Modal, Carousel } from 'antd';
import './index.scss';

/** 图片轮播弹窗 组件 */
const PictureView = ({ imgList, visible, hidePictureView }) => {
  let Node = imgList.map((item, index) => {
    return (
      <div key={`picture_index_${index}`}>
        <div>
          <img src={item} style={{ width: '90%', height: 'auto', marginBottom: 40 }} />
        </div>
        {/* <hr /> */}
        <div style={{ maxHeight: 200 }}></div>
      </div>
    );
  });
  return (
    <Modal visible={visible} width={500} onOk={hidePictureView} onCancel={hidePictureView} footer={null}>
      <Carousel>{Node}</Carousel>
    </Modal>
  );
};
export default React.memo(PictureView);
