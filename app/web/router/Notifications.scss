.notifications-container {
  font-size: 14px;

  .ant-drawer-body {
    padding: 0;
  }


  .ant-tabs {
    padding-left: 20px;
    padding-right: 8px;
  }

  .ant-tabs-tab+.ant-tabs-tab {
    margin-left: 24px;
  }

  .ant-tabs-tab .ant-tabs-tab-btn {
    color: rgb(99, 115, 129);
    font-weight: 600;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: rgb(33, 43, 54);
    font-weight: 600;
  }

  .ant-tabs-ink-bar {
    background-color: rgb(33, 43, 54);
  }

  .ant-tabs-tab.ant-tabs-tab-active {
    .unread {
      color: #fff;
      background-color: rgb(0, 184, 217);
    }

    .archived {
      color: #fff;
      background-color: rgb(34, 197, 94);
    }

    .red {
      color: #fff;
      background-color: #FF5630;
    }
  }

  .notifications-tab-tag {
    height: 24px;
    min-width: 24px;
    border-radius: 6px;
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    padding: 0 6px;
    font-weight: 700;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;

    &.all {
      background-color: rgb(33, 43, 54);
      color: #fff;
    }

    &.unread {
      color: rgb(0, 108, 156);
      background-color: rgba(0, 184, 217, 0.16);
    }

    &.archived {
      color: rgb(17, 141, 87);
      background-color: rgba(34, 197, 94, 0.16);
    }

    &.red {
      color: #B71D18;
      background-color: rgba(255, 86, 48, 0.16);
    }

  }

  .ant-drawer-footer {
    padding: 8px;
    width: 100%;
    font-weight: 700;
    color: rgb(33, 43, 54);

    .view-all-btn {
      width: 100%;
      height: 100%;
      padding: 8px 10px;
      height: 48px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;

      border-radius: 8px;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      cursor: pointer;

      &:hover {
        background-color: rgba(145, 158, 171, 0.08);

      }
    }

  }

  .notifications-item:hover {
    text-decoration: none;
    background-color: rgba(145, 158, 171, 0.08);
  }

  .notifications-item {

    -webkit-tap-highlight-color: transparent;
    background-color: transparent;
    outline: 0px;
    border-width: 0px 0px 1px;
    border-top-style: initial;
    border-right-style: initial;
    border-left-style: initial;
    border-top-color: initial;
    border-right-color: initial;
    border-left-color: initial;
    border-image: initial;
    margin: 0px;
    border-radius: 0px;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    color: inherit;
    display: flex;
    -webkit-box-flex: 1;
    flex-grow: 1;
    -webkit-box-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    position: relative;
    text-decoration: none;
    min-width: 0px;
    box-sizing: border-box;
    text-align: left;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    padding: 20px;
    align-items: flex-start;
    border-bottom-style: dashed;
    border-bottom-color: rgba(145, 158, 171, 0.2);


    .notifications-item-avatar {
      flex-shrink: 0;
      min-width: auto;
      margin-right: 16px;
    }

    .notifications-item-content {
      display: flex;
      flex-direction: column;
      -webkit-box-flex: 1;
      flex-grow: 1;

      .notifications-item-content-root {
        flex: 1 1 auto;
        min-width: 0px;
        margin: 0px;

        .notifications-item-content-title {
          margin-bottom: 4px;
          color: rgb(33, 43, 54);
        }
      }

      .notifications-item-content-time {
        color: rgb(145, 158, 171);
        font-size: 12px;
      }

      .notifications-item-content-note {
        padding: 12px;
        margin-top: 12px;
        margin-bottom: 12px;
        border-radius: 12px;
        color: rgb(99, 115, 129);
        background-color: rgb(244, 246, 248);
      }

      .notifications-item-content-action {
        .ant-btn {
          height: 30px;
          font-size: 13px;
          font-family: "Public Sans", sans-serif;
          text-transform: unset;
          font-weight: 700;
        }

        .ant-btn-primary {
          background-color: rgb(33, 43, 54);
          color: #Fff;
          border: none;
        }
      }
    }
  }

  .notifications-container-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .notifications-tabs {
      flex: 0 0 auto;
    }

    .notifications-list-container {
      flex: 1 1 auto;
      overflow-y: auto;
    }
  }
}
