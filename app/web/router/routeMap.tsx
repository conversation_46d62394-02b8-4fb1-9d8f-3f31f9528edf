import React, { lazy } from 'react';
import Icon, { LockOutlined, MehOutlined, PartitionOutlined, TeamOutlined } from '@ant-design/icons';
import Iconfont from '@/component/iconfont'
import KeepAlive from 'react-activation';


const Authority = lazy(() => import('@/page/authority'));
const OmsUser = lazy(() => import('@/page/authority/user'));
const Role = lazy(() => import('@/page/authority/role'));
// 项目基础部分，基本不用调整 结束
const ActivityRecord = lazy(() => import('@/page/activityManage/record'));
const ClueROI = lazy(() => import('@/page/clueManage/roi'));
const FollowUpRecord = lazy(() => import('@/page/clueManage/followUpRecord'));
const OwnedLead = lazy(() => import('@/page/clueManage/ownedLead'));
const LeadPool = lazy(() => import('@/page/clueManage/leadPool'));
const CustomerPool = lazy(() => import('@/page/customerManage/pool'));
const CustomerOwned = lazy(() => import('@/page/customerManage/owned'));
const ContractApprovel = lazy(() => import('@/page/contractManage/approval'));
const OwnedApprovel = lazy(() => import('@/page/contractManage/owned'));
const WholeApproval = lazy(() => import('@/page/contractManage/whole'));
const SharedApproval = lazy(() => import('@/page/contractManage/shared'));
const TodoRecord = lazy(() => import('@/page/todoManage/record'));
const CommissionReport = lazy(() => import('@/page/commission/report'));
// plugin router file start
// plugin router file end
const lang = window.__INITIAL_STATE__.lang
const roleAuthMap = (window.__INITIAL_STATE__.auth.roleAuth || []).reduce((obj, item) => {
  obj[item.name] = item[lang === 'en' ? 'remark_en' : 'remark']
  return obj;
}, {})

export const AUTH_MAP = {
  '/operation/authority': {
    icon: <Iconfont type='icon-xitongguanli' />,
    name: roleAuthMap['authority'],
  },
  '/user-manage': {
    icon: <LockOutlined />
  },
  '/operation/authority/area': {
    view: Authority,
    name: roleAuthMap['area'],
    icon: <Iconfont type='icon-m_authority' />
  },
  '/operation/authority/role': {
    view: Role,
    name: roleAuthMap['role'],
    icon: <Iconfont type='icon-m_authority' />
  },
  '/operation/authority/user': {
    view: OmsUser,
    name: roleAuthMap['user'],
    icon: <TeamOutlined />
  },
  // plugin router end
  '/operation/user-manage': {
    name: '用户管理',
    icon: <LockOutlined />
  },
  '/operation/activity-manage': {
    name: roleAuthMap['activity-manage'],
    icon: <Iconfont type='icon-huodong' />
  },
  '/operation/activity-manage/activity': {
    view: ActivityRecord,
    name: roleAuthMap['activity'],
    icon: <Iconfont type='icon-huodongjilu' />,
    keepAlive: false
  },
  '/operation/clue-manage/roi': {
    view: ClueROI,
    name: roleAuthMap['roi'],
    icon: <Iconfont type='icon-alarm-situation' />,
    keepAlive: true
  },

  '/operation/clue-manage': {
    name: roleAuthMap['clue-manage'],
    icon: <Iconfont type='icon-xiansuoguanli1' />
  },
  '/operation/clue-manage/personal': {
    view: OwnedLead,
    name: roleAuthMap['activity'],
    icon: <Iconfont type='icon-gerenxiansuo' />,
    keepAlive: false
  },
  '/operation/clue-manage/pool': {
    view: LeadPool,
    name: roleAuthMap['pool'],
    icon: <Iconfont type='icon-xiansuochi' />,
    keepAlive: false
  },
  '/operation/clue-manage/follow-up-record': {
    view: FollowUpRecord,
    name: roleAuthMap['follow-up-record'],
    icon: <Iconfont type='icon-follow-up' />,
    keepAlive: false
  },

  '/operation/client-manage': {
    name: roleAuthMap['/client-manage'],
    icon: <Iconfont type='icon-kehuxinxiguanli' />
  },
  '/operation/client-manage/personal': {
    view: CustomerOwned,
    name: roleAuthMap['activity'],
    icon: <Iconfont type='icon-gerenkehu' />
  },
  '/operation/client-manage/pool': {
    view: CustomerPool,
    name: roleAuthMap['pool'],
    icon: <Iconfont type='icon-guazhangkehuliebiao' />
  },
  '/operation/contract-manage': {
    name: roleAuthMap['contract-manage'],
    icon: <Iconfont type='icon-hetongguanli' />
  },
  '/operation/contract-manage/approval': {
    view: ContractApprovel,
    name: roleAuthMap['approval'],
    icon: <Iconfont type='icon-Arcredit' />,
    keepAlive: false
  },
  '/operation/contract-manage/owned': {
    view: OwnedApprovel,
    name: roleAuthMap['owned'],
    icon: <Iconfont type='icon-contract' />,
    keepAlive: false
  },
  '/operation/contract-manage/whole': {
    view: WholeApproval,
    name: roleAuthMap['whole'],
    icon: <Iconfont type='icon-Contract' />,
    keepAlive: false
  },
  '/operation/contract-manage/shared': {
    view: SharedApproval,
    name: roleAuthMap['shared'],
    icon: <Iconfont type='icon-fenxiang' />,
    keepAlive: false
  },

  '/operation/todo-manage': {
    name: roleAuthMap['todo-manage'],
    icon: <Iconfont type='icon-daibanguanli' />
  },
  '/operation/todo-manage/record': {
    view: TodoRecord,
    name: roleAuthMap['record'],
    icon: <Iconfont type='icon-zu' />,
    keepAlive: true
  },
  '/operation/billing-manage': {
    name: roleAuthMap['billing-manage'],
    icon: <Iconfont type='icon-a-BillingStatement' />
  },
  '/operation/billing-manage/billing-report': {
    name: roleAuthMap['billing-report'],
    icon: <Iconfont type='icon-report' />
  },
  '/operation/billing-manage/programmatic-billing-report': {
    name: roleAuthMap['billing-report'],
    icon: <Iconfont type='icon-basic-report' />
  },
  '/operation/billing-manage/overall-billing-report': {
    name: roleAuthMap['billing-report'],
    icon: <Iconfont type='icon-zonglan' />
  },
  '/operation/billing-manage/h5-billing-report': {
    name: roleAuthMap['billing-report'],
    icon: <Iconfont type='icon-yidongduan' />
  },

  '/operation/invoice-management-base': {
    name: roleAuthMap['invoice-management-base'],
    icon: <Iconfont type="icon-invoice" />
  },
  '/operation/invoice-management-base/invoice-management': {
    name: roleAuthMap['invoice-management'],
    icon: <Iconfont type="icon-huodongjilu" />
  },
  '/operation/invoice-management-base/write-off-manager': {
    name: roleAuthMap['write-off-manager'],
    icon: <Iconfont type="icon-write_off" />
  },
  '/operation/invoice-management-base/affiliate-payment-report': {
    name: roleAuthMap['affiliate-payment-report'],
    icon: <Iconfont type="icon-report" />
  },
  '/operation/invoice-management-base/programmatic-payment-report': {
    name: roleAuthMap['programmatic-payment-report'],
    icon: <Iconfont type="icon-basic-report" />
  },
  '/operation/invoice-management-base/bad-debt-overview': {
    name: roleAuthMap['bad-debt-overview'],
    icon: <Iconfont type='icon-ic_report' />
  },

  '/operation/commission': {
    name: roleAuthMap['commission'],
    icon: <Iconfont type='icon-commission' />
  },
  '/operation/commission/report': {
    view: CommissionReport,
    name: roleAuthMap['report'],
    icon: <Iconfont type='icon-Report' />,
    keepAlive: true
  },

};
