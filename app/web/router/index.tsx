import React, { lazy, Suspense, useContext, useState, useEffect } from 'react';
import { Link, Switch, Route, Redirect, useLocation } from 'react-router-dom';
import { Layout, Menu, ConfigProvider, Spin, Dropdown, Tooltip, Drawer, Breadcrumb, Space, Badge } from 'antd';
import { LogoutOutlined, FullscreenOutlined, UsbOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import { AppContext } from '@/store';
import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/lib/locale/en_US';
import { useAuth, useLocalStorage } from '@/hooks';
import { enterFullscreen, exitFullscreen, isSuperAdmin } from '@/modules/utils';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import NavTabbar from '@/component/navTabbar'
import { AUTH_MAP } from './routeMap'
import { CSSTransition, SwitchTransition } from 'react-transition-group'
import Notifications from './Notifications';
import { useHistory } from 'react-router-dom'
import { HISOTRY_STASH } from './historyStash'
import useNotificationStore, { getUnreadCount, getContractPendingCount } from '@/store/notification.store'

const handlerScroll = debounce(() => {
  const e = new Event('resize');
  window.dispatchEvent(e);
}, 200)

import './index.scss'
import Iconfont from '@/component/iconfont';
import request from '@/modules/request';
import { debounce } from '@@/lib/tool';
import useSelector from '@/hooks/useSelector';
ConfigProvider.config({
  theme: {
    primaryColor: '#646cff',
    errorColor: '#f5222d'
  },

})

// 项目基础部分，基本不用调整 开始
const Home = lazy(() => import('@/page/home/<USER>'));
const Login = lazy(() => import('@/page/login'));
const Dingtalk = lazy(() => import('@/page/login/dingtalk'));


const { Header, Sider, Content } = Layout;

function getRouteInfo(path) {
  let result = {};
  Object.keys(AUTH_MAP).forEach(key => {
    if (path.endsWith(key)) {
      result = AUTH_MAP[key];
      return;
    }
  });
  return result;
}

function getOpenKeys(path) {
  const arr: string[] = path.split('/');
  return [arr.slice(0, 3).join('/')];
}

export default function AppRoute() {
  const location = useLocation();
  const history = useHistory();
  const localStorage = useLocalStorage();
  const [isShowNotification, setIsShowNotification] = useState(false);
  const path = location.pathname;
  const { unreadCount, setUnreadCount, contractPendingCount, setContractPendingCount } = useNotificationStore(useSelector(['unreadCount', 'setUnreadCount', 'contractPendingCount', 'setContractPendingCount']));

  const { user, auth, lang, localeData, omsPluginConfig, query } = useContext(AppContext);
  const [isLogout, setIsLogout] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<any>([path]);
  const [sideMenu, setSideMenu] = useState<any>(getSideMenu(path));
  const [topSelectedKeys, setTopSelectedKeys] = useState<any>('/' + path.split('/')[1]);
  const [openKeys, setOpenKeys] = useState<any>(getOpenKeys(path));
  const [counter, setCounter] = useState(1);
  const { isNotAuth } = useAuth();
  const [inIframe, setInIframe] = useState(false);
  const [showUserPop, setShowUserPop] = useState<boolean>(false);
  const [transitionStyle, setTransitionStyle] = useState({
    overflow: '',
  });
  const [menuList, setMenuList] = useState<any>([]);

  const [inputVal, setInputVal] = useState('');
  const [collapsed, setCollapsed] = useState(localStorage.getStoredValue('collapsed', 9) === 1);

  const [isFullscreen, setIsFullscreen] = useState(false);

  const userDropdownItems = [
    {
      label: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {localeData.PersonalCenter}
        </span>
      ), key: '1', icon: <UsbOutlined />, onClick: () => { setShowUserPop(true) }
    }, // 菜单项务必填写 key
    {
      label: localeData.LogOut, key: '2', icon: <LogoutOutlined />, onClick: () => {
        setIsLogout(true);
        window.location.href = '/logout'
      }
    },
  ];


  const getNotifyUnreadCount = async () => {
    const unreadCount = await getUnreadCount();

    setUnreadCount(unreadCount)
  }
  useEffect(() => {
    setMenuList([...(sideMenu || [])]);
    const contractMenu = sideMenu.find(it => it.name === 'contract-manage')
    if (contractMenu?.children?.length && contractMenu.children.find(it => it.name === 'approval')) {
      getContractPendingCount().then(res => {
        setContractPendingCount(res)
      })
    }
  }, [sideMenu]);

  useEffect(() => {
    if (window.__INITIAL_STATE__.isLogin) {
      getNotifyUnreadCount();

      HISOTRY_STASH.history = history;
    }
    if (query.in_iframe) {
      sessionStorage.setItem('in_iframe', query.in_iframe);
      setInIframe(true);
    } else {
      if (sessionStorage.getItem('in_iframe')) {
        setInIframe(true);
      }
    }
  }, []);

  /**
   * 动态加载插件路由
   */
  Object.keys(omsPluginConfig).map((key: string) => {
    try {
      let pluginRouter: any = null;
      if (omsPluginConfig[key]?.package) {
        if (omsPluginConfig[key]?.package?.startsWith('@flat-design/')) {
          const packageName = omsPluginConfig[key]?.package?.split('@flat-design/')?.[1];
          pluginRouter = require(`@@@/node_modules/@flat-design/${packageName}/app/web/router`);
        } else if (omsPluginConfig[key]?.package?.startsWith('@flat/')) {
          const packageName = omsPluginConfig[key]?.package?.split('@flat/')?.[1];
          pluginRouter = require(`@@@/node_modules/@flat/${packageName}/app/web/router`);
        }
      } else {

      }
      if (pluginRouter) {
        Object.assign(AUTH_MAP, pluginRouter.default || pluginRouter);
      }
    } catch (e) { }
  });

  function getDefaultRoute(target) {
    const arr = auth.authObjTree.pathList;
    let result: string = '/';
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i];
      if (item.startsWith(target) && item.split('/').length === 4) {
        result = item;
        break;
      }
    }

    return result;
  }
  useEffect(() => {
    handleTopSelect(path);
    setOpenKeys(getOpenKeys(path));

    // console.log(routes)
  }, [path]);


  function getSideMenu(path) {
    let result: any = [];
    if (auth.authObjTree && auth.authObjTree.menu && auth.authObjTree.menu.length) {
      auth.authObjTree.menu.forEach(item => {
        if (path.startsWith(item.path)) {
          result = item.children;
          return;
        }
      });
    }
    return result;
  }

  function handleTopSelect(openKeys) {
    const path = typeof openKeys === 'string' ? openKeys : openKeys.key;
    setSideMenu(getSideMenu(path));
    setInputVal('');
    setTopSelectedKeys('/' + path.split('/')[1]);
  }

  useEffect(() => {
    if (selectedKeys[0] !== location.pathname) {
      setSelectedKeys([location.pathname]);
    }
  }, [location]);

  const menuDom = (
    <Menu>
      <Menu.Item key="en" disabled={lang === 'en'}>
        <a href="?lang=en">English</a>
      </Menu.Item>
      <Menu.Item key="zh" disabled={lang === 'zh'}>
        <a href="?lang=zh">中文</a>
      </Menu.Item>
    </Menu>
  );

  // 多层次菜单渲染
  const menuRender = arr => {
    return arr.map(item => {
      const { icon }: any = getRouteInfo(item.path);
      const hasChild = item.children && item.children.length > 0;
      let toPath = item.path;
      if (['auth-mjicdc', 'auth-gm3376', 'auth-sc49u6', 'auth-72fqhh', 'auth-eni6ox', 'auth-7ybat4', 'auth-4nsffz', 'auth-emle1d', 'auth-bt3qdh'].includes(item.auth_code)) {
        toPath = undefined
      }
      return hasChild ? (
        <Menu.SubMenu key={item.path} icon={icon} title={lang === 'zh' ? item.remark : item[`remark_${lang}`] || item.remark}>
          {menuRender(item.children)}
        </Menu.SubMenu>
      ) : (
        <Menu.Item key={item.path}>
          {icon}
          {
            !toPath ? (
              <div onClick={() => {
                const authCodeStrategy = {
                  'auth-mjicdc': 'https://traffic.mobshark.net/billing-report',
                  'auth-gm3376': 'https://traffic.mobshark.net/programmatic-billing-report',
                  'auth-sc49u6': 'https://traffic.mobshark.net/overall-billing-report',
                  'auth-72fqhh': 'https://traffic.mobshark.net/h5-billing-report',
                  'auth-eni6ox': 'https://traffic.mobshark.net/invoice-management',
                  'auth-7ybat4': 'https://traffic.mobshark.net/write-off-manager',
                  'auth-4nsffz': 'https://traffic.mobshark.net/affiliate-payment-report',
                  'auth-emle1d': 'https://traffic.mobshark.net/programmatic-payment-report',
                  'auth-bt3qdh': 'https://traffic.mobshark.net/bad-debt-overview'
                }
                window.open(authCodeStrategy[item.auth_code])
              }}>
                {lang === 'zh' ? item.remark : item[`remark_${lang}`] || item.remark}
              </div>
            ) : (
              <Link title={lang === 'zh' ? item.remark : item[`remark_${lang}`] || item.remark} to={toPath}>
                {
                  (item.name === 'approval' && item.path.includes('contract')) ? (
                    <Badge dot={contractPendingCount > 0} offset={[8, 7]}>
                      {lang === 'zh' ? item.remark : item[`remark_${lang}`] || item.remark}
                    </Badge>
                  ) : (
                    lang === 'zh' ? item.remark : item[`remark_${lang}`] || item.remark
                  )
                }
              </Link>
            )
          }

        </Menu.Item>
      );
    });
  };


  const pathSnippets = location.pathname.split('/').filter(i => i);


  const extraBreadcrumbItems = pathSnippets.map((_, index) => {
    const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
    if (index === 0) { return null };
    return (
      <Breadcrumb.Item key={url}>
        <Link to={url} >
          {AUTH_MAP[url]?.icon}
          {AUTH_MAP[url]?.name}
        </Link>
      </Breadcrumb.Item>
    );
  });

  return (
    <ConfigProvider locale={lang === 'zh' ? zhCN : enUS} autoInsertSpaceInButton={false}>
      {path === '/login' || path === '/page/ddlogin' ? (
        <Suspense fallback={null}>
          <Switch>
            <Route path="/login" component={Login} />
            <Route path="/page/ddlogin" component={Dingtalk} />
          </Switch>
        </Suspense>
      ) : (
        <Layout>
          {((menuList.length > 0 && !query.in_iframe && !inIframe) || sideMenu?.length > 0) && ( // iframe中隐藏左侧菜单
            <Sider collapsed={collapsed} defaultCollapsed={collapsed} collapsedWidth={64} className="app-layout-sidebar" width={220} trigger={null} style={{ height: '100vh', overflow: 'hidden' }}>
              <div className='logo-container'>
                <Link to={`/`}>
                  <div className="logo-txt">
                    <span className="logo-icon"></span>
                    {
                      collapsed ? null : (
                        <div className={`app-name ${localeData.AppName.length > 18 ? 'small-size' : ''}`} title={localeData.AppName}>
                          {localeData.AppName}
                        </div>
                      )
                    }
                  </div>
                </Link>
              </div>
              <div className="app-layout-sidebar-menu-wrapper">
                <Menu key={counter} mode="inline" defaultOpenKeys={collapsed ? null : openKeys} selectedKeys={selectedKeys}>
                  {menuRender(menuList)}
                </Menu>
              </div>
            </Sider>
          )}

          <Layout className="main-wrap" style={{ height: '100vh' }}>
            {!query.in_iframe &&
              !inIframe && ( // iframe中隐藏头部
                <Header className="page-main-header">
                  <>
                    <div className='header-container'>
                      <div className="header-left-wrap">
                        <div className="collapse-btn" onClick={() => {
                          localStorage.setStoredValue('collapsed', !collapsed ? 1 : 0);
                          setCollapsed(!collapsed)
                        }}>
                          <Tooltip title={collapsed ? '收起菜单' : '折叠菜单'}>
                            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                          </Tooltip>
                        </div>

                        <Breadcrumb className="breadcrumb-container">
                          {extraBreadcrumbItems}
                        </Breadcrumb>

                      </div>
                      <div className="header-right-wrap">
                        <Space>
                          <div className='header-icon-container'>
                            <Badge count={unreadCount} >
                              <Iconfont onClick={() => {
                                setIsShowNotification(true);
                              }} style={{ fontSize: 20 }} type="icon-xiaoxi" ></Iconfont>
                            </Badge>
                          </div>
                          <div className='header-icon-container'>
                            <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
                              {
                                isFullscreen ? <FullscreenExitOutlined onClick={() => {
                                  exitFullscreen();
                                  setIsFullscreen(false);
                                }} className='px-14' /> : <FullscreenOutlined className='px-14' onClick={() => {
                                  enterFullscreen();
                                  setIsFullscreen(true);
                                }} />
                              }
                            </Tooltip>
                          </div>
                        </Space>
                        <div className="user-info-container">

                          <Dropdown menu={{ items: userDropdownItems }} getPopupContainer={(node) => {
                            return node.parentElement || document.body
                          }}>
                            <div className='user-info' onClick={e => e.preventDefault()}>
                              <Iconfont type='icon-yonghu-yuan' style={{ fontSize: 24 }} />
                              <span
                                className='user-name'
                              >
                                {user.pub_name}
                              </span>
                            </div>
                          </Dropdown>
                        </div>
                      </div>
                    </div>
                    <div className='header-tabs'>
                      <NavTabbar />
                    </div>
                  </>

                </Header>
              )}

            {/* <Layout> */}
            <Content className="main-content" style={{ height: 'calc(100vh - 56px)', overflowY: 'auto' }} onScroll={() => {
              handlerScroll()
            }}>
              <div className='main-content-container' style={{ ...transitionStyle }}>
                <SwitchTransition style={{ height: '100%' }} mode="out-in" >
                  <CSSTransition timeout={300} classNames="fade-slide" key={location.pathname} onEnter={() => {
                    setTransitionStyle({
                      ...transitionStyle,
                      overflow: '',
                    })
                  }}
                    onExit={() => {
                      setTransitionStyle({
                        ...transitionStyle,
                        overflow: 'hidden',
                      });
                    }}
                  >
                    <Suspense fallback={<Spin className={'page-loading'} />}>
                      <Switch location={location}>
                        {isNotAuth ? <Route exact path="/" component={Home} /> : <Redirect exact from={'/'} to={getDefaultRoute(auth.authObjTree.pathList[0])} />}

                        {auth.authObjTree.pathList &&
                          auth.authObjTree.pathList.length > 0 &&
                          auth.authObjTree.pathList.map(path => {
                            const { view }: any = getRouteInfo(path);
                            // @ts-ignore
                            return view ? <Route exact key={path} path={path} component={view} /> : <Redirect exact key={path} from={path} to={getDefaultRoute(path)} />;
                          })}
                        {Object.keys(AUTH_MAP).map(path => {
                          if (AUTH_MAP[path]?.isPublic) {
                            return <Route key={path} path={path} component={AUTH_MAP[path].view} />;
                          }
                        })}
                        <Redirect to="/" />
                      </Switch>
                    </Suspense>
                  </CSSTransition>

                </SwitchTransition>
              </div>
            </Content>
          </Layout>
        </Layout>
        // </Layout>
      )}
      <Drawer
        title={localeData.PersionInfo}
        placement="right"
        closable={false}
        onClose={() => {
          setShowUserPop(false);
        }}
        open={showUserPop}
        key="user-info-pop"
        className="user-info-pop"
      >
        <div className="info-item">
          <span className="info-label">ID:</span>
          <span>{user.id}</span>
        </div>
        <div className="info-item">
          <span className="info-label">{localeData.Email}:</span>
          <span>{user.email}</span>
        </div>
        <div className="info-item">
          <span className="info-label">{localeData.UserName}:</span>
          <span>{user.pub_name}</span>
        </div>
        {isSuperAdmin() ? (
          <div className="info-item">
            <span className="info-label">JWT:</span>
            <span>{user.jwt}</span>
          </div>
        ) : null}
      </Drawer>


      {
        window.__INITIAL_STATE__.isLogin && (
          <Notifications
            open={isShowNotification}
            onClose={() => setIsShowNotification(false)}
            onUpdate={() => {
              getNotifyUnreadCount();
            }}
          />
        )
      }
    </ConfigProvider>
  );
}
