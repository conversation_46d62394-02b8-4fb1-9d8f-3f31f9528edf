import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Drawer, Empty, message, Popconfirm, Space, Spin, Tabs } from 'antd'
import './Notifications.scss'
import request from '@/modules/request';
import { timeAgo } from '@@/lib/tool';
import { useHistory } from 'react-router-dom';

interface IProps {
  open: boolean
  onClose: () => void
  onUpdate: () => void
}

const NODE_MAP_TEXT = {
  'node1': (item) => (
    <>
      <strong style={{ marginRight: 2 }}>{item.creator}</strong>
      <span>uploaded 1 leads to the clue pool, please assign BD</span>
    </>
  ),
  'node2': (item: Record<string, any> = {}) => (
    <>
      <strong style={{ marginRight: 2 }}>{item.creator}</strong>
      <span> assigned leads to "{item['affiliate_publisher']?.pub_name}"</span>
    </>
  ),
  'node3': (item: Record<string, any> = {}) => (
    <>
      <strong style={{ marginRight: 2 }}>{item.creator}</strong>
      <span>forwarded leads to "{item['affiliate_publisher']?.pub_name}"</span>
    </>
  ),
  'node6': (item: Record<string, any> = {}) => (
    <>
      <strong style={{ marginRight: 2 }}>{item.other_info?.client_name}</strong>
      <span>The customer has not cooperated for more than 6 months and has been returned to the clue pool.</span>
    </>
  ),
  'node7': (item: Record<string, any> = {}) => ( // 合同相关的
    <>
      {/* 这里不是所有都需要展示creator，所以放在other_info里面 */}
      <strong style={{ marginRight: 2 }}>{item.other_info?.headerStrong ? item.other_info?.applyUserName || '' : ''}</strong>
      <span>{item.other_info?.title}</span>
    </>
  ),
  'node8': (item: Record<string, any> = {}) => ( // 合同聊天记录被艾特
    <>
      <strong style={{ marginRight: 2 }}>{item.creator} </strong>
      <span>{item.other_info?.title}</span>
    </>
  ),
  'node9': (item: Record<string, any> = {}) => ( // 合同聊天记录被艾特
    <>
      <strong style={{ marginRight: 2 }}>{item.creator} </strong>
      <span>{item.other_info?.title}</span>
    </>
  ),
  'common': (item: Record<string, any> = {}) => (
    <>
      <span>{item.other_info?.title}</span>
    </>
  )
}

type NotifyTabType = 'unread' | 'read' | 'all'


export default ({ open, onClose, onUpdate }: IProps) => {
  const history = useHistory();
  const [countInfo, setCountInfo] = useState({
    unread: 0,
    read: 0,
    all: 0
  });
  const [loading, setLoading] = useState(false);
  const [currenTab, setCurrenTab] = useState<NotifyTabType>('unread');
  const [notifyData, setNotifyData] = useState([]);
  const items = [
    {
      label: (
        <div>
          Unread
          <span className='notifications-tab-tag unread'>
            {countInfo.unread}
          </span>
        </div>
      ),
      key: 'unread',
    },
    {
      label: (
        <div>
          Archived
          <span className='notifications-tab-tag archived'>
            {countInfo.read}
          </span>
        </div>
      ),
      key: 'read',
    },
    {
      label: (
        <div>
          All
          <span className='notifications-tab-tag all'>
            {countInfo.all}
          </span>
        </div>
      ),
      key: 'all'
    }
  ];

  const getNotifyCount = async () => {
    const [unreadRes, readRes, allRes] = await Promise.all([
      request.get('/notify/getUnreadCount'),
      request.get('/notify/getReadCount'),
      request.get('/notify/getAllCount'),
    ])

    setCountInfo({
      unread: unreadRes.data,
      read: readRes.data,
      all: allRes.data
    })
  };
  const getNotifyData = async (type: NotifyTabType) => {
    const keyStrategy = {
      'unread': 'getUnreadData',
      'read': 'getReadData',
      'all': 'getAllData'
    }
    const key = keyStrategy[type];
    setLoading(true);
    try {
      const res = await request.get(`/notify/${key}`);
      setNotifyData(res.data);
    } catch { } finally {
      setLoading(false);
    }
  };
  const noticeToDetail = async (item) => {
    const { relation_id, notify_type } = item;

    if (notify_type === 'node1') {
      history.push(`/operation/clue-manage/pool?clue_id=${relation_id}`);
    } else if (['node2', 'node3'].includes(notify_type)) {
      history.push(`/operation/clue-manage/personal?clue_id=${relation_id}`);
    } else if (['node6'].includes(notify_type)) {
      const otherInfo = item.other_info;
      history.push(`/operation/clue-manage/personal?client=${otherInfo?.client_name}`);
    } else if (['node7', 'node8'].includes(notify_type)) {
      const otherInfo = item.other_info;
      const isAudit = otherInfo.isAudit
      history.push(`/operation/contract-manage/${isAudit ? 'approval' : 'owned'}?id=${relation_id}`);
    } else if (notify_type === 'node8') {
      const otherInfo = item.other_info;
      const isAudit = otherInfo.isAudit
      history.push(`/operation/contract-manage/${isAudit ? 'whole' : 'owned'}?id=${relation_id}`);
    } else if (notify_type === 'node9') {
      history.push(`/operation/contract-manage/owned?id=${relation_id}`);
    } else if (notify_type === 'common') {
      const otherInfo = item.other_info;
      const path = otherInfo.path;
      history.push(path);
    } else {
      return;
    }

    request.put(`/noticePool/${item.id}`, { status: 1 }).then(() => {
      getNotifyData(currenTab);
      getNotifyCount();
      onUpdate(); // 通知外面更新
    })
  }
  useEffect(() => {
    getNotifyCount();
    getNotifyData('unread')
  }, [])

  useEffect(() => {
    if (open) {
      getNotifyCount();
      getNotifyData(currenTab);
    }
  }, [open]);

  useEffect(() => {
    getNotifyData(currenTab)
  }, [currenTab])
  return (
    <Drawer
      title="Notifications"
      width={420}
      className='notifications-container'
      onClose={onClose}
      open={open}
      footer={
        <Popconfirm title="Are you sure you want to set all notifications to read?" okText="Confirm" onConfirm={async () => {
          const res = await request.post('/notify/viewAll');
          message.success(res.msg);
          getNotifyData(currenTab);
          getNotifyCount();
          onUpdate(); // 通知外面更新
        }}>
          <div className='view-all-btn'>View All</div>
        </Popconfirm>

      }
    >
      <div className='notifications-container-content'>
        <Tabs className='notifications-tabs' defaultActiveKey='unread' activeKey={currenTab} items={items} onChange={(e: any) => {
          setNotifyData([]);
          setCurrenTab(e);
        }} />

        <div className='notifications-list-container'>
          <Spin spinning={loading}>
            {
              // 让loading 出现在中间
              (notifyData.length === 0 && loading) && (
                <div style={{ marginTop: 100 }} ></div>
              )
            }
            {
              (notifyData.length === 0 && !loading) && (
                <Empty style={{ marginTop: 100 }} />
              )
            }
            {
              notifyData.map((item: any) => {
                return (
                  <div className='notifications-item'>
                    <div className='notifications-item-avatar'>
                      <Avatar>{item.creator}</Avatar>
                    </div>
                    <div className='notifications-item-content'>
                      <div className='notifications-item-content-root'>
                        <div className='notifications-item-content-title'>
                          {NODE_MAP_TEXT[item.notify_type]?.(item)}
                        </div>
                      </div>
                      <div className='notifications-item-content-time'>
                        {timeAgo(item.ctime)}
                      </div>
                      {
                        item.remark && (
                          <div className='notifications-item-content-note'>
                            <strong style={{ marginRight: 2 }}>Note:</strong>
                            {item.remark}
                          </div>
                        )
                      }
                      <div className='notifications-item-content-action' style={{ marginTop: item.note ? '0' : '8px' }}>
                        <Space>


                          <Button onClick={() => {
                            noticeToDetail(item)
                          }} >To Detail</Button>

                        </Space>

                      </div>

                    </div>
                  </div>
                )
              })
            }

          </Spin>
        </div>
      </div>

    </Drawer>
  )
}
