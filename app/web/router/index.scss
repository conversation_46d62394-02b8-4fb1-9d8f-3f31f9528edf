.app-layout-sidebar .ant-layout-sider-children {
  height: 100%;
  display: flex;
  flex-direction: column;

  .ant-menu {
    .ant-menu-item {
      padding-left: 36px !important;
    }

  }

  .ant-menu-item-selected {
    background-color: transparent;
    position: relative;
    color: var(--ant-primary-color);
    z-index: 2;

    &::after {
      content: "";
      position: absolute;
      left: 8px;
      right: 8px;
      background-color: var(--n-item-color-active);
      border-radius: 6px;
      border-right: 0;
      z-index: -1;
    }
  }

  .app-layout-sidebar-menu-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;


    .ant-menu-root:not(.ant-menu-inline-collapsed) {
      .ant-menu-submenu-title {
        padding: 0 18px !important;
        background-color: #fafafa;
      }
    }


    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.12);
      border-radius: 3px;
      box-shadow: inset 0 0 5px rgba(0, 21, 41, 0.05);
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.06);
      border-radius: 3px;
      box-shadow: inset 0 0 5px rgba(0, 21, 41, 0.05);
    }
  }

  .app-layout-sidebar-extra-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 40px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-top: 4px;
    margin-bottom: 4px;
    padding: 0 16px;
    overflow: hidden;


  }
}

.ant-layout {
  background-color: transparent;

  &.fundraiser-wrap {
    height: 100%;
  }

  .ant-breadcrumb a {
    color: #767c82;
  }

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    margin-bottom: 6px;

    .logo-txt {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      height: 43px;

      &>.logo-icon {
        width: 36px;
        height: 36px;
        background-image: url(../asset/images/logo.png);
        background-size: 100% 100%;
      }
    }

    .logo-hr {
      width: 1px;
      height: 26px;
      background-color: rgba(255, 255, 255, 0.35);
      left: 10px;
      position: relative;
    }

    .app-name {
      font-size: 18px;
      color: var(--ant-primary-color);
      font-weight: 700;
      width: 130px;
      margin-left: 8px;
      text-align: left;
      line-height: 1.1;
      // font-family: v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

      &.small-size {
        font-size: 16px;
      }
    }
  }


  .main-content {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, .5) transparent;
    transition: all .3s;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    background-color: rgb(247, 250, 252);

    .main-content-container {
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
      padding: 16px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .header-right-wrap {
    height: 36px;

    .px-14 {
      padding: 0 14px;
    }

    .anticon {
      color: var(--n-text-color);
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      transition: font-size .2s var(--n-bezier), color .2s var(--n-bezier);
    }
  }

  .user-info-container {
    height: 100%;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 100%;
    transition: color .3s cubic-bezier(.4, 0, .2, 1),
      background-color .3s cubic-bezier(.4, 0, .2, 1),
      opacity .3s cubic-bezier(.4, 0, .2, 1),
      border-color .3s cubic-bezier(.4, 0, .2, 1);
    border-radius: 6px;
    padding: 0 14px;
    cursor: pointer;

    &:hover {
      background-color: rgba(46, 51, 56, .09);
      color: var(--n-text-color);
    }

    .user-name {
      font-size: 16px;
      font-weight: 500;
    }

    .user-logout .anticon {
      color: var(--n-text-color);
    }
  }

  .breadcrumb-container {
    font-weight: 400;

    .anticon {
      font-size: 18px;
      margin-right: 4px;
    }

    li {
      display: flex;
      align-items: center;
    }

    .ant-breadcrumb-link {
      a {
        display: flex;
        align-items: center;
      }
    }
  }
}


.fade-slide-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s;
}

.fade-slide-exit {
  opacity: 1;
  transform: translateX(0);
}

.fade-slide-exit-active {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.3s;
}

.header-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color .2s var(--n-bezier), font-size .2s var(--n-bezier);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(99, 115, 129, 0.08);

    .anticon {
      font-size: 21px !important;
    }
  }
}
