{"extends": "../../config/tsconfig.json", "compilerOptions": {"jsx": "preserve", "target": "es5", "module": "esnext", "sourceMap": true, "lib": ["es6", "dom", "es2017", "esnext"], "baseUrl": ".", "paths": {"@/*": ["*"], "@@/*": ["../*"], "request": ["./modules/request.ts"]}}, "files": ["./typings/global.d.ts"], "include": ["node_modules/@flat/**/*.tsx", "./page/**/**.tsx", "typings/*.ts"], "exclude": ["node_modules", "**/*.spec.ts"]}