import request from '@/modules/request';
import { CONTRACT_DATA_TYPE } from '@@/lib/constant';
import { create } from 'zustand';

interface NotificationStore {
  unreadCount: number
  setup: (payload: Omit<NotificationStore, 'setup' | 'setUnreadCount' | 'setContractPendingCount'>) => void;
  setUnreadCount: (newValue: number) => void;
  setContractPendingCount: (newValue: number) => void;

  contractPendingCount: number
};

export const getUnreadCount = async () => {
  const activityRes = await request.get('/notify/getUnreadCount');
  return activityRes.data;
}

export const getContractPendingCount = async () => {
  const res = await request.get('/contract/getContractCount', { dataType: CONTRACT_DATA_TYPE.PENDING })
  return res.data?.total || 0
}

const useNotificationStore = create<NotificationStore>((set, get) => ({
  unreadCount: 0,
  contractPendingCount: 0,
  setup: (payload) => {
    set(() => ({ ...payload }));
  },
  setUnreadCount: (newValue) => {
    set((state) => ({ unreadCount: newValue }));
  },
  setContractPendingCount: (newValue) => {
    set((state) => ({ contractPendingCount: newValue }));
  },
}));

export default useNotificationStore;
