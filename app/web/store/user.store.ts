import { create } from 'zustand';

interface UserStore {
  dbId?: number;
  id: string;
  // 钉钉用户id
  dingUserId: string;
  email: string;
  username: string;
  nickname: string;
  realname: string;
  team: string;
  avatar: string;
  // 用户支持的审核类型，只在合同审批且针对 资产组，财务，税务，法务有效
  crmAuditType: string[]
  workPlace: string;
  // 用户角色
  roleCode: string[];
  // 路由操作权限
  routeOperateAuths: Map<string, string[]>;
  // 路由可访问权限
  routeAccessAuths: Map<string, Record<string, any>>;
  bd_member: string;
  op_member: string;
  setup: (payload: Omit<UserStore, 'setup' | 'setAuthProjectObjs' | 'setRouteOperateAuths' | 'setRouteAccessAuths'>) => void;
  setRouteOperateAuths: (newValue) => void;
  setRouteAccessAuths: (newValue) => void;
}

const useUserStore = create<UserStore>((set, get) => ({
  id: '',
  dingUserId: '',
  email: '',
  username: '',
  nickname: '',
  realname: '',
  team: '',
  workPlace: '',
  avatar: '',
  crmAuditType: [],
  roleCode: [],
  authProjectObjs: [],
  routeOperateAuths: new Map(),
  routeAccessAuths: new Map(),
  bd_member: '',
  op_member: '',
  setup: (payload) => {
    set(() => ({ ...payload }));
  },
  setRouteOperateAuths: (newValue) => {
    set((state) => ({ routeOperateAuths: newValue }));
  },
  setRouteAccessAuths: (newValue) => {
    set((state) => ({ routeAccessAuths: newValue }));
  }
}));

export default useUserStore;
