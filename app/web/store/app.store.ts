import request from '@/modules/request';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const getCustomer = async () => {
  const customerRes = await request.get('/commonFilter/getCustomer');
  return customerRes.data;
}

export const getActivity = async () => {
  const activityRes = await request.get('/commonFilter/getActivity');
  return activityRes.data;
}

export const getEnums = async () => {
  const enumsRes = await request.get('/commonFilter/getEnums');
  return enumsRes.data;
}

export const getSystemAccounts = async () => {
  const res = await request.get('/omsUser', {
    pageIndex: 1,
    pageSize: 9999
  });
  const data = res.data || [];
  const result = data.map(it => ({
    label: it.pub_name,
    value: it.id
  }));
  return result;
}

export const getCountrys = async () => {
  const enumsRes = await request.get('/commonFilter/getCountrys');
  return enumsRes.data;
}



interface ICommonRecord {
  label: string
  value: number | string
}

interface IRoleUser extends ICommonRecord {
  aud_type: string[]
}

type IPayload = 'setup'
  | 'SET_OP_USERS'
  | 'SET_BD_USERS'
  | 'SET_BD_LEADER_USERS'
  | 'SET_OP_LEADER_USERS'
  | 'SET_BD_ASSISTANT_USERS'
  | 'SET_MKT_USERS'
  | 'SET_FIN_USERS'
  | 'SET_LEGAL_USERS'
  | 'SET_ACTIVITYS'
  | 'SET_ENUMS'

interface IEnums {
  lead_source: ICommonRecord[],
  lead_industry: ICommonRecord[],
  lead_invalid_clue: ICommonRecord[],
  lead_conversion_failure: ICommonRecord[],
  lead_mismatch: ICommonRecord[]
  customer_department: ICommonRecord[]
  pay_type: ICommonRecord[]
  lead_other_reason: ICommonRecord[]
}

interface AppStore {
  // 系统初始化是否完成
  isMetadataReady: boolean;

  // SUPER ADMIN
  SUPER_ADMIN_USERS: IRoleUser[]
  // ADMIN
  ADMIN_USERS: IRoleUser[]

  // 上游运营人员列表
  ADV_OP_USERS: IRoleUser[]
  // 下游运营人员列表
  PUB_OP_USERS: IRoleUser[]

  // 上游商务人员列表
  ADV_BD_USERS: IRoleUser[]
  // 下游商务人员列表
  PUB_BD_USERS: IRoleUser[]

  // 运营人员列表
  OP_USERS: IRoleUser[]
  // 商务人员列表
  BD_USERS: IRoleUser[]
  // 商务领导列表
  BD_LEADER_USERS: IRoleUser[]
  // 运营领导列表
  OP_LEADER_USERS: IRoleUser[]
  // 商务助理列表
  BD_ASSISTANT_USERS: IRoleUser[]
  // 市场人员列表
  MKT_USERS: IRoleUser[]
  // 财务人员列表
  FIN_USERS: IRoleUser[]
  // 税务人员列表
  TAX_USERS: IRoleUser[]
  // 法务人员列表
  LEGAL_USERS: IRoleUser[]
  // 资产组人员列表
  ASSET_USERS: IRoleUser[]
  // 程序化运营人员列表
  PROGRAMMATIC_OP_USERS: IRoleUser[]

  // 审核人员列表
  ALL_AUDIT_USERS: IRoleUser[]

  setup: (payload: Omit<Partial<AppStore>, IPayload>) => void;
  REFRESH_DATA: (key: 'customer') => void;

  SET_SUPER_ADMIN_USERS: (payload: IRoleUser[]) => void;
  SET_ADMIN_USERS: (payload: IRoleUser[]) => void;
  SET_OP_USERS: (payload: IRoleUser[]) => void;
  SET_BD_USERS: (payload: IRoleUser[]) => void;
  SET_BD_LEADER_USERS: (payload: IRoleUser[]) => void;
  SET_OP_LEADER_USERS: (payload: IRoleUser[]) => void;
  SET_BD_ASSISTANT_USERS: (payload: IRoleUser[]) => void;
  SET_MKT_USERS: (payload: IRoleUser[]) => void;
  SET_FIN_USERS: (payload: IRoleUser[]) => void;
  SET_TAX_USERS: (payload: IRoleUser[]) => void;
  SET_LEGAL_USERS: (payload: IRoleUser[]) => void;
  SET_ASSET_USERS: (payload: IRoleUser[]) => void;
  SET_PROGRAMMATIC_OP_USERS: (payload: IRoleUser[]) => void;
  SET_ALL_AUDIT_USERS: (payload: IRoleUser[]) => void;
  hasStorage: (key: string) => boolean;


  // 下面是公共筛选项的
  ACTIVITYS: ICommonRecord[]
  SET_ACTIVITYS: (payload: ICommonRecord[]) => void
  ENUMS: IEnums,
  SET_ENUMS: (payload: IEnums) => void
  CUSTOMER: ICommonRecord[]
  SET_CUSTOMER: (payload: ICommonRecord[]) => void
  ACCOUNTS: ICommonRecord[]
  SET_ACCOUNTS: (payload: ICommonRecord[]) => void
  COUNTRYS: ICommonRecord[]
  SET_COUNTRYS: (payload: ICommonRecord[]) => void
}

const useAppStore = create(
  persist<AppStore>(
    (set, get) => ({
      isMetadataReady: false,
      ADV_OP_USERS: [],
      PUB_OP_USERS: [],
      SUPER_ADMIN_USERS: [],
      ADMIN_USERS: [],
      OP_USERS: [],
      ADV_BD_USERS: [],
      PUB_BD_USERS: [],
      BD_USERS: [],
      BD_LEADER_USERS: [],
      OP_LEADER_USERS: [],
      BD_ASSISTANT_USERS: [],
      MKT_USERS: [],
      FIN_USERS: [],
      TAX_USERS: [],
      LEGAL_USERS: [],
      ASSET_USERS: [],
      PROGRAMMATIC_OP_USERS: [],
      ALL_AUDIT_USERS: [],
      setup: payload => {
        set(() => ({ ...payload }));
      },
      REFRESH_DATA: async key => {
        if (key === 'customer') {
          const data = await getCustomer();
          set(() => ({ CUSTOMER: data }));
        }
      },
      SET_OP_USERS: payload => {
        set(() => ({ OP_USERS: payload }));
      },
      SET_BD_USERS: payload => {
        set(() => ({ BD_USERS: payload }));
      },
      SET_BD_LEADER_USERS: payload => {
        set(() => ({ BD_LEADER_USERS: payload }));
      },
      SET_OP_LEADER_USERS: payload => {
        set(() => ({ OP_LEADER_USERS: payload }));
      },
      SET_BD_ASSISTANT_USERS: payload => {
        set(() => ({ BD_ASSISTANT_USERS: payload }));
      },
      SET_MKT_USERS: payload => {
        set(() => ({ MKT_USERS: payload }));
      },
      SET_FIN_USERS: payload => {
        set(() => ({ FIN_USERS: payload }));
      },
      SET_TAX_USERS: payload => {
        set(() => ({ TAX_USERS: payload }));
      },
      SET_LEGAL_USERS: payload => {
        set(() => ({ LEGAL_USERS: payload }));
      },
      SET_ASSET_USERS: payload => {
        set(() => ({ ASSET_USERS: payload }));
      },
      SET_PROGRAMMATIC_OP_USERS: payload => {
        set(() => ({ PROGRAMMATIC_OP_USERS: payload }));
      },
      SET_ALL_AUDIT_USERS: payload => {
        set(() => ({ ALL_AUDIT_USERS: payload }));
      },
      SET_SUPER_ADMIN_USERS: payload => {
        set(() => ({ SUPER_ADMIN_USERS: payload }));
      },
      SET_ADMIN_USERS: payload => {
        set(() => ({ ADMIN_USERS: payload }));
      },
      hasStorage(key: string) {
        const storage = window.localStorage.getItem(useAppStore.persist.getOptions().name as string);
        if (storage) {
          const storageInfo = JSON.parse(storage);
          const value = Reflect.get(storageInfo.state, key);
          return !!value;
        }
        return false;
      },

      // 下面是公共筛选项的
      ACTIVITYS: [],
      SET_ACTIVITYS: payload => {
        set(() => ({ ACTIVITYS: payload }));
      },
      ENUMS: {
        lead_source: [],
        lead_industry: [],
        lead_invalid_clue: [],
        lead_conversion_failure: [],
        lead_mismatch: [],
        customer_department: [],
        pay_type: [],
        lead_other_reason: []
      },

      SET_ENUMS: payload => {
        set(() => ({ ENUMS: payload }));
      },

      CUSTOMER: [],
      SET_CUSTOMER: payload => {
        set(() => ({ CUSTOMER: payload }));
      },

      ACCOUNTS: [],
      SET_ACCOUNTS: payload => {
        set(() => ({ ACCOUNTS: payload }));
      },
      COUNTRYS: [],
      SET_COUNTRYS: payload => {
        set(() => ({ COUNTRYS: payload }));
      }
    }),
    {
      name: '$$app-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state =>
      ({

      } as any)
    }
  )
);
//
// // 监听storage事件，数据发生变化时，重新对数据进行水合
// withStorageDOMEvents(useAppStore);
export default useAppStore;
