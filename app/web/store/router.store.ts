import { create } from 'zustand';

export type PathTo = string;
export type PathState = Record<PathTo, any>;
export interface Route {
  // 路由名
  name: string;
  // 路由路径
  path: string;
}

interface RouterStoreState {
  routeHistory: Route[];
  routeHistorySet: Set<string>;
  routeState: Map<PathTo, PathState | null>;
}

interface RouterStoreAction {
  setRouteState: (path: PathTo, state?: PathState) => void;
  removeRouteState: (path: PathTo) => void;
  addRouteHistory: (route: Route) => void;
  deleteRouteHistory: (path: string) => void;
}

const useRouterStore = create<RouterStoreState & RouterStoreAction>(
  (set, get) => ({
    routeHistory: [],
    routeHistorySet: new Set(),
    routeState: new Map(),
    setRouteState(path: PathTo, state?: PathState) {
      const {routeState} = get();
      routeState.set(path, state || null);
      set({
        routeState
      });
    },
    removeRouteState(path: PathTo) {
      const {routeState} = get();
      routeState.delete(path);
      set({
        routeState
      });
    },
    addRouteHistory(route: Route) {
      const {routeHistory, routeHistorySet} = get();
      const {name, path} = route;
      if (!routeHistorySet.has(name)) {
        routeHistorySet.add(name);
        routeHistory.push(route);
      } else {
        const index = routeHistory.findIndex(route => route.name === name);
        if (index !== -1  && routeHistory[index].path !== path) {
          routeHistory[index] = route;
        }
      }
      set({
        routeHistory: [...routeHistory],
        routeHistorySet: new Set(routeHistorySet)
      });
    },
    deleteRouteHistory(path) {
      const {routeHistory, routeHistorySet} = get();
      const index = routeHistory.findIndex(route => route.path === path);
      if (index !== -1) {
        const item = routeHistory.splice(index, 1);
        routeHistorySet.delete(item[0].name);
      }
      set({
        routeHistory: [...routeHistory],
        routeHistorySet: new Set(routeHistorySet)
      });
    }
  })
);

export default useRouterStore;