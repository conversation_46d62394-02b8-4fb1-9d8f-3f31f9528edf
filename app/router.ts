import { Application } from 'egg';
export default (app: Application) => {
  const { router, controller } = app;

  router.get('/api/customer-refresh/refreshClientCtime', controller.customerManage.pool.refreshClientCtime);

  // 用户相关接口
  router.get('/api/user/getByRoleUsers', controller.authority.user.getByRoleUsers);

  // 通知用户角色权限已授予
  router.post('/api/dingtalk/notifyUserRoleAuthIsApprove', controller.commom.notifyUserRoleAuthIsApprove);
  /** 调用后端接口 */
  router.post('/api/curlPostProxy', controller.commom.post);
  router.get('/api/curlGetProxy', controller.commom.get);

  // 公共筛选项接口
  router.get('/api/commonFilter/getActivity', controller.commom.getActivity);
  router.get('/api/commonFilter/getCustomer', controller.commom.getCustomer);
  router.get('/api/commonFilter/getEnums', controller.commom.getEnums);
  router.get('/api/commonFilter/getCountrys', controller.commom.getCountrys);
  router.post('/api/commonFilter/createEnum', controller.commom.createEnum);

  // 公共接口
  router.resources('upload', '/api/upload', controller.upload);
  router.post('/api/upload/editorUploadImage', controller.upload.editorUploadImage);
  router.resources('log', '/api/log', controller.operateLog);
  router.get('/api/log/getFilters', controller.operateLog.getFilters);
  router.post('/api/custom-log', controller.operateLog.addCustomLog);
  router.get('/api/log/operator-list', controller.operateLog.operatorList);
  router.get('/api/log/action-list', controller.operateLog.actionList);
  // 大文件上传, 获取 token key
  router.get('/api/get-upload-key', controller.upload.getUploadKey);

  // 权限相关
  router.resources('omsUser', '/api/omsUser', controller.authority.user);
  router.resources('authority', '/api/authority', controller.authority.authority);
  router.resources('role', '/api/role', controller.authority.role);
  router.get('/api/getAdsUser', controller.authority.user.getAdsUser);
  router.put('/api/roleAuth/bulkUpdate', controller.authority.roleAuth.bulkUpdate);
  router.resources('roleAuth', '/api/roleAuth', controller.authority.roleAuth);
  router.resources('userRole', '/api/userRole', controller.authority.userRole);
  router.get('/api/oms-user-role-list', controller.authority.role.roleList);
  // 钉钉相关接口
  router.post('/api/ddCloud/settingDDUserIds', controller.ddCloud.settingDDUserIds);
  // router.get('/api/ddCloud/settingCorpSpaceRole', controller.ddCloud.settingCorpSpaceRole);
  router.get('/api/ddCloud/settingUserPermission', controller.ddCloud.settingUserPermission);
  router.post('/api/ddCloud/completeTodo', controller.ddCloud.completeTodo);
  // mc
  router.resources('proxy', '/api/proxy/mc', controller.mc);

  // 活动管理
  router.resources('activity', '/api/activity/record', controller.activityManage.record);
  router.post('/api/activity/record/export', controller.activityManage.record.export);
  // 线索管理
  router.resources('clue', '/api/clue', controller.clueManage.pool);
  router.resources('clue-roi', '/api/clue-roi', controller.clueManage.roi);
  router.post('/api/clue/batchDestory', controller.clueManage.pool.batchDestory);
  router.post('/api/clue/batchLeadAssignment', controller.clueManage.pool.batchLeadAssignment);
  router.post('/api/clue/associatedCustomer', controller.clueManage.pool.associatedCustomer);
  router.post('/api/clue/conversionCustomer', controller.clueManage.pool.conversionCustomer);
  router.post('/api/clue/clueFailureHandler', controller.clueManage.pool.clueFailureHandler);
  router.post('/api/clue/clueStatusToContacted', controller.clueManage.pool.clueStatusToContacted);
  router.post('/api/clude/changeToUnassigned', controller.clueManage.pool.changeToUnassigned);
  router.post('/api/clue/importLeadDataAndVerify', controller.clueManage.pool.importLeadDataAndVerify);
  router.post('/api/clue/disparkCreate', controller.clueManage.pool.disparkCreate);
  router.post('/api/clue/runSchedule', controller.clueManage.pool.runSchedule);
  router.post('/api/clue/runNotifySchedule', controller.clueManage.pool.runNotifySchedule);
  router.get('/api/clue/runNotifySchedule', controller.clueManage.pool.runNotifySchedule);
  router.post('/api/clue/export', controller.clueManage.pool.export);
  router.post('/api/clue-roi/export', controller.clueManage.roi.export);
  router.get('/api/clue/getClueCompany', controller.clueManage.pool.getClueCompany);
  // 客户管理
  router.resources('customer', '/api/customer', controller.customerManage.pool);
  router.post('/api/customer/clientUpdateMasterCueBD', controller.customerManage.pool.clientUpdateMasterCueBD);
  router.post('/api/customer/export', controller.customerManage.pool.export);
  router.post('/api/customer/clientDeleteRelationship', controller.customerManage.pool.clientDeleteRelationship);
  router.post('/api/customer/createClueAndCustomer', controller.customerManage.pool.createClueAndCustomer);
  router.get('/api/getCustomers', controller.customerManage.pool.getCustomers);

  // 合同管理
  router.get('/api/contract/getContractCount', controller.contractManage.index.getContractCount);
  router.resources('contract', '/api/contract', controller.contractManage.index);
  router.resources('contractChat', '/api/contractChat', controller.contractManage.chat);
  router.get('/api/contractChat/getChatCount', controller.contractManage.chat.getChatCount);
  router.get('/api/test-api', controller.contractManage.index.testApi);
  router.post('/api/contract/updateRelevanceClients', controller.contractManage.index.updateRelevanceClients);
  router.post('/api/contract/contractTransfer', controller.contractManage.index.contractTransfer);
  router.post('/api/contract/updateSharedUser', controller.contractManage.index.updateSharedUser);
  // 消息通知
  router.resources('notice', '/api/noticePool', controller.systemNotify.notify);
  router.get('/api/notify/getUnreadCount', controller.systemNotify.notify.getUnreadCount);
  router.get('/api/notify/getReadCount', controller.systemNotify.notify.getReadCount);
  router.get('/api/notify/getAllCount', controller.systemNotify.notify.getAllCount);
  router.get('/api/notify/getUnreadData', controller.systemNotify.notify.getUnreadData);
  router.get('/api/notify/getReadData', controller.systemNotify.notify.getReadData);
  router.get('/api/notify/getAllData', controller.systemNotify.notify.getAllData);
  router.post('/api/notify/viewAll', controller.systemNotify.notify.viewAll);
  // 跟进记录
  router.resources('followUpRecord', '/api/followUpRecord', controller.followUpRecord.index);
  router.post('/api/followUpRecord/export', controller.followUpRecord.index.export);
  router.post('/api/followUpRecord/importLeadDataAndVerify', controller.followUpRecord.index.importLeadDataAndVerify);

  // 系统应用
  router.get('/logout', controller.authority.user.logout);

  // 待办管理
  router.resources('crmTodoManager', '/api/crmTodoManager/record', controller.crmTodoManager.index);
  router.get('/api/crmTodo/overallData', controller.crmTodoManager.index.getOverallData);

  // 提成报表
  router.resources('commissionReport', '/api/commission/report', controller.commissionReport.index);
  router.get('/api/commissionReport/getFilters', controller.commissionReport.index.getFilter);
  router.post('/api/commissionReport/export', controller.commissionReport.index.export);
  // @ts-ignore
  router.post('/api/passport/local', app.passport.authenticate('local', { successRedirect: '/authCallback', failureRedirect: '/authCallback' }));
  router.get('/authCallback', controller.authority.admin.authCallback);
  router.get('/', controller.authority.admin.index);
  router.get('/:main/:sub?/:sub?', controller.authority.admin.index);

};
