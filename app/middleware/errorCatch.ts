import { Context } from 'egg';
import { AppConfig } from '../lib/constant';

export default function errCatch(): any {
  return async (ctx: Context, next: () => Promise<any>) => {
    const start = new Date().getTime(); // 开始计时
    const { service, locals } = ctx;
    const { env } = locals;
    // 超时日志打点
    const overTimeLog = () => {
      const path = (ctx.path === '/') ? ctx.query.template : ctx.path;
      const isApiRouter = /\/api\//.test(path);
      const rs = Math.ceil(new Date().getTime() - start);
      if (isApiRouter && rs >= 30000) { // 大于30秒则打印日志
        const protocol = ctx.protocol.toUpperCase();
        const status = ctx.status;
        const length = ctx.length || '-';
        const referrer = ctx.get('referrer') || '-';
        // 打印的log（自定义）
        const message = `fetch long time rs:${rs}, protocol:${protocol}, status:${status},  length:${length}, referrer:${referrer}`;
        ctx.logger.error(message);
      }
    };

    try {
      await next();
      overTimeLog();
    } catch (err) {
      const requestObj = ctx.request;
      const userName = ctx.user?.pub_name || '';
      const id = ctx.user?.id || '';
      const url = `${requestObj.origin}${requestObj.path}`;
      const errMsg = `error:\n\n env: ${ctx.locals.env}\n\n system: ${AppConfig.name} \n\n user: ${id} - ${userName}\nurl: ${url}\nerror: ${err} \n}`;
      ctx.logger.error(`err: ${err}`);
      if (env && env !== 'local') {
        const params = {
          msgtype: 'text',
          text: {
            content: errMsg
          }
        };
        await service.dingtalk.index(params);
      }
      overTimeLog();
      ctx.body = {
        data: null,
        msg: `err: ${err}`,
        isSuccess: false,
        code: 500
      };
    }
  };
}
