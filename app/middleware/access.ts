import { Context } from 'egg';

export default function (): any {
  return async (ctx: Context, next: () => Promise<any>) => {
    const { app, path } = ctx;
    const method = ctx.method.toLowerCase();
    if (path.startsWith('/api') && method !== 'get') {
      const { body } = ctx.request;
      const { query } = ctx;
      const { pub_name } = ctx.user || {};
      const name = pub_name;

      if (['post'].includes(method)) {
        setSign('creator');
      }

      if (['delete', 'put'].includes(method)) {
        setSign('modifier');
      }

      function setSign(text) {
        if (body && Object.keys(body).length) {
          if (Array.isArray(body)) {
            body.forEach(item => {
              item[text] = name;
            });
          } else {
            body[text] = name;
          }
        } else if (query && Object.keys(query).length) {
          query[text] = name;
        }
      }
    }
    await next();
  };
}
