import { Context } from 'egg';

export default function locals(): any {
  return async (ctx: Context, next: () => Promise<any>) => {
    const {
      app: { config }
    } = ctx;

    // multi-language start
    // let lang = ctx.__getLocale();
    let lang = config.i18n.defaultLocale;
    await ctx.cookies.set('lang', lang, {
      httpOnly: false
    });
    let localeData = ctx.app.localesCache[lang];
    if (!localeData) {
      ctx.logger.warn(`cannot found ${lang} locals`);
      lang = 'en';
      localeData = ctx.app.localesCache[lang];
    }
    ctx.locals.lang = lang;
    ctx.locals.localeData = localeData;
    // multi-language end

    ctx.locals.env = config.env;
    ctx.locals.appId = config?.[ctx?.query?.corp]?.appId || config?.dingtalk?.appId;
    ctx.locals.query = { ...ctx.query };

    await next();
  };
}
