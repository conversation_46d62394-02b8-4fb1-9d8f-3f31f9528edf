import { Context } from 'egg';

// 鉴权白名单
const whiteList = [
  '/logout',
  '/api/passport/local',
  '/authCallback',
  '/api/system-update-notification/connect',
  '/api/system-update-notification/update-start',
  '/api/system-update-notification/update-finish',
  '/api/system-update-notification/verify',
  '/api/clue/disparkCreate'
];

export default function locals(): any {
  return async (ctx: Context, next: () => Promise<any>) => {
    const { app, path } = ctx;

    if (!ctx.isAuthenticated() && !whiteList.some(pathPrefix => path.startsWith(pathPrefix))) {
      if (path.startsWith('/api')) {
        // 已退出登录的标记
        ctx.body = {
          msg: 'Unauthorized',
          isSuccess: false,
          code: 403
        };
        return;
      } else {
        // 未登录，访问非登录页面跳到登录页面
        if (path !== '/login' && path !== '/page/ddlogin') {
          const url = ctx.url;
          let searchParams = '';
          if (url.includes('?')) {
            searchParams = url.split('?')[1];
          }
          return searchParams ? ctx.redirect(`/login?${searchParams}&from=${path}`) : ctx.redirect(`/login`);
        }
      }
    }

    await next();
  };
}
