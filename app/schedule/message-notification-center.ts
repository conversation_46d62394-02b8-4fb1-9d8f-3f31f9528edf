import { Subscription } from 'egg';
import moment from 'moment';
import { Op } from 'sequelize';

// 每天早上10点执行
export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 5 10 * * *', // 每天早上10点05分执行
      type: 'worker',
      env: ['prod'], // 只在生产运行
    };
  }
  /**
   * 合并通知，每天一次，每天早上10点，通知bd leader 昨天录入的线索
   * 点击跳线索池，筛选【待分配】
   */
  async node1Handler() {
    // 判断是否是周一，三，五
    const date = new Date().getDay();
    if ([1, 3, 5].includes(date)) {
      // 若当天，有线索通知重复，则不再触发此线索通知（避免打扰）目前已有线索通知：不再通知「昨日未分配」
      const threeDayAgo = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      const ctimeThanThreeClue = await this.service.clueManage.pool.findOne({
        where: {
          ctime: {
            [Op.lte]: threeDayAgo
          },
          clue_status: 'unassigned'
        },
        raw: true,
      });
      if (ctimeThanThreeClue) { return; }
    }
    const notifys = await this.service.systemNotify.notify.getKeyNodeNotify({
      notify_type: 'node1',
      relation_type: 'clue',
      group: ['receiver_id']
    });
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/clue-manage/pool?status=unassigned`;
    for (const item of notifys) {
      const { receiver_id, count } = item;
      const dUserId = item['affiliate_publisher.d_user_id'];
      const dUnionId = item['affiliate_publisher.d_union_id'];
      if (!dUnionId) { continue; }
      // 发送待办
      await this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】：${count} new leads added to leads pool, please assign BD`,
        jump_url: jumpUrlOrigin
      });
    }
  }
  /**
   * 节点2：商务负责人分配线索给BD 通知BD 本人
   */
  async node2Handler() {
    const notifys = await this.service.systemNotify.notify.getKeyNodeNotify({
      notify_type: 'node2',
      relation_type: 'clue',
      group: ['receiver_id']
    });
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/clue-manage/personal?status=assigned`;

    for (const item of notifys) {
      const { count } = item;
      const dUserId = item['affiliate_publisher.d_user_id'];
      const dUnionId = item['affiliate_publisher.d_union_id'];
      const pubName = item['affiliate_publisher.pub_name'];
      if (!dUnionId) { continue; }
      // 发送待办
      await this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】："${item.creator}" assigned ${count} leads to "${pubName}"`,
        jump_url: jumpUrlOrigin
      });
    }
  }

  /**
   *  节点3： BD之间转让线索，通知被转让的BD本人
   */
  async node3Handler() {

    const notifys = await this.service.systemNotify.notify.getKeyNodeNotify({
      notify_type: 'node3',
      relation_type: 'clue',
      group: ['receiver_id']
    });
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/clue-manage/personal?status=assigned`;
    for (const item of notifys) {
      const { count } = item;
      const dUserId = item['affiliate_publisher.d_user_id'];
      const dUnionId = item['affiliate_publisher.d_union_id'];
      const pubName = item['affiliate_publisher.pub_name'];
      if (!dUnionId) { continue; }
      // 发送待办
      await this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】："${item.creator}" forwarded ${count} leads to "${pubName}"`,
        jump_url: jumpUrlOrigin
      });
    }
  }

  /**
   * 节点6： 客户回退到线索池通知
   */
  async node6Handler() {

    const notifys = await this.service.systemNotify.notify.getKeyNodeNotify({
      notify_type: 'node6',
      relation_type: 'clue',
      group: '' // 需要单个通知，不分组
    });
    for (const item of notifys) {
      const { count, relation_id } = item;
      const dUserId = item['affiliate_publisher.d_user_id'];
      const dUnionId = item['affiliate_publisher.d_union_id'];
      const pubName = item['affiliate_publisher.pub_name'];
      const otherInfo = item.other_info;
      if (!dUnionId) { continue; }
      const clientName = otherInfo?.client_name;

      const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
      const jumpUrlOrigin = `${ddUseProjectUrl}/operation/clue-manage/personal?clue_id=${relation_id}`;

      // 发送待办
      await this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】：[${clientName}] The customer has not cooperated for more than 6 months and has been returned to the clue pool.`,
        jump_url: jumpUrlOrigin
      });
    }
  }

  /**
   * 节点9：财/法/税 修改了合同文件后，通知申请人
   */
  async node9Handler() {

    const notifys = await this.service.systemNotify.notify.getKeyNodeNotify({
      notify_type: 'node9',
      relation_type: 'contract',
      group: ['relation_id']
    });
    for (const item of notifys) {
      const { count, relation_id, other_info } = item;
      const dUserId = item['affiliate_publisher.d_user_id'];
      const dUnionId = item['affiliate_publisher.d_union_id'];
      const pubName = item['affiliate_publisher.pub_name'];
      const otherInfo = item.other_info;
      if (!dUnionId) { continue; }
      const clientName = otherInfo?.client_name;

      const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
      const jumpUrlOrigin = `${ddUseProjectUrl}/operation/contract-manage/owned?id=${relation_id}`;

      try {
        // 发送待办
        await this.service.ddCloud.sendNotificationToDo({
          executor_id: dUnionId,
          title: other_info.title,
          jump_url: jumpUrlOrigin
        });
      } catch {
        return Promise.reject();
      }
    }
  }

  async subscribe() {
    const flagKey = 'message-notification-center';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }
    this.ctx.logger.info('====== start to notice message notification center ======)');
    try {
      await this.node1Handler();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点1执行成功',
        title: '每天早上10点执行'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点1执行失败。错误原因${err?.message}`,
        title: '每天早上10点执行'
      });
    }
    try {
      await this.node2Handler();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点2执行成功',
        title: '每天早上10点执行'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点2执行失败。错误原因${err?.message}`,
        title: '每天早上10点执行'
      });
    }

    try {
      await this.node3Handler();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点3执行成功',
        title: '每天早上10点执行'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点3执行失败。错误原因${err?.message}`,
        title: '每天早上10点执行'
      });
    }
    try {
      await this.node6Handler();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点6执行成功',
        title: '每天早上10点执行'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点6执行失败。错误原因${err?.message}`,
        title: '每天早上10点执行'
      });
    }

    try {
      await this.node9Handler();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点9执行成功',
        title: '每天早上10点执行'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点9执行失败。错误原因${err?.message}`,
        title: '每天早上10点执行'
      });
    }

    this.ctx.logger.info('====== end to notice message notification center ======)');

  }
}
