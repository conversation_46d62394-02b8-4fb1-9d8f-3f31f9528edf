import { Subscription } from 'egg';
import moment from 'moment';
import { literal, Op } from 'sequelize';
import { NODE1_RECEIVER } from '../lib/globalVar';

// 每天早上10点执行
export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 5 10 * * 1,3,5', // 周一/周三/周五 ，早上10点05分执行
      type: 'worker',
      env: ['prod'], // 只在生产运行
    };
  }
  /**
   * 线索过期未分配提醒的机制
   * 线索创建时间-当前时间 > 3天的线索
   * 若当天，有线索通知重复，则不再触发此线索通知（避免打扰）目前已有线索通知： 每天早上10点通知昨天未分配的线索，如果同时存在通知冲突就只通知这个，不再通知「昨日未分配」
   */
  async clueNotAssignmentNotice() {

    // 查询出已创建时间大于三天的
    const threeDayAgo = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
    const ctimeThanThreeClue = await this.service.clueManage.pool.findOne({
      where: {
        ctime: {
          [Op.lte]: threeDayAgo
        },
        clue_status: 'unassigned'
      },
      raw: true,
    });
    if (!ctimeThanThreeClue) {
      return;
    }
    return this.service.systemNotify.notify.commonNoticeToSystemAndDingtalk({
      title: `The new lead has been unassigned for over three days, please handle it promptly.`,
      notifyUserId: NODE1_RECEIVER,
      jumpPath: '/operation/clue-manage/pool?status=unassigned'
    });
  }

  async contractUnapprovedNotice() {
    const twoDaysAgo = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
    const {
      assetGroups,
      finances,
      legals,
      taxs
    } = await this.service.authority.user.getByRoleUsers();
    const receiveUser = new Set();

    // 资产组未审核和合同，通知资产组
    const assetGroupContract = await this.service.contractManage.index.find({
      where: {
        status: 'asset_approval',
        submission_time: {
          [Op.lte]: twoDaysAgo
        }
      },
      raw: true
    });
    assetGroups.forEach(({ aud_type, value, label }) => {

      const whetherThereContract = assetGroupContract.some(it => aud_type.includes(it.contract_type));
      if (!whetherThereContract) { return; } // 证明该角色没有任意一条合同权限
      // 发送通知
      receiveUser.add(Number(value));

    });
    assetGroupContract.forEach(contract => {
      const transmittorInfo = Object.values(contract.transmittor_info || {}) as Array<Record<string, any>>;
      if (transmittorInfo.length) {
        transmittorInfo.forEach(item => {
          if (item.status) { return; }
          receiveUser.delete(item.origin);
          receiveUser.add(item.transmittor);
        });
      }
    });
    // 通知财务
    const financeContract = await this.service.contractManage.index.find({
      where: {
        status: 'financial_approval',
        [Op.or]: [
          literal(`JSON_EXTRACT(approval_memo, '$.asset_submit.time') <= '${twoDaysAgo}' `),
          literal(` asset_team_audit_required = 0 and submission_time <= '${twoDaysAgo}' `),
        ],
      },
      raw: true
    });
    finances.forEach(({ aud_type, value, label }) => {
      const whetherThereContract = financeContract.some(it => aud_type.includes(it.contract_type));
      if (!whetherThereContract) { return; } // 证明该角色没有任意一条合同权限
      // 发送通知
      receiveUser.add(Number(value));
    });
    financeContract.forEach(contract => {
      const transmittorInfo = Object.values(contract.transmittor_info || {}) as Array<Record<string, any>>;
      if (transmittorInfo.length) {
        transmittorInfo.forEach(item => {
          if (item.status) { return; }
          receiveUser.delete(item.origin);
          receiveUser.add(item.transmittor);
        });
      }
    });

    // 通知税务
    const taxContract = await this.service.contractManage.index.find({
      where: {
        status: 'tax_approval',
        [Op.and]: [
          literal(`JSON_EXTRACT(approval_memo, '$.financial_submit.time') <= '${twoDaysAgo}' `),
        ],
      },
      raw: true
    });
    taxs.forEach(({ aud_type, value }) => {
      const whetherThereContract = taxContract.some(it => aud_type.includes(it.contract_type));
      if (!whetherThereContract) { return; } // 证明该角色没有任意一条合同权限
      // 发送通知
      receiveUser.add(Number(value));
    });
    taxContract.forEach(contract => {
      const transmittorInfo = Object.values(contract.transmittor_info || {}) as Array<Record<string, any>>;
      if (transmittorInfo.length) {
        transmittorInfo.forEach(item => {
          if (item.status) { return; }
          receiveUser.delete(item.origin);
          receiveUser.add(item.transmittor);
        });
      }
    });
    // // 通知法务
    const legalContract = await this.service.contractManage.index.find({
      where: {
        status: 'legal_approval',
        [Op.and]: [
          literal(`JSON_EXTRACT(approval_memo, '$.tax_submit.time') <= '${twoDaysAgo}' `),
        ],
      },
      raw: true
    });
    legals.forEach(({ aud_type, value }) => {
      const whetherThereContract = legalContract.some(it => aud_type.includes(it.contract_type));
      if (!whetherThereContract) { return; } // 证明该角色没有任意一条合同权限
      // 发送通知
      receiveUser.add(Number(value));
    });
    legalContract.forEach(contract => {
      const transmittorInfo = Object.values(contract.transmittor_info || {}) as Array<Record<string, any>>;
      if (transmittorInfo.length) {
        transmittorInfo.forEach(item => {
          if (item.status) { return; }
          receiveUser.delete(item.origin);
          receiveUser.add(item.transmittor);
        });
      }
    });
    const receiveUserArr = Array.from(receiveUser);

    receiveUserArr.forEach(userId => {
      this.service.systemNotify.notify.commonNoticeToSystemAndDingtalk({
        title: `You have contracts pending approval for more than 2 days.`,
        notifyUserId: userId,
        jumpPath: '/operation/contract-manage/approval'
      });
    });

  }

  async subscribe() {
    const flagKey = 'working-day-notice';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }
    this.ctx.logger.info('====== start to working day notice ======)');
    try {
      await this.clueNotAssignmentNotice();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '执行成功',
        title: '周一/周三/周五 ，线索过期未分配提醒'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `错误原因${err?.message}`,
        title: '周一/周三/周五 ，线索过期未分配提醒'
      });
    }

    try {
      await this.contractUnapprovedNotice();
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '执行成功',
        title: '周一/周三/周五 ，合同审批人收到合同信息后未审批'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `错误原因${err?.message}`,
        title: '周一/周三/周五 ，合同审批人收到合同信息后未审批'
      });
    }

    this.ctx.logger.info('====== end to working day notice ======)');

  }
}
