import { Subscription } from 'egg';
import * as fs from 'fs';
import * as child_process from 'child_process';

export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 0 5 * * 1', // 每周一5点
      // interval: '10s',
      type: 'worker'
    };
  }

  async subscribe() {
    const { ctx } = this;
    const path = ctx.app.baseDir + '/fileTemp'; // 临时路径

    ctx.logger.info('====== Start to clear upload file path:', path, ' ======');

    // +7 即是7天前
    child_process.exec(`find ${path} -mtime +7 -type f -exec rm -rf {} \;`, (error: any) => {
      if (error) {
        ctx.logger.info(`====== clear upload path ${path} error ======`);
      }
    });

    ctx.logger.info('====== End to clear upload file ======');
  }
}
