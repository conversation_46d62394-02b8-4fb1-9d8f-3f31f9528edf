import { Subscription } from 'egg';
import { Op, QueryTypes } from 'sequelize';
import { COOP_BUS_ENUM, GET_COOP_BUS_TO_ENUM } from '../lib/constant';

// 每天早上10点执行
export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 5 10 * * *', // 每天早上10点05分执行
      type: 'worker',
    };
  }

  async subscribe() {
    const flagKey = 'customer-modification-state';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }
    this.ctx.logger.info('====== start to customer modification state ======)');

    // 查出所有未合作且关联的客户
    const clients = await this.service.customerManage.pool.find({
      where: {
        association_id: {
          [Op.ne]: 0
        },
        client_status: 0
      },
      raw: true
    });

    clients.forEach(it => {
      it.re_client_type = GET_COOP_BUS_TO_ENUM(it.coop_bus);
    });
    const affAdvIds = clients.filter(it => it.re_client_type === COOP_BUS_ENUM.AFF_ADV_GROUP).map(it => it.association_id);
    const affPubIds = clients.filter(it => it.re_client_type === COOP_BUS_ENUM.AFF_PUB_GROUP).map(it => it.association_id);
    const adxDmdIds = clients.filter(it => it.re_client_type === COOP_BUS_ENUM.ADX_DEMAND_GROUP).map(it => it.association_id);
    const adxPubIds = clients.filter(it => it.re_client_type === COOP_BUS_ENUM.ADX_PUB_GROUP || it.re_client_type === COOP_BUS_ENUM.OTHER_H5_PUB).map(it => it.association_id);

    const affReportData = await this.app.model.query(`
      SELECT * FROM dwr_affiliate_report_accrual_month_offer_revenue_cost_1d WHERE 1 = 1 and final_revenue > 0 and ( advertiser_group_id in (${affAdvIds.join(',')}) or affiliate_publisher_group_id in (${affPubIds.join(',')}))
    `, { type: QueryTypes.SELECT });

    const adxReportData = await this.app.model.query(`
      SELECT * FROM dwr_affiliate_report_accrual_month_programmatic_revenue_cost_1d WHERE 1 = 1 and revised_revenue > 0 and ( demand_source in (${adxDmdIds.join(',')}) or publisher_group_id in (${adxPubIds.join(',')}))
    `, { type: QueryTypes.SELECT });

    for (const client of clients) {
      if (client.re_client_type === COOP_BUS_ENUM.AFF_ADV_GROUP || client.re_client_type === COOP_BUS_ENUM.AFF_PUB_GROUP) {
        // aff客户
        const isIncome = affReportData.find(it => Number(it.advertiser_group_id) === Number(client.association_id) || Number(it.affiliate_publisher_group_id) === Number(client.association_id));
        if (!isIncome) { continue; }
        // 有收入，状态改为以合作
      } else {
        // adx客户
        const isIncome = adxReportData.find(it => Number(it.demand_source) === Number(client.association_id) || Number(it.publisher_group_id) === Number(client.association_id));
        if (!isIncome) { continue; }
      }
      await this.service.customerManage.pool.update({ id: client.id }, { client_status: 1 });

    }

    this.ctx.logger.info('====== end to customer modification state ======)');

  }
}
