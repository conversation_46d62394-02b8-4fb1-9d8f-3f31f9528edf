import { Subscription } from 'egg';
import moment from 'moment';
import { Op, where } from 'sequelize';
import sequelize from 'sequelize';
import { CONTRACT_TYPE_ENUM } from '../lib/constant';

export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 0 1 * * *', // 每天凌晨1点
      // interval: '10s',
      type: 'worker',
      immediate: false
    };
  }
  /**
   * 线索分配时间>=6个月，且状态还在已分配，则改成转化失败
   */
  async clueAssistToFail(sixMonthAgo: string) {
    // 查询出已分配日期大于等于6个月，且状态还是已分配的线索
    const result = await this.service.clueManage.pool.find({
      where: {
        assignment_time: {
          [Op.lte]: sixMonthAgo
        },
        clue_status: 'assigned'
      },
      raw: true
    });
    const ids = result.map(item => item.id);
    if (ids.length > 0) {
      const modifier = 'system';
      await this.service.clueManage.pool.update({
        id: {
          [Op.in]: ids
        }
      }, {

        previously_bd: sequelize.literal(`
          CASE
            WHEN previously_bd IS NULL THEN bd_id
            WHEN previously_bd = '' THEN bd_id
            ELSE CONCAT(previously_bd, ",", bd_id)
          END
        `),
        clue_status: 'conversion_failed',
        bd_id: '',
        assist_id: '',
        modifier
      });
    }
  }
  /**
   * 客户超过6个月未合作，系统会删除这个客户。这个逻辑不需要了，所以注释了
   */
  async clientUncooperativeHandler(sixMonthAgo) {
    // 查询出已分配日期大于等于6个月，且状态还是已分配的线索
    const result = await this.service.customerManage.pool.find({
      where: {
        ctime: {
          [Op.lte]: sixMonthAgo
        },
        client_status: 0
      },
      include: {
        model: this.ctx.model.ContractManage.Index,
        attributes: ['id', 'status'],
        required: false,
        where: {
          status: {
            [Op.ne]: CONTRACT_TYPE_ENUM.DRAFT
          }
        }
      },
      // raw: true
    });
    if (!result?.length) { return; }
    // 如果存在非草稿的合同，则不能删除
    const ids = result.filter(it => !it.dataValues.crm_contracts?.length).map(item => item.dataValues.id);

    if (!ids.length) { return; }

    for (const clientId of ids) {
      const clueDatas = await this.service.clueManage.pool.find({
        where: {
          client_id: clientId,
          clue_identity: 1 // 只获取主线索
        },
        raw: true,
        attributes: ['id', 'bd_id']
      });

      if (clueDatas?.length) {
        // 拿到所有线索
        const clientInfo = result.find(it => it.id === clientId);
        for (const clueInfo of clueDatas) {
          const bdId = clueInfo.bd_id;

          // 节点6： 客户回退到线索池通知
          await this.service.systemNotify.notify.sendClueRollbackNotify({ // 通知一条给自己
            relation_id: clueInfo.id,
            receiver_id: bdId,
            other_info: {
              client_name: clientInfo.client_name,
              client_id: clientInfo.id
            }
          });
          // 使用bd id 查询 BD leader
          const bdLeader = await this.service.authority.user.find({
            where: {
              bd_member: sequelize.literal(`find_in_set('${bdId}', bd_member)`)
            },
            raw: true
          });
          // 在通知给Leader
          if (bdLeader.length) {
            for (const bdLeaderInfo of bdLeader) {
              await this.service.systemNotify.notify.sendClueRollbackNotify({ // 通知一条给自己
                relation_id: clueInfo.id,
                receiver_id: bdLeaderInfo.id,
                other_info: {
                  client_name: clientInfo.client_name,
                  client_id: clientInfo.id
                }
              });
            }
          }
        }
      }
    }

    // 先处理关联的线索数据
    await this.service.clueManage.pool.update({
      client_id: {
        [Op.in]: ids
      }
    }, {
      previously_bd: sequelize.literal(`
          CASE
            WHEN previously_bd IS NULL THEN bd_id
            WHEN previously_bd = '' THEN bd_id
            ELSE CONCAT(previously_bd, ",", bd_id)
          END
        `),
      clue_status: 'unassigned',
      bd_id: '',
      assist_id: '',
      client_id: 0,
      clue_identity: 0,
      modifier: 'system'
    });
    // 删除客户
    await this.service.customerManage.pool.destroy({
      id: {
        [Op.in]: ids
      }
    });

  }
  async subscribe() {
    const flagKey = 'clue-modification-state';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }
    // // 从今天往前推6个月
    const sixMonthAgo = moment().subtract(6, 'months').format('YYYY-MM-DD HH:mm:ss');
    await this.clueAssistToFail(sixMonthAgo); // 处理线索
    // await this.clientUncooperativeHandler(sixMonthAgo); // 处理客户

  }
}
