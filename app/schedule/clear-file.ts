import { Subscription } from 'egg';
import * as fs from 'fs';
import * as child_process from 'child_process';

const CLEAR_PATTERN = ['@flat', 'master-stderr', 'master-stdout'];

export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 0 6 * * *', // 每天6点
      // interval: '10s',
      type: 'worker',
      immediate: true
    };
  }

  async subscribe() {
    const { ctx } = this;
    const loggerPath = ctx.app.config.logger.dir; // log的路径
    const files = fs.readdirSync(loggerPath);

    ctx.logger.info('====== Start to clear file path:', loggerPath, ' ======');

    files.forEach(fileName => {
      const filePath = `${loggerPath}/${fileName}`;
      const state = fs.statSync(filePath);
      const d = new Date();
      const todayNum = +`${d.getFullYear()}${(d.getMonth() + 1).toString().padStart(2, '0')}${d.getDate() - ctx.app.config.logrotator.maxDays}`;

      // 匹配正则的则删除
      const isTarget = CLEAR_PATTERN.some(pattern => {
        const reg = new RegExp(pattern, 'g');
        return reg.test(fileName);
      });
      if (isTarget) {
        // 删除文件
        if (state.isFile()) {
          if (/^(master-stderr|master-stdout)/.test(fileName)) {
            const nameArr = fileName.split('.');
            if (nameArr.length >= 3) {
              const date = +nameArr[2];
              if (todayNum > date) {
                fs.unlinkSync(filePath);
              }
            }
          } else {
            fs.unlinkSync(filePath);
          }
        }
        // 删除文件夹
        if (state.isDirectory()) {
          // recursive这个选项是v12.10才支持
          // fs.rmdirSync(filePath, { recursive: true });
          child_process.exec(`rm -rf ${filePath}`, (error: any) => {
            if (error) {
              ctx.logger.info(`====== clear folder ${filePath} error ======`);
            }
          });
        }
      }
    });

    ctx.logger.info('====== End to clear file ======');
  }
}
