import { Subscription } from 'egg';
import moment from 'moment';
import { Op, where } from 'sequelize';
import sequelize from 'sequelize';

// 每月1号的早上10点
export default class ClearFile extends Subscription {
  static get schedule() {
    return {
      cron: '0 5 10 1 * *', // 每个月1号的早上10点05分
      // interval: '10s',
      type: 'worker',
      env: ['prod'],  // 只在生产运行
    };
  }
  /**
   * 节点4：线索太久未跟进，通知线索负责的BD
   */
  async clueAssistNotice(threeMonthAgo: string) {
    // 查询出已分配日期大于等于6个月，且状态还是已分配的线索
    const notifys = await this.service.clueManage.pool.find({
      where: {
        assignment_time: {
          [Op.lte]: threeMonthAgo
        },
        clue_status: 'assigned'
      },
      attributes: ['bd_id', [sequelize.fn('count', sequelize.col('bd_id')), 'count']],
      group: ['bd_id'],
      raw: true,
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['d_user_id', 'd_union_id'],
        }
      ]
    });
    if (!notifys.length) { return; }
    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/clue-manage/personal?status=assigned`;
    for (const item of notifys) {
      const { bd_id, count } = item;
      const dUnionId = item['affiliate_publisher.d_union_id'];
      if (!dUnionId) { continue; }
      // 发送待办
      this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】：There are ${count} leads that have not been followed up for more than 3 months.`,
        jump_url: jumpUrlOrigin
      });
    }
    // console.log(result);
  }
  /**
   * 客户长期未合作，通知负责该客户的BD
   */
  async clientUncooperativeHandler(threeMonthAgo) {
    // 查询出已分配日期大于等于6个月，且状态还是已分配的线索
    const clientDatas = await this.service.customerManage.pool.find({
      where: {
        ctime: {
          [Op.lte]: threeMonthAgo
        },
        client_status: 0,

      },
      raw: true
    });
    if (!clientDatas?.length) { return; }
    const ids = clientDatas.map(item => item.id);

    const clueDatas = await this.service.clueManage.pool.find({
      where: {
        client_id: {
          [Op.in]: ids
        },
        clue_identity: 1 // 只获取主线索
      },
      attributes: ['bd_id', [sequelize.fn('count', sequelize.col('bd_id')), 'count']],
      group: ['bd_id'],
      raw: true,
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['d_user_id', 'd_union_id'],
        }
      ]
    });
    if (!clueDatas?.length) { return; }

    const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
    const jumpUrlOrigin = `${ddUseProjectUrl}/operation/client-manage/personal?status=0`;

    for (const item of clueDatas) {
      const { count } = item;
      const dUnionId = item['affiliate_publisher.d_union_id'];
      if (!dUnionId) { continue; }
      // 发送待办
      this.service.ddCloud.sendNotificationToDo({
        executor_id: dUnionId,
        title: `【CRM】：${count} customers who have not been followed up for more than 6 months will be automatically returned to the lead pool. `,
        jump_url: jumpUrlOrigin
      });
    }

  }
  async subscribe() {
    const flagKey = 'notification-of-unprocessed';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }
    // 从今天往前推3个月
    const threeMonthAgo = moment().subtract(3, 'months').format('YYYY-MM-DD HH:mm:ss');
    try {
      await this.clueAssistNotice(threeMonthAgo); // 处理线索
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点4执行成功',
        title: '每月1号的早上10点'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点4执行失败。错误原因${err?.message}`,
        title: '每月1号的早上10点'
      });
    }

    try {
      await this.clientUncooperativeHandler(threeMonthAgo); // 处理客户
      this.service.dingtalk.sendCrmWarning({
        code: 200,
        message: '定时任务节点5执行成功',
        title: '每月1号的早上10点'
      });
    } catch (err: any) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `定时任务节点5执行失败。错误原因${err?.message}`,
        title: '每月1号的早上10点'
      });
    }
  }
}
