import { Subscription } from 'egg';
import moment from 'moment';
import { Op, where } from 'sequelize';
import { unlink } from 'fs';
import FormStream from 'formstream';
import { CONTRACT_TYPE_ENUM, OSS_PREFIX_URL, OSS_ROOT_PATH } from '../lib/constant';

function replaceSpecialCharacters(filename) {
  // 找到文件名中最后一个点的位置
  const lastDotIndex = filename.lastIndexOf('.');

  // 如果找到了点，则将点之前的部分与点之后的部分分开处理
  if (lastDotIndex !== -1) {
    const filenamePart = filename.substring(0, lastDotIndex); // 文件名部分（不含后缀）
    const extensionPart = filename.substring(lastDotIndex); // 后缀部分（包含点）

    // 使用正则表达式匹配所有非字母、数字、下划线的字符，包括空格和特殊符号
    const regex = /[^\w\d]/g; // 匹配所有非字母、数字、下划线的字符

    // 将文件名部分的特殊字符替换为下划线
    const replacedFilenamePart = filenamePart.replace(regex, '_');

    // 返回替换后的文件名和后缀拼接在一起
    return `${replacedFilenamePart}-${Date.now()}` + extensionPart;
  } else {
    // 如果找不到点，直接返回原始文件名
    return `${filename}-${Date.now()}`;
  }
}

export default class ContractModificationState extends Subscription {
  static get schedule() {
    return {
      // cron: '0 0 * * * *', // 每隔一小时执行一次
      cron: '0 0,30 * * * *', // 每隔半小时
      // interval: '10s',
      type: 'worker',
      immediate: false,
      env: ['test', 'prod'], // 只在测试和线上环境跑
    };
  }

  async contractUpdateState() {
    const contracts = await this.service.contractManage.index.find({
      where: {
        status: {
          [Op.in]: [CONTRACT_TYPE_ENUM.OA_APPROVAL, CONTRACT_TYPE_ENUM.DUAL_SIGNATURE_REVIEW]
        }
      },
      include: [
        {
          model: this.ctx.model.CustomerManage.Pool,
          attributes: ['id', 'client_name'],
          through: {
            attributes: [] // 空数组表示不包含中间表 CrmClientContract 的其他字段
          }
        }
      ],
    });
    const contractIdMap = new Map();
    for (const contract of contracts) {
      const { instance_id, id, dual_signed, crm_clients, apply_id } = contract.dataValues;
      if (contractIdMap.has(id)) {
        // 如果记录过了，跳过，防止重复数据
        continue;
      }
      if (!contractIdMap.has(id)) {
        contractIdMap.set(id, true);
      }
      await this._contractUpdateState({ instance_id, id, ori_dual_signed: dual_signed, crm_clients, apply_id });
    }
  }

  async _contractUpdateState({ instance_id, id, ori_dual_signed, crm_clients, apply_id }) {
    const { isSuccess, result, msg } = await this.service.ddCloud.getAuditDetail(instance_id);
    if (!isSuccess) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `错误原因：${msg}, 合同ID: ${id}`,
        title: '每隔半小时执行，修改合同状态'
      });
      return;
    }

    const { formComponentValues } = result;
    const dualSignature = formComponentValues.find(it => it.name === '双签合同上传')?.value; // 存在钉钉合同
    const isComplete = result.status === 'COMPLETED' && result.result === 'agree'; // 已完成
    const isRefuse = result.status === 'COMPLETED' && result.result === 'refuse'; // 已拒绝
    const isTerminated = result.status === 'TERMINATED'; // 发起人撤销，这个状态暂时用不到

    let fileSrc = ori_dual_signed?.src;
    const fileObj = JSON.parse(dualSignature || '[]');
    let { fileName } = fileObj[0] || {};
    const { fileId } = fileObj[0] || {};
    if (fileName) {
      fileName = replaceSpecialCharacters(fileName);
    }
    if (dualSignature && !ori_dual_signed) {
      // 上传文件到OSS，并记录起来，如果已经记录过了，则直接跳过

      if (!fileId) {
        this.service.dingtalk.sendCrmWarning({
          code: 500,
          message: `文件ID不存在，合同ID: ${id}`,
          title: '每隔半小时执行，修改合同状态'
        });
        return;
      }

      const src = await this.downloadFileAndToOSS({
        processInstanceId: instance_id,
        fileId,
        fileName
      });

      if (!src) { return; }

      fileSrc = src;
    }

    if (dualSignature && (!isRefuse && !isComplete && !isTerminated)) {
      await this.service.contractManage.index.update({
        id
      }, {
        status: CONTRACT_TYPE_ENUM.DUAL_SIGNATURE_REVIEW,
        dual_signed: {
          src: fileSrc,
          file_id: fileId,
          file_name: fileName
        }
      });
      return;

    }
    if (isRefuse || isComplete || isTerminated) {
      const updateResult: Record<string, any> = {
        // 拒绝和撤销当失败，否则是完成
        status: (isRefuse || isTerminated) ? CONTRACT_TYPE_ENUM.OA_REJECTED : CONTRACT_TYPE_ENUM.COMPLETED
      };
      if (dualSignature) {
        updateResult.dual_signed = {
          src: fileSrc,
          file_id: fileId,
          file_name: fileName
        };
      }
      updateResult.complete_time = moment().format('YYYY-MM-DD HH:mm:ss');

      await this.service.contractManage.index.update({
        id
      }, updateResult);

      if (isRefuse || isTerminated) {
        // 撤销或者拒绝
        this.service.contractManage.index.noticeToSystemAndDingtalk({
          notifyUserId: apply_id,
          contractId: id,
          title: `Your contract approval form (id: ${id}) has been rejected`,
          applyId: apply_id
        });
      } else if (isComplete) {
        // 通知合同创建人
        this.service.contractManage.index.noticeToSystemAndDingtalk({
          notifyUserId: apply_id,
          contractId: id,
          title: `contract approval forms（id: ${id}） you submitted have been approved`,
          applyId: apply_id
        });
      }
      if (isRefuse) {
        // 拒绝，添加到合同消息里面
        const refuseRecord = result.operationRecords.find(it => it.result === 'REFUSE');
        let creator = 'DingTalk-OA';
        if (refuseRecord) {
          const userId = refuseRecord.userId;
          const userInfo = await this.service.authority.user.findOne({
            where: {
              d_user_id: userId
            }
          });
          creator = userInfo?.pub_name;
        }
        let remark = refuseRecord?.remark || '';
        if (remark) {
          remark = remark.replace(/\[([^\]]+)\]\(\d+\)/g, (match, p1) => {
            return `@${p1}`; // 在匹配的内容前加上@
          });
        }
        const commentParams: Record<string, any> = {
          contract_id: id,
          tag: 'OA-Rejected',
          message: remark,
          type: 'oa_reject',
          creator,
        };
        await this.service.contractManage.chat.create(commentParams);
      } else if (isTerminated) {
        // 被撤销了
        const refuseRecord = result.tasks.find(it => it.status === 'TERMINATED');
        let creator = 'DingTalk-OA';
        if (refuseRecord) {
          const userId = refuseRecord.userId;
          const userInfo = await this.service.authority.user.findOne({
            where: {
              d_user_id: userId
            }
          });
          creator = userInfo?.pub_name;
        }
        const commentParams: Record<string, any> = {
          contract_id: id,
          tag: 'OA-Revocation',
          message: refuseRecord?.remark,
          type: 'oa_revocation',
          creator,
        };
        await this.service.contractManage.chat.create(commentParams);
      }
    }
    if (isComplete) {

      const commentParams: Record<string, any> = {
        contract_id: id,
        tag: 'Completed',
        message: '',
        type: 'oa_complete',
        creator: 'DingTalk-OA',
      };
      await this.service.contractManage.chat.create(commentParams);

      // 如果已完成，修改客户状态为已合作
      const clientIds = crm_clients.map(it => it.id);
      if (!clientIds.length) { return; }
      this.service.customerManage.pool.update({
        id: {
          [Op.in]: clientIds
        }
      }, {
        client_status: 1
      });
    }

    // console.log(1)
  }

  async downloadFileAndToOSS({
    processInstanceId,
    fileId,
    fileName
  }) {
    const { result, isSuccess } = await this.service.ddCloud.downloadAuditFile({
      processInstanceId,
      fileId
    });
    if (!isSuccess) { return; }
    const downloadUri = result.downloadUri;
    const { isSuccess: downloadFileIsSuccess, stashFilePath } = await this.service.upload.downloadFileFromPath({
      downloadUri,
      fileName
    });

    if (!downloadFileIsSuccess) {
      this.service.dingtalk.sendCrmWarning({
        code: 500,
        message: `审批实例ID: ${processInstanceId}`,
        title: '双签合同文件保存到本地失败'
      });
      return;
    }

    const form = new FormStream();
    form.file('file', stashFilePath);
    const ossSavePath = `${OSS_ROOT_PATH}${fileName}`; // 文件路径
    this.service.upload.saveToOssFile({ form, savePath: `${ossSavePath}` });

    setTimeout(() => {
      // 5秒之后删除文件
      unlink(stashFilePath, () => { });
    }, 5000);

    return `${OSS_PREFIX_URL}${OSS_ROOT_PATH}${fileName}`;
  }

  async subscribe() {
    const flagKey = 'contract-modification-state';
    const isLock = await this.service.redis.isLock(flagKey);
    if (isLock) {
      // 已经存在
      return false;
    }

    await this.contractUpdateState();

  }
}
