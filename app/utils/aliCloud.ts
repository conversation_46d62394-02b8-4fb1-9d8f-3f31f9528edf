import Util, * as $Util from '@alicloud/tea-util';
import dingtalkoauth2_1_0, * as $dingtalkoauth2_1_0 from '@alicloud/dingtalk/dist/oauth2_1_0/client';
import dingtalktodo_1_0, * as $dingtalktodo_1_0 from '@alicloud/dingtalk/todo_1_0';
import dingtalkstorage_1_0, * as $dingtalkstorage_1_0 from '@alicloud/dingtalk/storage_1_0';
import dingtalkworkflow_1_0, * as $dingtalkworkflow_1_0 from '@alicloud/dingtalk/workflow_1_0';

import SYSTEM_CONFIG from '@flat/egg-system-config';

import * as $OpenApi from '@alicloud/openapi-client';
import Axios from 'axios';
import { CONTRACT_IS_EXCEED_AMOUNT, CONTRACT_TYPE } from '../lib/constant';
import { format } from 'sequelize/types/lib/utils';
import moment from 'moment';
import { ADMIN_UNION_ID, DD_CORP_ID, DD_SPACE_ID, DEV_ACTION_KEY, DEV_PROCESS_CODE, PROD_ACTION_KEY, PROD_PROCESS_CODE } from '../lib/globalVar';

export default class DDClient {
  static OAUTH_CLIENT: any;
  static STORAGE_CLIENT: any;
  static WORKFLOW_CLIENT: any;

  /**
   * 使用 Token 初始化账号Client
   */

  static createOauthClient(): dingtalktodo_1_0 {
    if (this.OAUTH_CLIENT) {
      return this.OAUTH_CLIENT;
    }
    const config = new $OpenApi.Config({});
    config.protocol = 'https';
    config.regionId = 'central';
    const client = new dingtalkoauth2_1_0(config);
    this.OAUTH_CLIENT = client;
    return client;
  }

  static createStorageClient() {
    if (this.STORAGE_CLIENT) {
      return this.STORAGE_CLIENT;
    }
    const config = new $OpenApi.Config({});
    config.protocol = 'https';
    config.regionId = 'central';
    const client = new dingtalkstorage_1_0(config);
    this.STORAGE_CLIENT = client;
    return client;
  }

  static createWorkflowClient(): dingtalkworkflow_1_0 {
    if (this.WORKFLOW_CLIENT) {
      return this.WORKFLOW_CLIENT;
    }
    const config = new $OpenApi.Config({});
    config.protocol = 'https';
    config.regionId = 'central';
    const client = new dingtalkworkflow_1_0(config);
    this.WORKFLOW_CLIENT = client;
    return client;
  }

  static async getAccessToken(): Promise<{ accessToken: string, expireIn: number }> {
    const client = DDClient.createOauthClient();
    const getAccessTokenRequest = new $dingtalkoauth2_1_0.GetAccessTokenRequest({
      appKey: SYSTEM_CONFIG.dd_appKey,
      appSecret: SYSTEM_CONFIG.dd_appSecret,
    });
    try {
      const res = await client.getAccessToken(getAccessTokenRequest);
      const { accessToken, expireIn } = res.body;
      return {
        accessToken,
        expireIn,
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题

        return err;
      }

    }
  }

  static async getUserDDIds({ token, mobile }) {
    try {
      const res = await Axios.post(`https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=${token}`, {
        mobile,
      });
      const userId = res.data.result.userid;
      const uniRes = await Axios.post(`https://oapi.dingtalk.com/topapi/v2/user/get?access_token=${token}`, {
        userid: userId,
        language: 'zh_CN',
      });

      return {
        token,
        userId,
        unionId: uniRes.data.result.unionid,
        deptId: uniRes.data.result.dept_id_list?.[0] || null,
        isSuccess: true
      };
    } catch (err: any) {
      return {
        isSuccess: false,
        msg: err.message
      };
    }
  }

  static async getFileUploadInfo({
    token,
    unionId,
    fileSize,
    fileName
  }) {
    const client = this.createStorageClient();

    const getFileUploadInfoHeaders = new $dingtalkstorage_1_0.GetFileUploadInfoHeaders({});

    getFileUploadInfoHeaders.xAcsDingtalkAccessToken = token;
    const optionPreCheckParam = new $dingtalkstorage_1_0.GetFileUploadInfoRequestOptionPreCheckParam({
      size: fileSize,
      name: fileName,
    });
    const option = new $dingtalkstorage_1_0.GetFileUploadInfoRequestOption({
      storageDriver: 'DINGTALK',
      preCheckParam: optionPreCheckParam,
      preferRegion: 'SHENZHEN', // 优先地域
      preferIntranet: true, // 是否优先使用内网传输，使用该参数前提是配置了专属存储内网传输。
    });
    const getFileUploadInfoRequest = new $dingtalkstorage_1_0.GetFileUploadInfoRequest({
      unionId,
      protocol: 'HEADER_SIGNATURE',
      multipart: false,
      option,
    });

    try {
      const res = await client.getFileUploadInfoWithOptions(DD_SPACE_ID, getFileUploadInfoRequest, getFileUploadInfoHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        res
      };
    } catch (err: any) {
      return {
        isSuccess: false,
        msg: err.message
      };
    }
  }

  static async submitFile({
    token,
    unionId,
    fileSize,
    fileName,
    fileUrl
  }) {
    const client = this.createStorageClient();

    const commitFileHeaders = new $dingtalkstorage_1_0.CommitFileHeaders({});

    const { isSuccess, res: uploadInfo, msg } = await this.getFileUploadInfo({ token, unionId, fileName, fileSize });
    if (!isSuccess) {
      return {
        isSuccess,
        msg
      };
    }
    // 执行上传操作
    const body = uploadInfo.body;
    const url = body.headerSignatureInfo.resourceUrls[0];
    const headers = body.headerSignatureInfo.headers;

    commitFileHeaders.xAcsDingtalkAccessToken = token;
    const optionAppProperties0 = new $dingtalkstorage_1_0.CommitFileRequestOptionAppProperties({
      name: Date.now().toString(),
      value: Date.now().toString(),
      visibility: 'PRIVATE',
    });
    const option = new $dingtalkstorage_1_0.CommitFileRequestOption({
      size: fileSize,
      conflictStrategy: 'AUTO_RENAME',
      appProperties: [
        optionAppProperties0
      ],
    });

    const commitFileRequest = new $dingtalkstorage_1_0.CommitFileRequest({
      unionId,
      uploadKey: body.uploadKey,
      name: fileName,
      parentId: '0',
      option,
    });
    const responseFile = await Axios({
      url: fileUrl,
      method: 'GET',
      responseType: 'stream'
    });

    const fileStream = responseFile.data;
    // const filtPath = path.resolve(__dirname, '../view/README.md');
    // const fileStream = fs.createReadStream(filtPath)

    await Axios.put(url, fileStream, {
      headers: {
        ...headers,
        'Accept-Charset': 'UTF-8',
        'contentType': 'UTF-8',
        'content-type': ''
      },
      maxContentLength: Infinity,
    });
    try {
      const res = await client.commitFileWithOptions(DD_SPACE_ID, commitFileRequest, commitFileHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body?.dentry
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题

        return {
          isSuccess: false,
          msg: err.message
        };
      }

    }
  }

  static async initiateApproveClient({
    token,
    userId,
    deptId,
    fileList,
    contractInfo,
    env,
    actionerUserIds = []
  }) {
    const client = this.createWorkflowClient();
    const startProcessInstanceHeaders = new $dingtalkworkflow_1_0.StartProcessInstanceHeaders({});

    startProcessInstanceHeaders.xAcsDingtalkAccessToken = token;

    const formItemInfo = {
      '合同编号 Contract No.': contractInfo.contract_code,
      '业务线 BU': contractInfo.bu,
      '合同类型 Contract Type': CONTRACT_TYPE.find(it => it.value === contractInfo.contract_type)?.label || '',
      '签约日期 Effective Date': moment(contractInfo.effective_date).format('YYYY-MM-DD'),
      '到期日期 Expiry Date': moment(contractInfo.expiry_date).format('YYYY-MM-DD'),
      '合同金额（元）Contract Amount': contractInfo.amount,
      '币种 Currency': contractInfo.currency,
      '合同金额是否超￥10万Does the contract amount exceed ￥100K？': CONTRACT_IS_EXCEED_AMOUNT.find(it => it.value === contractInfo.is_exceed_amount)?.label || '',
      '我方签约主体 Our Company name': contractInfo.our_company_name,
      '我方对接人 Contact person from our side': contractInfo.our_contact_person,
      '我方联系方式 Contact information from our side': contractInfo.our_information,
      '合作方名称 Brand Name of the Business Partner': contractInfo.bus_brand_name,
      '合作方签约主体 Company Name of the Business Partner': contractInfo.partner_company_name,
      '合作方对接人 Contact person': contractInfo.partner_contact_person,
      '合作方联系方式 Contact Information': contractInfo.partner_information,
      '合同内容简述 Brief description of the contract': contractInfo.contract_desc
    };
    const formComponentValues = [
      // formComponentValues0
      ...Object.entries(formItemInfo).map(([name, value]) => {
        return new $dingtalkworkflow_1_0.StartProcessInstanceRequestFormComponentValues({
          name,
          value
        });
      }),
      new $dingtalkworkflow_1_0.StartProcessInstanceRequestFormComponentValues({
        name: '合同附件',
        value: JSON.stringify(fileList)
      })
    ];
    const actionerKey = env === 'prod' ? PROD_ACTION_KEY : DEV_ACTION_KEY;
    const targetSelectActioners0 = new $dingtalkworkflow_1_0.StartProcessInstanceRequestTargetSelectActioners({
      actionerKey,
      actionerUserIds
    });
    const processCode = env === 'prod' ? PROD_PROCESS_CODE : DEV_PROCESS_CODE;
    const startProcessInstanceRequest = new $dingtalkworkflow_1_0.StartProcessInstanceRequest({
      originatorUserId: userId,
      processCode,
      deptId,

      ccPosition: 'FINISH',
      formComponentValues,
      targetSelectActioners: [
        targetSelectActioners0
      ],
    });

    try {
      const res = await client.startProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        instanceId: res.body?.instanceId
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题
      }
      return {
        isSuccess: false,
        msg: err.message
      };

    }
  }

  static async getAuditDetail({
    token,
    processInstanceId
  }) {
    const client = this.createWorkflowClient();
    const getProcessInstanceHeaders = new $dingtalkworkflow_1_0.GetProcessInstanceHeaders({});
    getProcessInstanceHeaders.xAcsDingtalkAccessToken = token;
    const getProcessInstanceRequest = new $dingtalkworkflow_1_0.GetProcessInstanceRequest({
      processInstanceId,
    });
    try {
      const res = await client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body.result
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题
        return {
          isSuccess: false,
          msg: err.message
        };
      }

    }

  }

  static async autoConfirmAudit({
    token,
    processInstanceId,
    taskId,
    actionerUserId
  }) {
    const client = this.createWorkflowClient();
    const executeProcessInstanceHeaders = new $dingtalkworkflow_1_0.ExecuteProcessInstanceHeaders({});
    executeProcessInstanceHeaders.xAcsDingtalkAccessToken = token;
    const executeProcessInstanceRequest = new $dingtalkworkflow_1_0.ExecuteProcessInstanceRequest({
      processInstanceId,
      result: 'agree',
      actionerUserId,
      taskId,
    });
    try {
      await client.executeProcessInstanceWithOptions(executeProcessInstanceRequest, executeProcessInstanceHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题
        return {
          isSuccess: false,
          msg: err.message
        };
      }

    }
  }

  static async downloadAuditFile({
    token,
    processInstanceId,
    fileId
  }) {
    const client = this.createWorkflowClient();
    const grantProcessInstanceForDownloadFileHeaders = new $dingtalkworkflow_1_0.GrantProcessInstanceForDownloadFileHeaders({});
    grantProcessInstanceForDownloadFileHeaders.xAcsDingtalkAccessToken = token;
    const grantProcessInstanceForDownloadFileRequest = new $dingtalkworkflow_1_0.GrantProcessInstanceForDownloadFileRequest({
      processInstanceId,
      fileId,
    });
    try {
      const res = await client.grantProcessInstanceForDownloadFileWithOptions(grantProcessInstanceForDownloadFileRequest, grantProcessInstanceForDownloadFileHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body.result
      };
    } catch (err: any) {
      if (!Util.empty(err.code) && !Util.empty(err.message)) {
        // err 中含有 code 和 message 属性，可帮助开发定位问题
        return {
          isSuccess: false,
          msg: err.message
        };
      }

    }
  }

  static async setUserSpaceRole({
    unionId,
    token
  }) {
    const client = this.createStorageClient();
    const addPermissionHeaders = new $dingtalkstorage_1_0.AddPermissionHeaders({});
    addPermissionHeaders.xAcsDingtalkAccessToken = token;
    const option = new $dingtalkstorage_1_0.AddPermissionRequestOption();
    const members0 = new $dingtalkstorage_1_0.AddPermissionRequestMembers({
      type: 'USER',
      id: unionId,
      corpId: DD_CORP_ID,
    });
    const addPermissionRequest = new $dingtalkstorage_1_0.AddPermissionRequest({
      roleId: 'MANAGER',
      unionId: ADMIN_UNION_ID,
      members: [
        members0
      ],
      option,
    });

    try {
      const res = await client.addPermissionWithOptions(DD_SPACE_ID, '0', addPermissionRequest, addPermissionHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body.result
      };
    } catch (err: any) {
      return {
        isSuccess: false,
        msg: err.message
      };

    }
  }

  static async settingCorpSpaceRole({
    token,
    unionId
  }) {
    const client = this.createStorageClient();
    const addPermissionHeaders = new $dingtalkstorage_1_0.AddPermissionHeaders({});
    addPermissionHeaders.xAcsDingtalkAccessToken = token;
    const option = new $dingtalkstorage_1_0.AddPermissionRequestOption();
    const members0 = new $dingtalkstorage_1_0.AddPermissionRequestMembers({
      type: 'ORG',
      id: DD_CORP_ID,
      corpId: DD_CORP_ID,
    });
    const addPermissionRequest = new $dingtalkstorage_1_0.AddPermissionRequest({
      roleId: 'MANAGER',
      unionId: ADMIN_UNION_ID,
      members: [
        members0
      ],
      option,
    });

    try {
      const res = await client.addPermissionWithOptions(DD_SPACE_ID, '0', addPermissionRequest, addPermissionHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body.result
      };
    } catch (err: any) {
      return {
        isSuccess: false,
        msg: err.message
      };

    }
  }

  static async setSpaceWithDownloadAuth({
    dingUserId,
    token,
    processInstanceId,
    fileId
  }) {
    const client = this.createWorkflowClient();
    const getSpaceWithDownloadAuthHeaders = new $dingtalkworkflow_1_0.GetSpaceWithDownloadAuthHeaders({});
    getSpaceWithDownloadAuthHeaders.xAcsDingtalkAccessToken = token;
    const getSpaceWithDownloadAuthRequest = new $dingtalkworkflow_1_0.GetSpaceWithDownloadAuthRequest({
      userId: dingUserId,
      processInstanceId,
      fileId,
      withCommentAttatchment: true
    });

    try {
      const res = await client.getSpaceWithDownloadAuthWithOptions(getSpaceWithDownloadAuthRequest, getSpaceWithDownloadAuthHeaders, new $Util.RuntimeOptions({}));
      return {
        isSuccess: true,
        result: res.body.result
      };
    } catch (err: any) {
      return {
        isSuccess: false,
        msg: err.message
      };

    }
  }

}
