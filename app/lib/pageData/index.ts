export const TABLE_SORT_LIST = ['descend', 'ascend', ''];

export const renderFunc = (data: any) => {
  let dataVal = '0';
  if (data === null) {
    return null;
  }
  if (!data) {
    return 0;
  }
  if (typeof data === 'number') {
    dataVal = data + '';
  } else if (data.replace) {
    dataVal = data?.replace(/,/gi, '')?.replace('$', '');
  }
  if (dataVal === '∞') {
    return dataVal;
  }
  return Number(dataVal);
};

export const dimensionMap = new Map<string, Record<string, any>>([
  ['Billing Month', { key: 'month', width: 120, sorter: true, sortDirections: TABLE_SORT_LIST, defaultSortOrder: 'descend' }],
  ['Adv Group', { key: 'adv_group', width: 150 }],
  ['Adv Type', { key: 'adv_type', width: 180 }],
  ['Payment Period', { key: 'payment_period', width: 150 }],
  ['Adv BD', { key: 'adv_bd', renderKey: 'adv_owner_name', width: 150 }],
  ['Adv Owner', { key: 'adv_owner', width: 120 }],
  ['Pub Group', { key: 'pub_group', width: 110 }],
  ['Pub Type', { key: 'pub_type', width: 120 }],
  ['Pub BD', { key: 'pub_bd', width: 150 }],
  ['Pub Owner', { key: 'pub_owner', width: 180 }],
  ['Received Month', { key: 'paid_month', width: 120 }],
  ['Received Date', { key: 'paid_date', width: 120 }],
  ['Overdue Payment Date', { key: 'overdue_payment_date', width: 190 }],
  ['Deduction', { key: 'deduction', width: 190, sorter: true, sortDirections: TABLE_SORT_LIST }],

]);
export const metricMap = new Map([
  ['Billing Revenue', { key: 'billing_revenue', width: 120, sorter: true, sortDirections: TABLE_SORT_LIST, renderFunc }],
  ['Actual Receipt', { key: 'paid_revenue', width: 120, sorter: true, sortDirections: TABLE_SORT_LIST, renderFunc }],

  ['Billing Pub Cost', { key: 'pub_cost', width: 150, sorter: true, sortDirections: TABLE_SORT_LIST, renderFunc }],
  ['Gross Profit', { key: 'gross_profit', width: 150, sorter: true, sortDirections: TABLE_SORT_LIST, renderFunc }],
  ['Gross Profit Margin', { key: 'gross_profit_margin', width: 190, sorter: true, sortDirections: TABLE_SORT_LIST }],
  ['Commissionable Amount', { key: 'commissionable_amount', width: 180, sorter: true, sortDirections: TABLE_SORT_LIST, renderFunc }],
]);


