import moment from 'moment';

// 后台初始化需要配置的内容如下：
const AppConfig = {
  name: 'Flat Ads CRM', // 后台官方名称
  slogan: '后台slogan（可选）' // 后台slogan
};
// 后台初始化需要配置的内容结束
export const SORT_DESC_LIST = ['desc', 'descend'];
const BaseData = {
  type: [
    { text: 'H5', value: 'H5' },
    { text: 'PC', value: 'PC' },
    { text: 'PWA', value: 'PWA' }
  ],
  authType: [
    { text: '菜单', value: 'menu' },
    { text: '接口', value: 'api' },
    { text: '操作', value: 'action' }
  ]
};

const RegExpRule = {
  staticFile: /.*\.(js|css|jpg|jpeg|png|gif|svga)$/gim
};

const SavePath = `./fileTemp`;
const Bucket = 'dsp-adcreative';

const AuthPrefix = 'auth-';
const RolePrefix = 'role-';
export const OPTION_SEPARATOR = '___';
export const OSS_PREFIX_URL = 'https://static.flat-ads.com/';
export const OSS_ROOT_PATH = 'flat-ads-crm-oms/';

const FILE_CATEGORY = ['image', 'video', 'audio', 'text', 'application', 'other'];
const PREVIEW_URL = 'http://8.214.100.26:6540';

export { BaseData, RegExpRule, SavePath, AppConfig, AuthPrefix, RolePrefix, Bucket, FILE_CATEGORY, PREVIEW_URL };

export enum OperateStatus {
  Failed,
  Success
}

export enum CONTRACT_DATA_TYPE {
  ALL = 'all',
  PENDING = 'pending',
  PROCESSED = 'processed'
}

export enum CONTRACT_TYPE_ENUM {
  DRAFT = 'draft',
  ASSET_APPROVAL = 'asset_approval',
  FINANCIAL_APPROVAL = 'financial_approval',
  TAX_APPROVAL = 'tax_approval',
  LEGAL_APPROVAL = 'legal_approval',
  OA_PENDING = 'oa_pending',
  OA_APPROVAL = 'oa_pending_approval',
  DUAL_SIGNATURE_REVIEW = 'dual_signature_review',
  COMPLETED = 'completed',
  OA_REJECTED = 'oa_rejected',
  APPROVAL_REJECTED = 'approval_rejected'
}

export const SUPER_ADMIN_KEY = 'role-superadmin';
export const ADMIN_KEY = 'role-admin';
export const ADV_OP_KEY = 'role-axq7oy';
export const PUB_OP_KEY = 'role-veqxz0';
export const ADV_BD_KEY = 'role-q7fn12';
export const PUB_BD_KEY = 'role-e7t8az';
export const BD_LEADER_KEY = 'role-3i4eks';
export const BD_ASSISTANT_KEY = 'role-8dbkqs';
export const MARKETER_KEY = 'role-kajphl';
export const FINANCE_KEY = 'role-ezj3h6';
export const TAX_KEY = 'role-z52aaq';
export const LEGAL_KEY = 'role-5gthgm';
export const ASSET_GROUP_KEY = 'role-ux31nc';
export const OP_LEADER_KEY = 'role-itc1ds';
export const PROGRAMMATIC_OP_KEY = 'role-j9yrxk';

export const userIsADMIN = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(SUPER_ADMIN_KEY) || user?.roleCode?.includes(ADMIN_KEY);
};

/**
 * 用户是否是OP(运营)
 * @param user
 * @returns boolean
 */
export const userIsOP = (user: { roleCode: string[] }) => {
  return (user?.roleCode?.includes(ADV_OP_KEY) || user?.roleCode?.includes(PUB_OP_KEY)) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是上游运营
 * @param user
 * @returns boolean
 */
export const userIsAdvOP = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(ADV_OP_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是上游运营
 * @param user
 * @returns boolean
 */
export const userIsPubOP = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(PUB_OP_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是运营领导
 */
export const userIsOPLeader = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(OP_LEADER_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是BD(商务)
 * @param user
 * @returns boolean
 */
export const userIsBD = (user: { roleCode: string[] }) => {
  return (user?.roleCode?.includes(ADV_BD_KEY) || user?.roleCode?.includes(PUB_BD_KEY)) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是上游BD(商务)
 * @param user
 * @returns boolean
 */
export const userIsAdvBD = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(ADV_BD_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是下游BD(商务)
 * @param user
 * @returns boolean
 */
export const userIsPubBD = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(PUB_BD_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是BD Leader(商务负责人)
 * @param user
 * @returns boolean
 */
export const userIsBDLeader = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(BD_LEADER_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是BD Assistant(商务助理)
 * @param user
 * @returns boolean
 */
export const userIsBDAssistant = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(BD_ASSISTANT_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是Marketer(市场)
 * @param user
 * @returns boolean
 */
export const userIsMarketer = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(MARKETER_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是Finance(财务)
 * @param user
 * @returns boolean
 */
export const userIsFinance = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(FINANCE_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是Tax(法务)
 * @param user
 * @returns boolean
 */
export const userIsTax = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(TAX_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是Legal(法务)
 * @param user
 * @returns boolean
 */
export const userIsLegal = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(LEGAL_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 *
 */
export const userIsProgrammaticOp = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(PROGRAMMATIC_OP_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

/**
 * 用户是否是AssetGroup(资产组)
 * @param user
 * @returns boolean
 */
export const userIsAssetGroup = (user: { roleCode: string[] }) => {
  return user?.roleCode?.includes(ASSET_GROUP_KEY) && !user?.roleCode?.includes(ADMIN_KEY) && !user?.roleCode?.includes(SUPER_ADMIN_KEY);
};

export const userISDemoAccount = (user: { roleCode: string[], id: number }) => {
  return user.id === 579;
};

export function clientRelatedFieldHandler(clueInfo: Record<string, any> = {}, curUserInfo) {

  const isOP = userIsOP(curUserInfo);
  const isBD = userIsBD(curUserInfo);
  const isBDLeader = userIsBDLeader(curUserInfo);
  const isBdAssist = userIsBDAssistant(curUserInfo);
  const isMkt = userIsMarketer(curUserInfo);
  if ((isMkt || isOP || isBdAssist) && clueInfo.client_id) {
    // 线索其他状态可见，转换客户后不能见
    return false;
  }
  if (isBD && clueInfo.client_id) {
    // 线索其他状态可见，转换客户后，BD是自己的才能见
    if (Number(clueInfo.bd_id) !== Number(curUserInfo.id)) {
      return false;
    }
  }

  return true;
}
export type NodeType = 'apply_submit_to_asset' | 'apply_submit_to_financial' |
  'asset_submit' | 'asset_reject' |
  'finance_submit' | 'finance_reject' |
  'tax_submit' | 'tax_reject' |
  'legal_submit' | 'legal_reject'
  | 'transfer';

export enum CONTRACT_CREATE_TYPE {
  TO_AUDIT,
  DRAFT,
  ASSET_SUBMIT,
  ASSET_REJECT,
  FINANCIAL_SUBMIT,
  FINANCIAL_REJECT,
  TAX_SUBMIT,
  TAX_REJECT,
  LEGAL_SUBMIT,
  LEGAL_REJECT,
  RESUBMIT_TO_AUDIT
}

export const AUDIT_SUBMIT_TYPE = {
  [CONTRACT_CREATE_TYPE.ASSET_SUBMIT]: 'asset_submit',
  [CONTRACT_CREATE_TYPE.FINANCIAL_SUBMIT]: 'financial_submit',
  [CONTRACT_CREATE_TYPE.TAX_SUBMIT]: 'tax_submit',
  [CONTRACT_CREATE_TYPE.LEGAL_SUBMIT]: 'legal_submit'
};

export const CONTRACT_TYPE = [
  { label: '推广类一网盟上下游合作 (直客，程序化，afiliate,休闲游戏等）advertising Network', value: '1' },
  { label: '推广类一一渠道推广（大媒体，ASO，SEO等）', value: '2' },
  // { label: '推广类——营销（网红合作、视频采买等）', value: '3' },
  { label: '联运、发行类（我方为授权方或定制委托方，含游戏定制、美术外包）', value: '4' },
  { label: '联运、发行类（我方为被授权方）', value: '11' },
  // { label: '支付中台业务合同', value: '5' },
  // { label: '第三方服务类（归因；第三方工具如素材库、数据库，第三方服务如声网、融云）', value: '6' },
  // { label: '运维类（短信、服务器、运维设备采购）', value: '7' },
  // { label: '版权类（版权授权、购买）', value: '8' },
  // { label: '技术类（代码采购、技术服务）', value: '9' },
  // { label: '其它（行政、人事、参会、财法代理、咨询、审计等）', value: '10' },
];

export const CONTRACT_TEMPLATE = [
  {
    label: 'Our template（我方模板）', value: '1', children: [
      { label: 'With modifications （有修改）', value: '1-1' },
      { label: 'No modifications（无修改）', value: '1-2' },
    ]
  },
  { label: 'Partner template（对方模板）', value: '2' },
];

export const CONTRACT_IS_EXCEED_AMOUNT = [
  { label: '是 yes', value: '1' },
  { label: '否 no', value: '0' },
  { label: '框架合同无金额 no specific amount', value: '2' },
];

export const LEAD_STATUS_COLOR_MAP = {
  'unassigned': '',
  'assigned': 'green',
  'conversion_failed': 'volcano',
  'converted': 'volcano',
  'invalid': 'volcano',
  'mismatch': 'volcano',
  'other': 'blue',
  'deleted': 'error',
  'contacted': 'green'
};

export const CONTRACT_STATUS = [
  { label: 'Draft', value: CONTRACT_TYPE_ENUM.DRAFT, color: '', isTag: false },
  { label: 'Finance BP Approval', value: CONTRACT_TYPE_ENUM.ASSET_APPROVAL, color: 'orange', isTag: true },
  { label: 'Finance Approval', value: CONTRACT_TYPE_ENUM.FINANCIAL_APPROVAL, color: 'orange', isTag: true },
  { label: 'Tax Approval', value: CONTRACT_TYPE_ENUM.TAX_APPROVAL, color: 'orange', isTag: true },
  { label: 'Legal Approval', value: CONTRACT_TYPE_ENUM.LEGAL_APPROVAL, color: 'orange', isTag: true },
  { label: 'OA Pending', value: CONTRACT_TYPE_ENUM.OA_PENDING, color: 'blue', isTag: true },
  { label: 'OA Approval', value: CONTRACT_TYPE_ENUM.OA_APPROVAL, color: 'geekblue', isTag: true },
  { label: 'Dual-signature review', value: CONTRACT_TYPE_ENUM.DUAL_SIGNATURE_REVIEW, color: 'geekblue', isTag: true },
  { label: 'Completed', value: CONTRACT_TYPE_ENUM.COMPLETED, color: '#389e0d', isTag: false },
  { label: 'OA Rejected', value: CONTRACT_TYPE_ENUM.OA_REJECTED, color: 'error', isTag: true },
  { label: 'Approval Rejected', value: CONTRACT_TYPE_ENUM.APPROVAL_REJECTED, color: 'error', isTag: true },
];

export const COOP_BUS_ENUM = {
  AFF_ADV_GROUP: `AFF_ADV_`,
  AFF_PUB_GROUP: `AFF_PUB_`,
  ADX_DEMAND_GROUP: `ADX_DMD_`,
  ADX_PUB_GROUP: `ADX_PUB_`,
  DSP_AGENCY: 'DSP_Agency_',
  QP_CHANNEL: 'QP_Chan_',
  QP_CUSTOMER: 'QP_Cust_',
  OTHER_THIRD: 'Other_Thirt_',
  OTHER_MEDIABUY: 'Other_Mediabuy_',
  OTHER_H5_ADV: 'H5_ADV_',
  OTHER_H5_PUB: 'H5_PUB_',
  OTHER_H5_CONTENT_PROVIDER: 'H5_PROVIDER_',
  OTHER_H5_TRAFFIC_CHANNEL: 'H5_CHANNEL_'
};

export const COOP_BUS_PREFIX_TO_ORIGIN = (coopBusPrefix: string) => {
  if (coopBusPrefix.includes('AFF_ADV')) {
    return ['Affiliate,Advertiser Group'];
  }
  if (coopBusPrefix.includes('AFF_PUB')) {
    return ['Affiliate,Publisher Group'];
  }
  if (coopBusPrefix.includes('ADX_DMD')) {
    return ['Adx,Demand Group', 'Commercialize,Interative Demand Group'];
  }
  if (coopBusPrefix.includes('ADX_PUB')) {
    return ['Adx,Publisher Group', 'Commercialize,Interative Publisher Group'];
  }
  if (coopBusPrefix.includes('DSP_Agency')) {
    return ['Commercialize,DSP Agency'];
  }
  if (coopBusPrefix.includes('QP_Chan')) {
    return ['Quarkpay,Payment channel'];
  }
  if (coopBusPrefix.includes('QP_Cust')) {
    return ['Quarkpay,Payment customer'];
  }
  if (coopBusPrefix.includes('Other_Thirt')) {
    return ['Other,Third party services'];
  }
  if (coopBusPrefix.includes('Other_Mediabuy')) {
    return ['Other,Mediabuy'];
  }
  if (coopBusPrefix.includes('H5_ADV')) {
    return ['Other,H5 Advertiser'];
  }
  if (coopBusPrefix.includes('H5_PUB')) {
    return ['Other,H5 Publisher'];
  }
  if (coopBusPrefix.includes('H5_PROVIDER')) {
    return ['Other,H5 Content Provider'];
  }
  if (coopBusPrefix.includes('H5_CHANNEL')) {
    return ['Other,H5 Traffic Channel'];
  }

};

export const COOP_BUS_ENUM_ARR = Object.values(COOP_BUS_ENUM);

export const GET_COOP_BUS_TO_ENUM = (coopBus: string) => {
  if (coopBus.includes('Affiliate,Advertiser Group')) {
    return COOP_BUS_ENUM.AFF_ADV_GROUP;
  }
  if (coopBus.includes('Affiliate,Publisher Group')) {
    return COOP_BUS_ENUM.AFF_PUB_GROUP;
  }
  if (coopBus === 'Adx,Demand Group' || coopBus === 'Commercialize,Interative Demand Group') {
    return COOP_BUS_ENUM.ADX_DEMAND_GROUP;
  }
  if (coopBus.includes('Adx,Publisher Group') || coopBus.includes('Commercialize,Interative Publisher Group')) {
    return COOP_BUS_ENUM.ADX_PUB_GROUP;
  }
  if (coopBus === 'Commercialize,DSP Agency') {
    return COOP_BUS_ENUM.DSP_AGENCY;
  }
  if (coopBus === 'Quarkpay,Payment channel') {
    return COOP_BUS_ENUM.QP_CHANNEL;
  }
  if (coopBus === 'Quarkpay,Payment customer') {
    return COOP_BUS_ENUM.QP_CUSTOMER;
  }
  if (coopBus === 'Other,Third party services') {
    return COOP_BUS_ENUM.OTHER_THIRD;
  }
  if (coopBus === 'Other,Mediabuy') {
    return COOP_BUS_ENUM.OTHER_MEDIABUY;
  }
  if (coopBus === 'Other,H5 Advertiser') {
    return COOP_BUS_ENUM.OTHER_H5_ADV;
  }
  if (coopBus === 'Other,H5 Publisher') {
    return COOP_BUS_ENUM.OTHER_H5_PUB;
  }
  if (coopBus === 'Other,H5 Content Provider') {
    return COOP_BUS_ENUM.OTHER_H5_CONTENT_PROVIDER;
  }
  if (coopBus === 'Other,H5 Traffic Channel') {
    return COOP_BUS_ENUM.OTHER_H5_TRAFFIC_CHANNEL;
  }
  return 'Other_';

};

export const GET_COOP_BUS_TO_SIMPLIFY = (coopBus: string) => {
  if (coopBus.includes('Affiliate,Advertiser Group')) {
    return `Affiliate - Adv - ${coopBus.split(',').pop()}`;
  }
  if (coopBus.includes('Affiliate,Publisher Group')) {
    return `Affiliate - Pub`;
  }
  if (coopBus === 'Adx,Demand Group' || coopBus === 'Commercialize,Interative Demand Group') {
    return `Adx - Dmd`;
  }
  if (coopBus.includes('Adx,Publisher Group') || coopBus.includes('Commercialize,Interative Publisher Group')) {
    return `Adx - Pub - ${coopBus.split(',').pop()}`;
  }
  if (coopBus === 'Commercialize,DSP Agency') {
    return coopBus.split(',').join(' - ');
  }
  if (coopBus === 'Quarkpay,Payment channel') {
    return `Quarkpay - Channel`;
  }
  if (coopBus === 'Quarkpay,Payment customer') {
    return `Quarkpay - Customer`;
  }
  if (coopBus === 'Other,Third party services') {
    return `Other - Third party services`;
  }
  if (coopBus === 'Other,Mediabuy') {
    return `Other - Mediabuy`;
  }
  if (coopBus === 'Other,H5 Advertiser') {
    return `Other - H5 Advertiser`;
  }
  if (coopBus === 'Other,H5 Publisher') {
    return `Other - H5 Publisher`;
  }
  if (coopBus === 'Other,H5 Content Provider') {
    return `Other - H5 Content Provider`;
  }
  if (coopBus === 'Other,H5 Traffic Channel') {
    return `Other - H5 Traffic Channel`;
  }

};

export const COMMON_BEFORE_FILTER = (filters: Record<string, any>) => {
  for (const key in filters) {
    if (!Object.prototype.hasOwnProperty.call(filters, key)) { continue; }
    if (Array.isArray(filters[key]) && !Array.isArray(filters[key][0])) {
      if (key === 'ctime') {
        filters.ctime = filters.ctime.map((it, index) => {
          if (index === 0) {
            return moment(it).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          } else {
            return moment(it).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          }
        }).join(OPTION_SEPARATOR);
      } else {
        filters[key] = filters[key].join(OPTION_SEPARATOR);
      }
      continue;
    }
    if (Array.isArray(filters[key])) {
      filters[key] = filters[key].map(it => it.join(',')).flat(Infinity).join(OPTION_SEPARATOR);

    }
  }
  return filters;

};

export const GET_COOP_BUS_STR = ({ association_id, coop_bus, id }) => {

  const coopPrefix = GET_COOP_BUS_TO_ENUM(coop_bus);
  return `${coopPrefix}${association_id || id}`;
};

export const CUSTOMER_STATUS_MAP = {
  0: 'Not Cooperating',
  1: 'Cooperated'
};

export const FOLLOW_UP_RECORD_TYPE = [
  { label: 'Weekly Follow-up', value: 1 },
  { label: 'Meetting Minutes', value: 2 },
  { label: 'Business Trip Report', value: 3 },
];

export const FOLLOW_UP_KEY_INFO = [
  { label: 'Key Info', value: 1 },
  { label: 'Not Key Info', value: 0 }
];

export const TODO_TYPE = [
  { label: 'Finance', value: 1 },
  { label: 'Contract', value: 2 },
  { label: 'Leads', value: 3 },
];

export const TODO_STEP = [
  { label: 'Revenue Confirm Billing', value: 1 },
  { label: 'Cost Confirm Billing', value: 2 },
  { label: 'Issue Invoice', value: 3 },
  { label: 'Stamp Invoice', value: 4 },
  { label: 'Match Payment', value: 5 },
  { label: 'Confirm Payment', value: 6 },
  { label: 'Confirm Skipping Invoice', value: 7 },
  { label: 'Confirm Write-off', value: 8 }
];

export const TODO_STATUS = [
  { label: 'Pending', value: 1 },
  { label: 'Completed', value: 2 },
  { label: 'Overdue', value: 3 },
];

export const TODO_STEP_TO_ROLE = {
  1: 'OP',
  2: 'OP',
  3: 'BD',
  4: 'FI',
  5: 'BD',
  6: 'FI',
  7: 'FI',
  8: 'FI'
};

export const COMMISSION_ADV_TYPE = [
  { label: 'Agency', value: 'Agency' },
  { label: 'Direct', value: 'Direct' },
  { label: 'Programmatic DSP', value: 'Programmatic DSP' }
];

export const COMMISSION_PUB_TYPE = [
  { label: 'Incbr', value: 'Incbr' },
  { label: 'External', value: 'External' },
];

export const ADS_PRESET_ROLE_CODE = {
  SUPER_ADMIN: 'role-superadmin',
  ADMIN: 'role-admin',
  PA_Manager: 'role-961ys0',
  INTERACTIVE_MANAGER: 'role-aw6uhi',
  OP_LEADER: 'role-grgkmo',
  BD_LEADER: 'role-ee6030',
  ADV_OP: 'role-x9balv',
  PUB_OP: 'role-ek6nsj',
  ADV_BD: 'role-em9n5q',
  PUB_BD: 'role-endfl8',
  FINANCE: 'role-5wt95y',
  ALL_DATA_BUT_NOT_ADMIN: 'role-5naqdd',
  NEWCOMER: 'role-895mnd'
};
