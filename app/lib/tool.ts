/**
 * 此文件为 pure function，可同时用于前后端
 */
import dayjs from 'dayjs';
import moment from 'moment';
export { isPureObject, makeCode, noop, urlChildren, getTargetText };

function isPureObject(obj) {
  return Object.prototype.toString.call(obj).slice(-7, -1) === 'Object';
}

/**
 * 此文件为 pure function，可同时用于前后端
 */

// tslint:disable-next-line:ban-types
export const isFunction = (value: unknown): value is Function => typeof value === 'function';
export const isString = (value: unknown): value is string => typeof value === 'string';
export const isBoolean = (value: unknown): value is boolean => typeof value === 'boolean';
export const isNumber = (value: unknown): value is number => typeof value === 'number';
export const isUndef = (value: unknown): value is undefined => typeof value === 'undefined';

export function deepClone(obj, hash = new WeakMap()) {
  if (Object(obj) !== obj) {
    return obj; // primitives
  }

  if (obj instanceof Date) {
    return new Date(obj);
  }

  if (obj instanceof RegExp) {
    return new RegExp(obj);
  }

  // Avoid circular references
  if (hash.has(obj)) {
    return hash.get(obj);
  }

  let result;
  if (Array.isArray(obj)) {
    result = [];
    hash.set(obj, result);
    obj.forEach((item, index) => {
      result[index] = deepClone(item, hash);
    });
    return result;
  }

  if (obj instanceof Map) {
    result = new Map();
    hash.set(obj, result);
    obj.forEach((value, key) => {
      result.set(key, deepClone(value, hash));
    });
    return result;
  }

  if (obj instanceof Set) {
    result = new Set();
    hash.set(obj, result);
    obj.forEach(value => {
      result.add(deepClone(value, hash));
    });
    return result;
  }

  if (isObject(obj)) {
    result = Object.create(Object.getPrototypeOf(obj));
    hash.set(obj, result);
    Object.keys(obj).forEach(key => {
      result[key] = deepClone(obj[key], hash);
    });
    return result;
  }

  throw new Error('Unable to copy obj! Its type isn\'t supported.');
}

function makeCode(length: number = 6, characters?: string) {
  let result = '';
  const str = characters || 'abcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = str.length;
  for (let i = 0; i < length; i++) {
    result += str.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

function urlChildren(url, count = 3) {
  const result = { isValid: false, children: [] };
  const tempArr = url.split('/');
  if (!url || tempArr.length === 1) {
    return result;
  }
  result.isValid = true;
  for (let i = 0; i < count; i++) {
    tempArr.splice(tempArr.length - 1, 1);
    const child = tempArr.join('/');
    if (child) {
      result.children.unshift(child);
    }
  }

  return result;
}

function noop() { }

function getTargetText(value, arr) {
  return arr.filter(item => item.value === value)[0].text;
}

// 密码校验
export const isPasswordValid = pwd => {
  const regex = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}');
  return regex.test(pwd);
};
// 邮箱校验
export const isEmail = email => {
  const regex = new RegExp(/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/);
  return regex.test(email);
};

export const debounce = (func, delay) => {
  let timer;
  return (...args) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

// 对象数组根据key重新分组
export const groupByKey = (originalArr, field, type: 'create' | 'update' = 'create') => {
  const tempArr = [];
  const endData = [];
  for (let i = 0; i < originalArr.length; i++) {
    if (tempArr.indexOf(originalArr[i][field]) === -1) {
      if ((type === 'create' && !originalArr[i]?.create?.hidden) || ((type === 'update' && !originalArr[i]?.update?.hidden))) {
        endData.push({
          [field]: originalArr[i][field],
          data: [originalArr[i]]
        });
        tempArr.push(originalArr[i][field]);
      }

    } else {
      for (let j = 0; j < endData.length; j++) {
        if (endData[j][field] === originalArr[i][field]) {
          endData[j].data.push(originalArr[i]);
          break;
        }
      }
    }
  }
  return endData;
};

export const formatAuth = (list, key = 'auth_code', parentKey = 'parent') => {
  if (!list) {
    return;
  }
  const map = {};
  const tree = {
    menu: [],
    pathList: []
  };
  let i: number;
  let node: any;
  for (i = 0; i < list.length; i++) {
    map[list[i][key]] = list[i]; // initialize the map
    map[list[i][key]].children = []; // initialize the children
    if (list[i].type === 'menu') {
      map[list[i][key]].path = `/${list[i].name}`; // initialize the children
    }

    tree[list[i].type] = [];
  }

  const tempMap = JSON.parse(JSON.stringify(map));

  // @ts-ignore
  for (const code in map) {
    if (map.hasOwnProperty(code)) {
      node = map[code];
      if (node[parentKey] !== '0') {
        if (node.type === 'menu') {
          node.path = getPath(node);
        }
        if (map[node[parentKey]]) {
          map[node[parentKey]].children.push(node);
          // 按顺序进行排序，如 0,1,2,3,4
          map[node[parentKey]].children = map[node[parentKey]].children.sort((a, b) => a.position - b.position);
        }
      } else {
        tree[node.type].push(node);
        // 按顺序进行排序，如 0,1,2,3,4
        tree[node.type] = tree[node.type].sort((a, b) => a.position - b.position);
      }
    }
  }

  getPathList(tree.menu);

  // 遍历获取可以访问的path
  function getPathList(arr) {
    if (!arr || arr.length === 0) {
      return;
    }
    arr.forEach(item => {
      tree.pathList.push(item.path);
      getPathList(item.children);
    });
  }

  function getPath(node) {
    const parent = tempMap[node[parentKey]];
    if (parent) {
      return getPath(parent) + node.path;
    } else {
      return node.path;
    }
  }
  return tree;
};

export const hasChineseStr = str => {
  const reg = /.*[\u4e00-\u9fa5]+.*$/;
  if (reg.test(str)) {
    return true;
  }
  return false;
};

export const hasEmojiStr = (str) => {
  const reg = /\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD]|[\u2660-\u2767]\u0020)/g;
  if (reg.test(str)) {
    return true;
  }
  return false;
};
// 校验是否为包名, 特殊字符校验, 包名只包含 26字母（大小写）数字 和 "." "_"
export const isPackageFormat = (str: string) => {
  let isformat = false;
  if (str) {
    isformat = /^[a-z\d\_\.]+$/i.test(str);
  }
  return isformat;
};

// 防抖函数
let tId: number = 0;
export const debounceFn = (method: (params?: object) => void, context: any, delay?: number) => {
  return function () {
    if (tId) {
      window.clearTimeout(tId);
    }
    tId = window.setTimeout(() => {
      method.call(context);
    }, delay || 500);
  };
};

export function throttle(fn: (...args: any[]) => void, delay: number) {
  let timer: NodeJS.Timeout | null = null;

  return function (...args: any[]) {
    if (timer) { return; }
    timer = setTimeout(() => {
      fn(args);
      timer = null;
    }, delay);
  };
}

export const isObject = (object) => {
  return object != null && typeof object === 'object';
};

// 对象比较
export const objectEqual = (object1, object2) => {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let index = 0; index < keys1.length; index++) {
    const val1 = object1[keys1[index]];
    const val2 = object2[keys2[index]];
    const areObjects = isObject(val1) && isObject(val2);
    if (areObjects && !objectEqual(val1, val2) ||
      !areObjects && val1 !== val2) {
      return false;
    }
  }
  return true;
};

/**
 * 检测密码是否符合规范;必须包含大小字母、数字、特称字符，至少8个字符
 * @param {*} str
 */
export function validUserPass(str) {
  let res = false;
  const regex = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}');
  if (regex.test(str)) {
    res = true;
  }
  return res;
}

/**
 * 获取分页数据
 */
export const getPagingData = ({ pageIndex: pi, pageSize: ps }: { pageIndex: string, pageSize: string }) => {
  const pageIndex = (pi && parseInt(pi, 10)) || 1;
  const pageSize = (ps && parseInt(ps, 10)) || 20;
  const offset = (pageIndex - 1) * pageSize;
  return {
    limit: pageSize,
    offset
  };
};

export const createRandomChar = () => {
  let result: string = '';
  let passwordList: string[] = [];
  const CREATE_COUNT_MAX = 10;
  // A~Z, a-z, 0-9, #$%^&, 16位密码
  const passStr = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ~0123456789<>()[]?:_-@!+=#$%^&';
  passwordList = passStr.split('');
  const len: number = passwordList.length;
  const getRandomChar = () => {
    let rChar = '';
    for (let i = 0; i < 16; i++) {
      const randomIndex = Math.floor(Math.random() * len);
      rChar += passwordList[randomIndex];
    }
    return rChar;
  };
  result = getRandomChar();
  if (!validUserPass(result)) { // 最多动态生成10次
    for (let i = 0; i < CREATE_COUNT_MAX; i++) {
      result = getRandomChar();
      if (validUserPass(result)) {
        break;
      }
    }
  }
  return result;
};

/**
 * 检测提交数据是否变更
 */
export const verifyChange = (object1, object2) => {
  // console.log('object1:', object1);
  // console.log('object2:', object2);
  const commonKeys = [];
  const object1Keys = Object.keys(object1);
  const object2Keys = Object.keys(object2);
  for (const object1Key of object1Keys) {
    for (const object2Key of object2Keys) {
      if (object1Key === object2Key && object2Key !== 'key') {
        commonKeys.push(object2Key);
        break;
      }
    }
  }
  const newObject1 = {};
  const newObject2 = {};
  for (const key of commonKeys) {
    newObject1[key] = object1[key];
    newObject2[key] = object2[key];
  }
  const isEqual = objectEqual(newObject1, newObject2);
  if (isEqual) {
    return false;
  }
  return true;
};

/** 获取字符串截取前后新字符  */
export const getTrimStr = (str) => {
  let newStr = str;
  if (str && str.trim) {
    newStr = newStr.trim();
    // 处理特殊字符， 回车、tab等不可见字符
    newStr = newStr.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
  }
  return newStr;
};

/**
 * 格式化api
 * @param url
 */
export const formatApi = (url) => {
  if (!url) {
    return;
  }
  let newUrl = url;
  const startIndex = newUrl.indexOf('/');
  if (startIndex !== 0) {
    newUrl = '/' + newUrl;
  }
  const endIndex = newUrl.lastIndexOf('/');
  if (endIndex === (newUrl.length - 1)) {
    newUrl = newUrl.substring(0, endIndex);
  }
  return newUrl;
};

/**
 * 字符串转驼峰
 */
export const strToCamel = (str) => {
  if (!str) {
    return;
  }
  let newStr = str;
  let arr = newStr.split('_');
  if (arr.length <= 1) {
    arr = newStr.split('-');
  }
  newStr = arr[0];
  for (let i = 1; i < arr.length; i++) {
    newStr += arr[i].substr(0, 1).toUpperCase() + arr[i].substr(1);
  }
  return newStr;
};

/**
 * 驼峰转字符串
 */

export const camelToStr = (str) => {
  const arr = str.split('');
  if (arr.length > 0) {
    arr[0] = arr[0].toLowerCase();
  }
  for (let i = 1; i < arr.length; i++) {
    if (/[A-Z]/.test(arr[i])) {
      arr[i] = '-' + arr[i].toLowerCase();
    }
    arr[i] = arr[i].replace('_', '-'); // 统一转中横杠格式
  }
  const newStr = arr.join('').replace(/--/g, '-');
  return newStr;
};

/**
 * @param mapData 如：CONTENT_STATUS = { '0': '下线', '1': '在线', '-1': '已删除' }
 * @param includeAll 是否包含“全部”选项，默认否
 * @returns 下拉选择列表，如：[{ label: '下线', value: '0' }, { label: '在线', value: '1' }, { label: '已删除', value: '-1' }]
 */
export const getOptionsByMap = (mapData: any, includeAll = false) => {
  let options: any = [];
  if (includeAll) {
    options = [{
      label: '全部',
      value: ''
    }];
  }

  Object.keys(mapData).map(key => {
    options.push({
      label: mapData[key],
      value: key
    });
  });
  return options;
};

export function isBrowser() {
  // 检查 window 对象
  if (typeof window === 'undefined') {
    return false;
  }

  // 检查 window 对象的典型属性
  if (typeof window.document !== 'object' || typeof window.navigator !== 'object') {
    return false;
  }

  // 检查 userAgent 字符串
  const userAgent = window.navigator.userAgent;
  const browserIndicators = ['Mozilla', 'Chrome', 'Safari', 'Edge', 'Opera', 'Firefox'];
  for (const indicator of browserIndicators) {
    if (userAgent.indexOf(indicator) !== -1) {
      return true;
    }
  }

  return false;
}

export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export const rangePickerRangesNotToday: Record<string, any> = {
  Yesterday: () => [moment(new Date()).add(-1, 'days'), moment(new Date()).add(-1, 'days')],
  'Last 7 days': () => [moment(new Date()).subtract(7, 'days'), moment().subtract(1, 'days')],
  'Last 30 days': () => [moment(new Date()).subtract(30, 'days'), moment().subtract(1, 'days')],
  'Last month': () => [
    moment()
      .month(moment().month() - 1)
      .startOf('month'),
    moment()
      .month(moment().month() - 1)
      .endOf('month')
  ]
};

export function timeAgo(date: string) {
  const now = moment();
  const past = moment(date);
  const diffInSeconds = now.diff(past, 'seconds');

  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  }

  const diffInMinutes = now.diff(past, 'minutes');
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minutes ago`;
  }

  const diffInHours = now.diff(past, 'hours');
  if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  }

  const diffInDays = now.diff(past, 'days');
  if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  }

  const diffInWeeks = now.diff(past, 'weeks');
  if (diffInWeeks < 4) {
    return `${diffInWeeks} weeks ago`;
  }

  const diffInMonths = now.diff(past, 'months');
  if (diffInMonths < 12) {
    // 处理 0 months ago 的情况
    if (diffInMonths === 0 && diffInWeeks < 4) {
      return `${diffInWeeks} weeks ago`;
    }

    if (diffInMonths === 0 && diffInDays < 30) {
      return `${diffInDays} days ago`;
    }
    return `${diffInMonths} months ago`;
  }

  const diffInYears = now.diff(past, 'years');
  return `${diffInYears} years ago`;
}

// 头字母大写
export function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
// 判断数组是否有效
export const isValidArray = (arr: any) => Array.isArray(arr) && arr.length > 0;
export const isValidString = (s: any) => isString(s) && s.length > 0;

//  newArr, oldArr 两个数组对比，得出删除元素的数组 delArr，增加元素的数组addArr
export function twoArraysCompare<T>(newArr: T[], oldArr: T[], key?: string) {
  const delArr = [];
  const addArr = [];

  // 如果提供了key，说明数组元素是对象结构，否则假定数组元素是简单类型
  if (key) {
    oldArr.forEach(oldItem => {
      if (!newArr.some(newItem => newItem[key] === oldItem[key])) {
        delArr.push(oldItem);
      }
    });

    newArr.forEach(newItem => {
      if (!oldArr.some(oldItem => oldItem[key] === newItem[key])) {
        addArr.push(newItem);
      }
    });
  } else {
    oldArr.forEach(oldItem => {
      if (!newArr.includes(oldItem)) {
        delArr.push(oldItem);
      }
    });

    newArr.forEach(newItem => {
      if (!oldArr.includes(newItem)) {
        addArr.push(newItem);
      }
    });
  }

  return { delArr, addArr };
}
/**
 *
 * @param fileUrl xxxx-timestamp.xxx
 * @returns xxx.xxx
 */
export function fileUrlToFileName(fileUrl: string = '') {
  if (!fileUrl) {
    return '';
  }
  const fileName = fileUrl.split('/').pop();
  return fileName.replace(/-\d+/, '');
}

export const clearUrlParameter = () => {
  if (!location.search) { return; }
  const url = new URL(window.location.href);
  window.history.replaceState(null, '', url.href.split('?')[0]); // 清除url参数

};

export const render$StringValue = (text: string | number, minDigit: number = 0, maxDigit: number = 2) => {
  return `$${Number(text || 0).toLocaleString('en-US', {
    maximumFractionDigits: maxDigit,
    minimumFractionDigits: minDigit
  })}`;
};

export const getUniqueArray = (arr: Record<string, any>, key: string) => {
  const res = new Map();
  return arr.filter((item) => !res.has(item[key]) && res.set(item[key], 1));
};

export const isNumberReg = /^\d+$/;

/**
 * 对象数组去重
 * @param array 原始数组
 * @param key 对象的唯一标识键
 * @returns 去重后的数组
 */
export function deduplicateArray<T>(array: T[], key: keyof T): T[] {
  const seen = new Set<T[keyof T]>();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}
