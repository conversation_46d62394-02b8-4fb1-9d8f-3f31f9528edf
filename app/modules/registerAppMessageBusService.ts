import { Application } from 'egg';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../lib/systemEvent';

export function registerAppMessageBusService(app: Application) {
  if (app.redis && app.sseBus) {
    app.redis.get('sub').subscribe(app.config.sun.redisChannel, (error: Error, count: number) => {
      if (error) {
        app.coreLogger.error(`[registerAppMessageBusService]`, error);
      } else {
        app.redis.get('sub').addListener('message', (channel: string, message: string) => {
          try {
            if (channel !== app.config.sun.redisChannel || !message) { return; }
            const { event, data = {} } = JSON.parse(message);
            switch (event) {
              case APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS:
                app.sseBus.publish(String(data?.targetUser?.id), {
                  event: APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS,
                  data: JSON.stringify(data)
                });
              case APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT:
                app.sseBus.publish(String(data?.targetUser?.id), {
                  event: APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT,
                  data: JSON.stringify(data)
                });
                break;
            }
          } catch (e) {
            app.coreLogger.error('[registerAppMessageBusService]', e);
          }
        });
      }
    });
  }
}
