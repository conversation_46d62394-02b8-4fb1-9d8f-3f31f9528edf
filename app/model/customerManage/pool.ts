
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DATE, DECIMAL } = app.Sequelize;

  const CrmClient = app.model.define(
    'crm_client',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      client_name: STRING(256), // 客户名称,
      client_status: INTEGER(2), // 客户状态：已合作，未合作
      bus_line: STRING(52), // 归属部门：程序化，网盟，支付，商业化, 其他
      coop_bus: STRING(52), // 业务类型
      ret_reason: STRING(52), // 退回原因,
      company_name: STRING(52), // 公司名称,
      comp_address: STRING(128), // 公司地址
      tag_reg_no: STRING(52), // 税号
      country: STRING(10), // 国家
      pay_type: STRING(52), // 付款类型： 预付 /  后付 /  无
      remark: STRING(256), // 备注,
      payment_legal_name: STRING(512), // 付款方名称
      association_id: INTEGER(11), // 关联广告系统客户的ID

      // 合同相关字段 开始
      contract_code: STRING(52), // 合同编号
      contract_type: STRING(52), // 合同类型
      template: STRING(52), // 合同模板
      bu: STRING(52), // 业务单元
      payment_terms: STRING(52), // 付款方式
      is_exceed_amount: STRING(20), // 是否超额
      currency: STRING(5), // 币种
      amount: DECIMAL(11, 3), // 合同金额
      our_company_name: STRING(52), // 我方公司名
      our_contact_person: STRING(52), // 我方联系人
      our_information: STRING(52), // 我方联系方式
      partner_company_name: STRING(52), // 合作方签约方
      partner_contact_person: STRING(52), // 合作方联系人
      partner_information: STRING(52), // 合作方联系方式
      bus_brand_name: STRING(52), // 业务品牌
      usdt_account_id: STRING(2048), // 关联USDT账户ID
      other_payment_terms: STRING(256), // 其他特殊付款条款，如：返点，税率等
      effective_date: DATE, // 生效日期
      expiry_date: DATE, // 到期日期
      // 合并相关字段 结束

      creator: STRING(52), // 创建人
      modifier: STRING(52), // 修改者
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      createdAt: false,
      updatedAt: false,
      ...config
    }
  );
  CrmClient.associate = () => {
    CrmClient.hasMany(app.model.ClueManage.Pool, { foreignKey: 'client_id', sourceKey: 'id' });
    app.model.ClueManage.Pool.belongsTo(CrmClient, { foreignKey: 'client_id', targetKey: 'id' });
  };

  return CrmClient;
};
