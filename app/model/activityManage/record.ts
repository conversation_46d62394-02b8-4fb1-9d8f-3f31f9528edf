
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DATE, DECIMAL } = app.Sequelize;

  const CrmActive = app.model.define(
    'crm_active',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      name: STRING(52), // 活动名称,
      content: STRING(52), // 活动内容,
      start_time: STRING(52), // 活动开始时间,
      end_time: STRING(52), // 活动结束时间,
      cost: DECIMAL(10, 2), // 活动成本,
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );

  return CrmActive;
};
