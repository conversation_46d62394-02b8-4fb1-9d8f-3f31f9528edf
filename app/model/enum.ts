
import { config } from '../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DATE } = app.Sequelize;

  const CrmEnum = app.model.define(
    'crm_enum',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      enum_type: STRING(20), // 枚举类型,
      enum_code: STRING(20), // 枚举名称,
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      createdAt: false,
      updatedAt: false,
      ...config
    }
  );

  return CrmEnum;
};
