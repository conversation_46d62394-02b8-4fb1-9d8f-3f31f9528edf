
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DATE } = app.Sequelize;

  const CrmFollowUpRecord = app.model.define(
    'crm_follow_up_record',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      company_name: STRING(128), // 公司名称
      type: INTEGER(2),
      record_type: STRING(52), // 记录相关方：clue,client,
      clue_id: INTEGER(11), // 线索id
      relation_id: INTEGER(11), // 记录关联id，线索id或客户id,
      attachment: STRING(52), // 文件,
      content: STRING(512), // 跟进内容,
      key_info: INTEGER(2),
      record_time: String(12), // 跟进时间
      creator: STRING(52),
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );
  CrmFollowUpRecord.associate = function () {
    CrmFollowUpRecord.belongsTo(app.model.ClueManage.Pool, {
      foreignKey: 'relation_id',
      targetKey: 'id',
      as: 'clue'
    });
    CrmFollowUpRecord.belongsTo(app.model.CustomerManage.Pool, {
      foreignKey: 'relation_id',
      targetKey: 'id',
      as: 'client'
    });
  };
  return CrmFollowUpRecord;
};
