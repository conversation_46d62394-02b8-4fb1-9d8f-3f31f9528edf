
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, BOOLEAN, DATE, JSON } = app.Sequelize;

  const CrmTodoManager = app.model.define(
    'crm_todo_manager',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      belong: INTEGER(11), // 1. affiliate-revenue 2. affiliate-cost 3. programmatic-revenue 4. programmatic-cost 5. invoice-billing
      relation_json: JSON, // 关联JSON
      content: STRING(256), // null,
      type: INTEGER(1), // 1. Finance, 2. Contract, 3. Leads,
      due_time: INTEGER(11), // null,
      owner: STRING(256), // null,
      status: BOOLEAN(2), // 1. Pending  \r\n2. Completed \r\n3. Overdue,
      ctime: DATE, // null,
      utime: DATE, // null
    },
    {
      createdAt: false,
      updatedAt: false,
      ...config
    }
  );

  return CrmTodoManager;
};
