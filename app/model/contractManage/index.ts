
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DECIMAL, DATE, JSON } = app.Sequelize;

  const CrmContract = app.model.define(
    'crm_contract',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      apply_id: INTEGER(11), // 申请提交人,
      contract_code: STRING(128), // 合同编号,
      contract_type: STRING(52), // 合同类型,
      status: STRING(28), // 状态 draft: 草稿 asset_approval: 资产组审核中 financial_approval: 财务组审核中 tax_approval: 税务审核中 legal_approval: 法务审核中 oa_pending: 钉钉待审核 oa_pending_approval: 钉钉发起合同审批 oa_rejected: OA 共担拒绝 oa_approved: 钉钉审核通过 completed: 已完成 approval_rejected: CRM工单拒绝
      template: STRING(52), // 合同模板,
      bu: STRING(52), // 业务单元,
      pay_type: STRING(52), // 客户类型
      payment_terms: STRING(52), // 付款方式,
      is_exceed_amount: STRING(20), // 是否超额,
      amount: DECIMAL(11, 3), // 合同金额,
      currency: STRING(10), // 币种,
      our_company_name: STRING(52), // 我方公司名称,
      our_contact_person: STRING(52), // 我方联系人,
      our_information: STRING(52), // 我方联系方式,
      partner_company_name: STRING(52), // 合作方公司名称,
      partner_contact_person: STRING(52), // 合作方联系人,
      partner_information: STRING(52), // 合作方联系方式,
      bus_brand_name: STRING(52), // 业务品牌,
      effective_date: DATE, // 生效日期,
      expiry_date: DATE, // 到期日期,
      creator: STRING(52), // 创建人,
      modifier: STRING(52), // 修改者,
      contract_desc: STRING(512), // 简要说明,
      attachment: JSON, // 合同附件,
      remark: STRING(256), // 备注,
      instance_id: STRING(56), // 钉钉实例ID
      submission_time: DATE, // 申请提交时间
      complete_time: DATE, // 审批单完成时间
      dual_signed: JSON, // 双签合同数据
      asset_team_audit_required: INTEGER(1),
      approval_memo: JSON, // 记录审核人的ID
      transmittor_info: JSON, // 合同的转交信息
      shared_user_ids: STRING(128), // 共享合同人
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );

  CrmContract.associate = () => {
    CrmContract.hasMany(app.model.ContractManage.Chat, { foreignKey: 'contract_id', sourceKey: 'id' });
  };
  return CrmContract;
};
