
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, TEXT, STRING, DATE, JSON } = app.Sequelize;

  const CrmContractChatRecord = app.model.define(
    'crm_contract_chat_record',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      arraignment_id: INTEGER(11), // 引用的聊天记录ID,
      tag: STRING(20),
      contract_id: INTEGER(11), // 合同ID,
      message: TEXT, // 内容,
      type: STRING(28), // 类型，reply | apply_submit_to_asset | apply_submit_to_financial | asset_submit | finance_submit | legal_submit
      attachment: STRING(512), // 附件,
      attachment_new: JSON, // 新附件
      is_update_attachment: INTEGER(2), // 	0: 未更新 1: 更新了
      is_delete: INTEGER(2), // 0未删除，1已删除
      creator: STRING(52), // 创建人
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );
  CrmContractChatRecord.associate = () => {
    CrmContractChatRecord.hasOne(CrmContractChatRecord, { foreignKey: 'id', sourceKey: 'arraignment_id', as: 'relatedChatRecord' });
  };
  return CrmContractChatRecord;
};
