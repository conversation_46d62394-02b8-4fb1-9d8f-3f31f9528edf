
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, DATE } = app.Sequelize;

  const CrmClientContract = app.model.define(
    'crm_client_contract',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      contract_id: INTEGER(11), // 合同审批记录表,
      client_id: INTEGER(11), // 生成合同的客户id,
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );
  CrmClientContract.associate = () => {
    app.model.ContractManage.Index.belongsToMany(app.model.CustomerManage.Pool, { through: CrmClientContract, foreignKey: 'contract_id' });
    app.model.CustomerManage.Pool.belongsToMany(app.model.ContractManage.Index, { through: CrmClientContract, foreignKey: 'client_id' });
  };

  return CrmClientContract;
};
