
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DECIMAL, JSON } = app.Sequelize;

  const CommissionReport = app.model.define(
    'commission_report',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      month: STRING(12), // null,
      adv_group: STRING(128), // null,
      adv_type: STRING(24), // null,
      payment_period: INTEGER(6), // null,
      adv_bd: STRING(64), // null,
      adv_owner: STRING(128), // null,
      pub_group: STRING(128), // null,
      pub_type: STRING(12), // null,
      pub_bd: STRING(64), // null,
      pub_owner: STRING(128), // null,
      billing_revenue: DECIMAL(10, 2), // null,
      paid_revenue: DECIMAL(10, 2), // null,
      paid_month: STRING(128), // null,
      paid_date: STRING(128), // null,
      pub_cost: DECIMAL(10, 2), // null,
      gross_profit: DECIMAL(10, 2), // null,
      overdue_payment_date: STRING(128), // null,
      deduction: STRING(128), // null,
      commissionable_amount: DECIMAL(10, 2), // null,
      data_type: STRING(3), // 'aff' | 'adx'
      generate_info: JSON
    },
    {
      ...config,
      createdAt: false,
      updatedAt: false
    }
  );

  return CommissionReport;
};
