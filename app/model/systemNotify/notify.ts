
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, DATE, JSON } = app.Sequelize;

  const CrmNotify = app.model.define(
    'crm_notify',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      relation_type: STRING(52), // 消息相关方,
      relation_id: INTEGER(11), // 消息相关方id,
      receiver_id: INTEGER(11), // 接收者id
      notify_type: STRING(52), // 消息类型 node1: 录入线索后，通知BD leader node2: 商务负责人分配线索给BD 通知BD 本人, node3: BD之间转让线索，通知被转让的BD本人 node4: 线索太久未跟进，通知 线索负责的BD node5: 客户长期未合作，通知负责该客户的BD node6: 客户回退到线索池通知
      msg: STRING(512), // 消息内容,
      status: INTEGER(2), // 0: 未读，1: 已读,
      remark: STRING(256), // 备注,
      other_info: JSON,
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
      creator: STRING(52), // 创建人ID
    },
    {
      ...config
    }
  );

  CrmNotify.associate = () => {
    // 与User存在一对一关系，所以是hasOne()
    app.model.SystemNotify.Notify.hasOne(app.model.Authority.User, { foreignKey: 'id', sourceKey: 'receiver_id' });

  };

  return CrmNotify;
};
