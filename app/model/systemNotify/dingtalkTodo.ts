
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, BIGINT, STRING } = app.Sequelize;

  const CrmTodoListStash = app.model.define(
    'crm_todo_list_stash',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      task_time: STRING(14), // 创建待办的时间,
      task_id: STRING(40), // 钉钉返回的待办任务ID
      executor_id: STRING(50), // 执行人的钉钉ID
    },
    {
      ...config,
      createdAt: false,
      updatedAt: false
    }
  );

  return CrmTodoListStash;
};
