import { config } from '../../lib/baseModel';

export default app => {
  const { STRING, INTEGER, DATE } = app.Sequelize;

  return app.model.define(
    'crm_oms_authority',
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      type: STRING(15), // 类型
      parent: STRING(15), // 带前缀的唯一码 auth-code
      code: STRING(15), // 带前缀的唯一码 auth-code
      name: STRING(50), // 英文
      remark: STRING(500), // 备注
      remark_en: STRING(500), // 英文备注
      position: INTEGER, // 层级中的位置
      ctime: DATE, // 创建时间
      utime: DATE, // 更新时间
      creator: STRING(255),
      modifier: STRING(255),
      is_delete: {
        type: INTEGER(1),
        defaultValue: 0
      }
    },
    { ...config }
  );
};
