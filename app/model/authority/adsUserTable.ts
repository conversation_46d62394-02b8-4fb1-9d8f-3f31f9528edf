
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, TEXT, BOOLEAN } = app.Sequelize;

  const CmsAuth = app.model.define(
    'cms_auth',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      name: STRING(128), // 登录名,
      nickname: STRING(128), // 用户名,
      password: STRING(128), // 登录密码,
      lastlogin: INTEGER(11), // 上次登录时间,
      permission: STRING(4096), // 用户权限值,
      role: STRING(20), // 角色（root，master，manager, user, publisher, pub_group,dmd_group,special_user）,
      subuser: STRING(4096), // 下属 id 列表,
      lang: STRING(256), // 用户所属语言， 多个语言用逗号分隔；,
      email: STRING(128), // 邮箱,
      dd_email: STRING(50), // null,
      special_account: INTEGER(2), // 是否是dsp系统 Account创建的特殊sp账号\r\n1：是\r\n0：不是,
      special_sub: STRING(4096), // 特殊账号子id,
      custom_config: TEXT, // 用户自定义表格配置,
      status: BOOLEAN(1), // null,
      uid: STRING(50), // 钉钉的uid,
      dingUserId: STRING(32), // null,
      team: STRING(128), // null,
      mobile: STRING(24), // null,
      backup_user: STRING(512), // null,
      ctime: INTEGER(11), // null,
      utime: INTEGER(11), // null
    },
    {
      ...config
    }
  );

  return CmsAuth;
};
