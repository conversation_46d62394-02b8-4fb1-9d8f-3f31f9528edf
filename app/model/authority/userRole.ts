import { config } from '../../lib/baseModel';

export default app => {
  const { STRING, INTEGER, DATE } = app.Sequelize;

  const UserRole = app.model.define(
    'crm_oms_user_role',
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      user_code: STRING(50),
      role_code: STRING(15),
      ctime: DATE, // 创建时间
      utime: DATE, // 更新时间
      creator: STRING(255),
      modifier: STRING(255),
      is_delete: {
        type: INTEGER(1),
        defaultValue: 0
      },
    },
    { ...config }
  );

  UserRole.associate = () => {
    // 与UserRole存在一对一关系，所以是hasOne()
    app.model.Authority.UserRole.hasOne(app.model.Authority.Role, { foreignKey: 'code', sourceKey: 'role_code' });

    app.model.Authority.UserRole.hasMany(app.model.Authority.RoleAuth, { foreignKey: 'role_code', sourceKey: 'role_code' });
  };

  return UserRole;
};
