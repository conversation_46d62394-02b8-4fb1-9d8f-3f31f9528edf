import { config } from '../../lib/baseModel';

export default app => {
  const { STRING, INTEGER, DATE } = app.Sequelize;

  const RoleAuth = app.model.define(
    'crm_oms_role_auth',
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      role_code: STRING(15), // 带前缀的唯一码 role-code
      auth_code: STRING(15), // 带前缀的唯一码 role-code
      operate_auth: STRING(32),
      ctime: DATE, // 创建时间
      utime: DATE, // 更新时间
      creator: STRING(255),
      modifier: STRING(255),
      is_delete: {
        type: INTEGER(1),
        defaultValue: 0
      },
    },
    { ...config }
  );

  RoleAuth.associate = () => {
    // 与Authority存在一对一关系，所以是hasOne()
    app.model.Authority.RoleAuth.hasOne(app.model.Authority.Authority, { foreignKey: 'code', sourceKey: 'auth_code' });
  };

  return RoleAuth;
};
