import { config } from '../../lib/baseModel';

export default app => {
  const { STRING, INTEGER, DATE, TEXT } = app.Sequelize;

  const User = app.model.define(
    'affiliate_publisher',
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      pub_name: STRING(64), // 英文
      pub_marker: STRING(64), // 带前缀的唯一码 auth-code
      postback_url: STRING(256), // 带前缀的唯一码 auth-code
      api_key: STRING(256),
      redirect: INTEGER(2),
      status: INTEGER(2),
      ctime: INTEGER(11),
      utime: INTEGER(11),
      email: STRING(256),
      password: STRING(128),
      role_type: INTEGER(2), // 1表示内部账号，2表示外部账号
      method: STRING(64), // affiliate_link; direct_link
      pid: STRING(128), // 第三方平台上的pid
      pub_tag: STRING(255), // 用户tag
      custom_config: TEXT, // 自定义配置显示table
      affiliate_publisher_group: INTEGER(20), // affiliate_publisher_gorup#id
      inner_pub_name: STRING(128), // 内部产品渠道名，用于报表同步发现增长平台数据
      remark: STRING(128),
      owner: STRING(64), // 运营负责人id, affiliate_publisher#id （筛选内部账号）
      billing_cc_email: STRING(256), // 抄送邮箱
      mobile: STRING(24), // 联系电话
      dsp_account_id: STRING(11), // dsp账号id
      bd_member: STRING(256), // bd成员
      op_member: STRING(256),
      d_union_id: STRING(50),
      d_user_id: STRING(50),
      d_dept_id: INTEGER(11),
      crm_bd_member: STRING(256),
      crm_audit_type: STRING(40),
      ads_user: INTEGER(11)
    },
    { ...config }
  );

  User.associate = () => {
    // 与UserRole存在一对多关系，所以是hasMany()
    app.model.Authority.User.hasMany(app.model.Authority.UserRole, { foreignKey: 'user_code', sourceKey: 'id' });
    app.model.Authority.User.hasOne(app.model.Authority.AdsUserTable, { foreignKey: 'id', sourceKey: 'ads_user' });
  };

  return User;
};
