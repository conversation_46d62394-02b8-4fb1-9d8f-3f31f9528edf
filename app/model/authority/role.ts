import { config } from '../../lib/baseModel';

export default app => {
  const { STRING, INTEGER, DATE } = app.Sequelize;

  const Role = app.model.define(
    'crm_oms_role',
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      parent: STRING(15), // 带前缀的唯一码 auth-code
      code: STRING(15), // 带前缀的唯一码 auth-code
      name: STRING(50), // 英文
      remark: STRING(500), // 备注
      ctime: DATE, // 创建时间
      utime: DATE, // 更新时间
      creator: STRING(255),
      modifier: STRING(255),
      is_delete: {
        type: INTEGER(1),
        defaultValue: 0
      },
    },
    { ...config }
  );

  Role.associate = () => {
    // 与RoleAuth存在一对多关系，所以是hasMany()
    app.model.Authority.Role.hasMany(app.model.Authority.RoleAuth, { foreignKey: 'role_code', sourceKey: 'code' });
  };

  return Role;
};
