export default app => {
  const { DataTypes } = app.Sequelize;
  return app.model.define(
    'crm_oms_operate_log',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '实体id'
      },
      action: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: '类型(create,update)'
      },
      record: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '数据json'
      },
      old_record: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '修改前的数据json'
      },
      table_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '表名'
      },
      uid: {
        type: DataTypes.STRING(50),
        comment: '用户id'
      },
      operator: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '操作人'
      },
      ctime: {
        type: DataTypes.DATE,
        comment: '操作时间',
        defaultValue: new Date()
      },
      url: {
        type: DataTypes.STRING(50),
        comment: '调用api'
      },
      pathname: {
        type: DataTypes.STRING(255),
        comment: '页面路径'
      },
      ip: {
        type: DataTypes.STRING(50),
        comment: '用户ip地址'
      }
    },
    { timestamps: false, freezeTableName: true }
  );
};
