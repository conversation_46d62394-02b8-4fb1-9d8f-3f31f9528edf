
import { config } from '../../lib/baseModel';

export default app => {
  const { INTEGER, STRING, JSON, DATE } = app.Sequelize;

  const CrmClue = app.model.define(
    'crm_clue',
    {
      'id': {
        type: INTEGER(11),
        'primaryKey': true,
        'autoIncrement': true
      },
      client_id: INTEGER(11), // 客户ID，关联客户表
      clue_identity: INTEGER(2), // 0: 无状态线索 1: 主线索 2: 副线索
      clue_from: STRING(52), // 线索来源,
      active_id: INTEGER(10), // 关联的活动id，可能不存在，需要看线索来源,
      connect_name: STRING(52), // 联系人名称,
      customer_name: STRING(150), // 客户名称
      company_name: STRING(128), // 公司名称,
      position: STRING(52), // 职务,
      contact_type: JSON, // 联系方式：{\微信\":\"xxx\",\"电话\":\"1xx\"}",
      industry: STRING(128), // 行业,
      clue_content: STRING(256), // 线索内容,
      clue_status: STRING(52), // 线索状态：待分配，已分配，无效线索，已转换，转换失败, 其他
      bd_id: STRING(52), // BD负责人,
      previously_bd: STRING(52),
      assist_id: STRING(52), // 协作人,
      conversion_time: DATE, // 线索转换客户的时间,
      assignment_time: DATE, // 线索分配时间,
      attachment: STRING(1024),

      invalid_clue: STRING(128), // 无效线索原因,
      convert_fail: STRING(128), // 转换失败原因,
      mismatch: STRING(128), // 不匹配原因
      other_reason: STRING(128), // 其他原因
      remark: STRING(128), // 备注,
      creator_id: INTEGER(11),
      creator: STRING(52), // 创建人
      modifier: STRING(52), // 修改者
      ctime: DATE, // 创建时间,
      utime: DATE, // 更新时间
    },
    {
      ...config
    }
  );
  CrmClue.associate = () => {
    // 与User存在一对一关系，所以是hasOne()
    app.model.ClueManage.Pool.hasOne(app.model.Authority.User, { foreignKey: 'id', sourceKey: 'bd_id' });

  };
  return CrmClue;
};
