
import Controller from '../base';
import sequelize from 'sequelize';
import moment from 'moment';
import { CUSTOMER_STATUS_MAP, GET_COOP_BUS_TO_SIMPLIFY, OPTION_SEPARATOR, userISDemoAccount } from '../../lib/constant';
import { Context } from 'egg';
import axios from 'axios';

enum UPLOAD_TITLE {
  'company_name' = 'Company',
  'content' = 'Content',
  'creator' = 'Creator'
}

export default class CrmFollowUpRecordController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.getParams(query);
    const result = await service.followUpRecord.index.find(params);
    const total = await service.followUpRecord.index.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  /** 导出Excel  */
  async export() {
    const { ctx } = this;
    const {
      query,
      columnObjs,
      sheetName,
    } = ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const params = await this.getParams({
      ...query
    });
    const result = await this.service.followUpRecord.index.find({
      ...params,
      raw: true
    });

    const attachmentNames = new Set();
    result.forEach(it => {

      if (it.utime) {
        it.utime = moment(it.utime).format('YYYY-MM-DD');
      }
      if (it.record_type === 'client') {
        it.client_status = CUSTOMER_STATUS_MAP[it['client.client_status']];
        it.client_type = GET_COOP_BUS_TO_SIMPLIFY(it['client.coop_bus'] || '');
      }

      if (it.attachment) {
        const attachments = it.attachment.split(',');
        attachments.forEach((url, index) => {
          attachmentNames.add(`Attachment${index + 1}`);
          it['Attachment' + (index + 1)] = url;
        });
      }
    });
    Reflect.deleteProperty(columnObjs, 'attachment');
    [...attachmentNames].forEach((it: string, index: number) => {
      columnObjs[it] = {
        dataIndex: it,
        sortedIndex: 17 + index,
        title: it,
        style: {
          width: 200
        }
      };
    });

    const fileStream = await this.service.excelHandler.export(sheetName, result, columnObjs);
    this.ctx.body = fileStream;

  }

  async getParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { relation_id, company_name, type, content, key_info, creator, client_status, client_type, record_type, startDate, endDate } = query;
    const where: any = {};
    const opAnd = [];

    if (relation_id) {
      where.relation_id = relation_id;
    }
    if (startDate && endDate) {
      where.utime = {
        [Op.between]: [moment(startDate).startOf('day'), moment(endDate).endOf('day')]
      };
    }
    if (company_name) {
      const companys = company_name.split(OPTION_SEPARATOR);
      const opOr = [];
      companys.forEach(it => {
        const [clueId, companyName] = it.split(':');
        opOr.push({
          clue_id: clueId,
          company_name: companyName
        });
      });
      opAnd.push({
        [Op.or]: opOr
      });
    }
    if (record_type) {
      where.record_type = record_type;
    }

    if (type) {
      where.type = type;
    }
    if (content) {
      where.content = {
        [Op.like]: `%${content}%`
      };
    }
    if (key_info) {
      where.key_info = key_info;
    }
    if (creator) {
      const creatorList = creator.split(OPTION_SEPARATOR);
      where.creator = {
        [Op.in]: creatorList
      };
    }
    if (client_status || client_status === 0) {
      where['$client.client_status$'] = client_status;
    }
    if (client_type) {
      const coopBus = client_type.split(OPTION_SEPARATOR);
      const opOr = [];
      coopBus.forEach((item, index) => {
        opOr.push({
          ['$client.coop_bus$']: {
            [Op.startsWith]: item
          }
        });
      });
      opAnd.push({
        [Op.or]: opOr
      });
    }
    if (userISDemoAccount(this.ctx.user)) {
      where.id = -1;
    }

    if (opAnd.length) {
      where[Op.and] = opAnd;
    }
    const order = [['ctime', 'DESC'], ['id', 'DESC']];
    // 主表筛选条件

    const params = {
      where,
      order,
      include: [
        {
          model: this.app.model.ClueManage.Pool,
          as: 'clue',
          attributes: ['id', 'company_name']
        }, {
          model: this.app.model.CustomerManage.Pool,
          as: 'client',
          attributes: ['id', 'company_name', 'client_status', 'coop_bus']
        }
      ],
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    return params;
  }

  /**
   * 导入Lead数据并进行校验
   */
  async importLeadDataAndVerify(ctx: Context) {
    const stream = await ctx.getFileStream();
    // 获取额外字段
    const { } = stream.fields;

    // 校验：如果以上七项表头，不正确。阻止操作，
    const verifyData = (result: Array<Record<string, any>>) => {
      const someVerify = result.some(item => {
        return !item.hasOwnProperty(UPLOAD_TITLE.content)
          || !item.hasOwnProperty(UPLOAD_TITLE.company_name)
          || !item.hasOwnProperty(UPLOAD_TITLE.creator);
      });
      return !someVerify;
    };
    const transformResult = (excelDatas: Array<Record<string, any>>) => {
      excelDatas.forEach(it => {
        Reflect.deleteProperty(it, 'Verify Note');
        for (const key in it) {
          if (!Object.prototype.hasOwnProperty.call(it, key)) {
            continue;
          }

        }
        return it;

      });

      return excelDatas;
    };

    try {
      // 获取原数据
      const excelDatas: any = await this.service.upload.getFileBuffers(stream);

      if (excelDatas.length > 5000) {
        return this.error({}, 'Can\'t import data over 5000 rows.');
      }

      const verifyResult = verifyData(excelDatas);
      if (verifyResult) {
        if (!verifyResult) {
          return this.error({}, 'The header name of form is not correct, please recheck with the export template and upload again.');
        }
        // 转换出需要的数据
        const result = transformResult(excelDatas);

        return this.success({
          result
        }, 'upload success');
      }

      // 采用导入格式2

      const result = await axios.post(`${this.app.config.all_host.ai_public_server}/api/ai/completionMessages`, {
        app_key: 'common_copilot',
        model: 'gpt-4o-mini',
        use_original_model: true,
        inputs: `
          This is a business weekly report. I need to parse the given JSON data into an array of objects.
          Example:
          For instance, one row in the data looks like this:
          Diamond Game: 两个印尼包目前成本达标，但是首充需要从3w5优化到5w才能加预算，目前维持$400/天（XY）
          线索哦: 新增PK联运产品测试，首充成本在10刀以内，每天的预算为250$。（MM）
          It should be parsed into the following JSON format:
          Company: Content (Creator)。
          In a certain data, you can use newlines, split into lines to Json, the Company generally will not repeat;
          [
            {
              Company: 'Diamond Game',
              Content: '两个印尼包目前成本达标，但是首充需要从3w5优化到5w才能加预算，目前维持$400/天',
              Creator: 'XY'
            },
            {
              Company: '线索哦',
              Content: '新增PK联运产品测试，首充成本在10刀以内，每天的预算为250$。',
              Creator: 'MM'
            }
          ];
            Parsing Rules:
             - In a certain data, you can use newlines, split into lines to Json, the Company generally will not repeat;
             - Identify valid cells that contain either ":" or "()" in the first column.
             - ":" Company before, Content after, Inside "()" is Creator
             - Each line within a cell represents a separate valid record.
             - Split the content using ":" or "()" into three parts: line by line;
             - Company Name
             - Content
             - Creator
             - Creator Mapping (Case-insensitive):
              > E → caolp
              > XY → chenxy
              > QY → yuqy
              > XJ → wuxj
              > MM → liangmm
              > TT → huangtt

              Input JSON Data:
              ${JSON.stringify(excelDatas)}

              Expected Output:
                Please return the parsed data only as a JSON string that can be directly processed with JSON.parse(). Do not include any additional text or Markdown formatting.
                Example expected output format:
                [{ "Company": "??", "Company": "??", "Creator": "??" }]
          `
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const answer = result.data.data.answer;
      console.log(answer, 'answer');
      try {
        const parsedData = JSON.parse(answer);
        parsedData.forEach(it => {
          const creatorStrategy = {
            'E': 'caolp',
            'XY': 'chenxy',
            'QY': 'yuqy',
            'XJ': 'wuxj',
            'MM': 'liangmm',
            'TT': 'huangtt'
          };
          if (creatorStrategy[it.Creator]) {
            it.Creator = creatorStrategy[it.Creator];
          }
        });
        this.success({
          result: parsedData
        }, 'upload success');
      } catch {
        this.error({}, 'The data is not in the correct format.');
      }

    } catch (err: any) {
      this.error({}, err.message);
    }
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.followUpRecord.index.findOne({ where: { id } });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body: list } = ctx.request;
    const results = [];

    for (const detail of list) {
      const res = await service.followUpRecord.index.create({
        ...detail,
        creator: detail.data_creator || ctx.user.pub_name,

        utime: moment().format('YYYY-MM-DD HH:mm:ss'),
      });
      results.push(res);
    }
    this.success(results, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    updateData.modifier = ctx.user.pub_name;
    const result = await service.followUpRecord.index.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.followUpRecord.index.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }
}
