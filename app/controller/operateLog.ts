import Controller from './base';
import * as sequelize from 'sequelize';
import { record } from '../../config/config.default';

export default class LogController extends Controller {
  async index() {
    const { ctx, service } = this;
    const pathname = ctx.headers.pathname;
    const { query } = ctx;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { operator, action, startTime, endTime, item_id } = query;
    const where: any = {
      pathname
    };
    if (operator) {
      where.operator = operator;
    }
    if (action) {
      where.action = action;
    }
    if (item_id) {
      if (item_id.includes(',')) {
        where.item_id = item_id.split(',');
      } else {
        where.item_id = item_id;
      }
    }
    if (startTime && endTime) {
      const { Op, literal } = sequelize; // 自定义 SQL， 修复 00:00:00 变成 16:00:00 问题
      where[Op.and]  = {
        $and: literal(`ctime >= '${startTime}' AND ctime <= '${endTime}'`)
      };
    }
    const params: any = {
      where,
      attributes: ['id', 'ctime', 'operator', 'action', 'record', 'old_record', 'ip', 'item_id'],
      order: [['id', 'DESC']]
    };
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    const result = await service.operateLog.find(params);
    const total = await service.operateLog.count(params);
    let list: any = [];
    if (result?.length > 0) {
      list = result.map((item: any) => {
        const { action } = item;
        if (action === 'destroy') {
          item.record = '{}';
        }
        return item;
      });
    }
    this.success(list, ctx.__('FetchSuccess'), { total });
  }
  async getFilters() {
    const { ctx, service } = this;
    const pathname = ctx.headers.pathname;
    const { operator, action } = ctx.query;
    const where: any = {
      pathname
    };
    if (operator) {
      where.operator = operator;
    }
    if (action) {
      where.action = action;
    }
    const operatorList = await service.operateLog.find({
      where,
      group: ['operator'],
      attributes: ['operator'],
      order: [['id', 'DESC']]
    });
    const actionList = await service.operateLog.find({
      where: {
        pathname
      },
      group: ['action'],
      attributes: ['action'],
      order: [['id', 'DESC']]
    });
    const result = {
      operatorList,
      actionList
    };
    this.success(result, ctx.__('FetchSuccess'));
  }
  /** 新增自定义日志 */
  async addCustomLog() {
    const { ctx, service } = this;
    const { body, query } = ctx.request;
    const result = {};
    // 调用 pbRequest Demo
    // const { id } = ctx.params;
    // const updateData = { ...body };
    // result = await service.proxy.pbRequest({
    //   xMethod: 'Config.EditConfig',
    //   data: updateData,
    //   action: 'update-config',
    //   itemId: id,
    //   pathname: '/operation/user-manage/user-normal', // 不写则使用当前页地址
    // });
    // 以下代码一般在 业务服务(service/xxx.ts)中调用
    record('create', {
      dataValues: body.newData,
      _previousDataValues: body.oldData,
      newUrl: body.url,
    }, {
      egg: this
    });
    this.success(result, ctx.__('CreateSuccess'));
  }
   /** 操作人列表 */
   async operatorList() {
    const { ctx, service } = this;
    const { query } = ctx.request;
    const { pathname } = query;
    const list: any = await service.operateLog.distinctField('operator', pathname);
    this.success(list, ctx.__('FetchSuccess'));
  }
  /** 操作类型列表 */
  async actionList() {
    const { ctx, service } = this;
    const { query } = ctx.request;
    const { pathname } = query;
    const list: any = await service.operateLog.distinctField('action', pathname);
    this.success(list, ctx.__('FetchSuccess'));
  }
}
