import { QueryTypes } from 'sequelize';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../lib/systemEvent';
import Controller from './base';

export default class CommonController extends Controller {
  async getActivity() {
    const { ctx } = this;

    const res = await ctx.service.activityManage.record.find({
      raw: true,
      order: [['id', 'DESC']],
    });

    const result = res.map(it => ({
      label: it.name,
      value: it.id
    }));
    this.success(result, ctx.__('FetchSuccess'));
  }

  async getCustomer() {
    const { ctx } = this;
    const res = await ctx.service.customerManage.pool.find({
      raw: true,
      order: [['id', 'DESC']]
    });
    const result = res.map(it => {
      const coopBus = it.coop_bus;
      let prefix = '';
      if (coopBus.includes('Affiliate,Advertiser Group')) {
        prefix = 'aff-adv:';
      } else if (coopBus === 'Affiliate,Publisher Group') {
        prefix = 'aff-pub:';
      } else if (coopBus === 'Adx,Demand Group' || coopBus === 'Commercialize,Interative Demand Group') {
        prefix = 'adx-demand:';
      } else if (coopBus.includes('Adx,Publisher Group') || coopBus === 'Commercialize,Interative Publisher Group') {
        prefix = 'adx-pub:';
      } else if (coopBus === 'Commercialize,DSP Agency') {
        prefix = 'dsp-agency:';
      } else if (coopBus === 'Quarkpay,Payment channel') {
        prefix = 'quark-pay-channel:';
      } else if (coopBus === 'Quarkpay,Payment customer') {
        prefix = 'quark-pay-customer:';
      } else if (coopBus.includes('Other')) {
        prefix = 'other:';
      }
      return {
        label: `${prefix}${it.client_name}`,
        value: it.id
      };
    });

    this.success(result, ctx.__('FetchSuccess'));

  }

  async getEnums() {
    const { ctx } = this;
    const res = await ctx.model.Enum.findAll({ raw: true, order: [['id', 'DESC']] });

    const result = res.reduce((obj, item) => {
      if (!obj[item.enum_type]) {
        obj[item.enum_type] = [];
      }
      obj[item.enum_type].push({
        label: item.enum_code,
        value: item.enum_code
      });
      return obj;
    }, {});
    this.success(result, ctx.__('FetchSuccess'));
  }

  async getCountrys() {
    const { ctx } = this;
    const countrySql = `
      SELECT * FROM \`dic_country\`
    `;
    const res = await this.app.model.query(countrySql, { type: QueryTypes.SELECT });

    const result = res.map((it: Record<string, any>) => ({
      label: `${it.name}(${it.iso_code})`,
      value: it.iso_code_3
    }));
    this.success(result, ctx.__('FetchSuccess'));
  }

  async createEnum() {
    const { ctx } = this;
    const { enum_type, enum_code } = ctx.request.body;
    if (!enum_type || !enum_code) {
      this.error({}, 'parameter error');
      return;
    }
    const isExist = await ctx.model.Enum.findOne({ where: { enum_type, enum_code } });
    if (isExist) {
      this.error({}, ctx.__('DataExists'));
      return;
    }
    const res = await ctx.model.Enum.create({ enum_type, enum_code });
    this.success(res, ctx.__('CreateSuccess'));
  }

  // 通知用户角色权限已授予
  async notifyUserRoleAuthIsApprove() {
    const { app, ctx } = this;
    try {
      const body = ctx.request.body;
      app.redis.get('pub').publish(app.config.sun.redisChannel, JSON.stringify({
        event: APP_MESSAGE_BUS_EVENT_MAP.AUTH_ROLE_PASS,
        data: body,
      }));
      this.success({}, '用户授权通知完成');
    } catch (e) {
      this.error({}, e.toString());
      app.coreLogger.error('[DingTalkController => notifyUserRoleAuthIsApprove]', e);
    }
  }

  // 通知对应用户可以审核合同了
  async notifyContractAudit() {
    const { app, ctx } = this;
    try {
      const body = ctx.request.body;
      app.redis.get('pub').publish(app.config.sun.redisChannel, JSON.stringify({
        event: APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT,
        data: body,
      }));
      this.success({}, 'notice success');
    } catch (e) {
      this.error({}, e.toString());
      app.coreLogger.error('[DingTalkController => notifyUserRoleAuthIsApprove]', e);
    }
  }

  async get() {
    const { ctx } = this;
    const { query } = ctx;
    const result = await this.service.proxy.curlGetProxy(query);
    if (result.isSuccess) {
      return this.success(result, this.ctx.__('FetchSuccess'));
    } else {
      return this.error(result, result.msg || '');
    }
  }
  async post() {
    const { ctx } = this;
    const { body } = ctx.request;
    const result = await this.service.proxy.curlPostProxy(body);
    return this.success(result.data, this.ctx.__('FetchSuccess'));
  }

}
