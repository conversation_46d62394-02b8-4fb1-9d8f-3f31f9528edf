import Controller from './base';

export default class DDCloudController extends Controller {

  async settingDDUserIds() {
    const { ctx, service } = this;

    const res = await service.ddCloud.settingDDUserIds();
    this.success(res, ctx.__('FetchSuccess'));

  }

  async settingUserPermission() {
    const { ctx, service } = this;

    const res = await service.ddCloud.settingUserPermission();

    this.success(res, ctx.__('FetchSuccess'));
  }

  async settingCorpSpaceRole() {
    const { ctx, service } = this;
    const res = await service.ddCloud.settingCorpSpaceRole();
    this.success(res, ctx.__('FetchSuccess'));
  }

  async completeTodo() {
    const { ctx } = this;
    const { dingtalk_ts } = ctx.request.body;
    const result = await this.service.systemNotify.dingtalkTodo.findOne({
      where: { task_time: dingtalk_ts }
    });
    if (!result) {
      return this.success({}, 'success');
    }
    const { task_id, id } = result;
    const isSuc = await this.service.ddCloud.completeTodo({
      task_id,
      unionid: ctx.user.d_union_id
    });
    if (isSuc) {
      // 删除todo记录
      await this.service.systemNotify.dingtalkTodo.destroy({ id });
    }

    this.success({}, 'success');
  }

}
