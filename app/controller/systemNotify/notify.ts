
import Controller from '../base';
import sequelize, { where } from 'sequelize';

const { Op } = sequelize;
export default class CrmNotifyController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.getParams(query);
    const result = await service.systemNotify.notify.find(params);
    const total = await service.systemNotify.notify.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async getParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { } = query;
    const where = {};
    const order = [['id', 'DESC']];
    // 主表筛选条件

    const params = {
      where,
      order,
      raw: true
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    return params;
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.systemNotify.notify.findOne({ where: { id } });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;
    delete body.id;
    const result = await service.systemNotify.notify.create(body);
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    updateData.modifier = ctx.user.pub_name;
    const result = await service.systemNotify.notify.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.systemNotify.notify.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async getUnreadCount() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.count({
      where: {
        receiver_id: userId,
        status: 0
      }
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }

  async getUnreadData() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.find({
      where: {
        receiver_id: userId,
        status: 0
      },
      order: [['ctime', 'DESC']],
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['id', 'pub_name']
        }
      ]
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }
  async getReadCount() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.count({
      where: {
        receiver_id: userId,
        status: 1
      }
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }

  async getReadData() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.find({
      where: {
        receiver_id: userId,
        status: 1
      },
      order: [['ctime', 'DESC']],
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['id', 'pub_name']
        }
      ]
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }

  async getAllData() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.find({
      where: {
        receiver_id: userId
      },
      order: [['ctime', 'DESC']],
      include: [
        {
          model: this.ctx.model.Authority.User,
          attributes: ['id', 'pub_name']
        }
      ]
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }
  async getAllCount() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.count({
      where: {
        receiver_id: userId
      }
    });
    this.success(result, this.ctx.__('FetchSuccess'));
  }

  async viewAll() {
    const userId = this.ctx.user.id;
    const result = await this.service.systemNotify.notify.update(
      {
        receiver_id: userId
      },
      {
        status: 1
      }

    );
    this.success(result, this.ctx.__('UpdateSuccess'));
  }
}
