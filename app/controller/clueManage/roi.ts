
import Controller from '../base';
import sequelize from 'sequelize';
import moment from 'moment';
import { userISDemoAccount } from '../../lib/constant';
const { Op } = sequelize;
export default class CrmClueROIController extends Controller {
  async index() {
    const { ctx } = this;
    const { query } = ctx;

    const { records, total } = await this.getDataList(query);
    this.success(records, ctx.__('FetchSuccess'), { total });
  }

  /** 导出Excel  */
  async export() {
    const { ctx } = this;
    const {
      query,
      columnObjs,
      sheetName,
    } = ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const { records } = await this.getDataList({
      ...query,
      pageIndex: 1,
      pageSize: 100000
    });
    records.forEach(it => {
      const json = JSON.parse(it.leads_no || '{}');
      const advGroup = (json.aff_adv || 0) + (json.adx_dem || 0);
      const pubGroup = (json.aff_pub || 0) + (json.adx_pub || 0);
      it.leads_no = `Adv_group: ${advGroup}  /  Pub_group: ${pubGroup}`;
      if (it.data === ` ~ `) {
        it.clue_roi = '-';
      }
    });
    const fileStream = await this.service.excelHandler.export(sheetName, records, columnObjs);
    this.ctx.body = fileStream;

  }

  async getDataList(query = {}) {
    if (userISDemoAccount(this.ctx.user)) {
      return {
        records: [],
        total: 0
      };
    }

    const result = await this.service.proxy.curlGetProxy({
      dataType: 'json',
      host: 'server',
      url: '/roi/get',
      data: {
        sign: 'n_e_m_o',
        ...query
      }
    });
    const { resp, total } = (result.data || {});

    return {
      records: resp || [],
      total: total || 0
    };

  }

}
