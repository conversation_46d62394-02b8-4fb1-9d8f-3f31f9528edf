
import Controller from '../base';
import sequelize from 'sequelize';
import moment from 'moment';
import { Context } from 'koa';
import { clientR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, COOP_BUS_PREFIX_TO_ORIGIN, CUSTOMER_STATUS_MAP, GET_COOP_BUS_STR, userIsADMIN, userIsBD, userIsBDAssistant, userISDemoAccount, userIsOP } from '../../lib/constant';
const { Op } = sequelize;

const statusOrder = {
  'unassigned': 1,
  'assigned': 2,
  'contacted': 3,
  'converted': 4,
  'conversion_failed': 5,
  'invalid': 6,
  'mismatch': 7,
  'other': 8
};

enum UPLOAD_TITLE {
  'connect_name' = 'Contact Name (Required)',
  'company_name' = 'Company Name (Required)',
  'position' = 'Job Title',
  'industry' = 'Industry',
  'content' = 'Lead Content',
}

export default class CrmClueController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.getParams(query);
    const result = await service.clueManage.pool.find(params);
    const total = await service.clueManage.pool.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async getParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { } = query;
    const where: any = {};
    const order = [
      [sequelize.literal(`CASE clue_status
        ${Object.entries(statusOrder).map(([status, order]) => `WHEN '${status}' THEN ${order}`).join(' ')}
        ELSE ${Object.keys(statusOrder).length + 1} END`), 'ASC'],
      ['id', 'DESC']
    ];
    const opAnd = [];
    const opOr = [];

    // 主表筛选条件
    if (query.id) {
      where.id = {
        [Op.substring]: query.id
      };
    }
    if (query.company_name) {
      where.company_name = {
        [Op.substring]: query.company_name
      };
    }
    if (query.clue_from) {
      const clueFroms = query.clue_from.split(',');
      where.clue_from = {
        [Op.in]: clueFroms
      };
    }
    if (query.clue_content) {
      where.clue_content = {
        [Op.substring]: query.clue_content
      };
    }
    if (query.connect_name) {
      where.connect_name = {
        [Op.substring]: query.connect_name
      };
    }
    if (query.contact_type) {
      where.contact_type = {
        [Op.substring]: query.contact_type
      };
    }
    if (query.position) {
      where.position = {
        [Op.substring]: query.position
      };
    }
    if (query.industry) {
      // find_in_set
      where.industry = {
        [Op.substring]: query.industry
      };
    }
    if (query.bd_id) {
      where.bd_id = query.bd_id;
    }
    if (query.previously_bd) {
      where.previously_bd = {
        [Op.substring]: query.previously_bd
      };
    }
    if (query.assist_id) {
      where.assist_id = query.assist_id;
    }
    if (query.PAGE_TYPE === 'owned') {
      if (userIsBDAssistant(this.ctx.user)) {
        where.assist_id = this.ctx.user.id;
      } else {
        where.bd_id = this.ctx.user.id;
      }
    }
    if (query.clue_status !== 'deleted') {
      // 正常情况，不包含删除状态的线索
      opAnd.push({
        clue_status: {
          [Op.ne]: 'deleted'
        }
      });
    }
    if (query.clue_status) {
      where.clue_status = query.clue_status;
    }

    if (query.customer_status) {
      where['$crm_client.client_status$'] = query.customer_status;
    }
    if (query.client_merge_field) {
      const coopBusOrigin = COOP_BUS_PREFIX_TO_ORIGIN(query.client_merge_field);
      const clientAssId = query.client_merge_field.replace(/\D+/g, '');
      if (!coopBusOrigin) {
        if (/^\d+$/.test(query.client_merge_field)) {
          // 是否包含字母和数字
          opOr.push({
            ['$crm_client.id$']: clientAssId
          });
          opOr.push({
            ['$crm_client.association_id$']: clientAssId
          });
        } else {
          opOr.push({
            ['$crm_client.id$']: clientAssId
          });
        }

        opOr.push({
          ['$crm_client.client_name$']: {
            [Op.substring]: query.client_merge_field
          }
        });
      } else {
        // 筛选的是关联客户
        opAnd.push({
          [Op.or]: coopBusOrigin.map(key => ({
            ['$crm_client.coop_bus$']: {
              [Op.substring]: key
            }
          }))
        });

        if (clientAssId) {
          opOr.push({
            ['$crm_client.id$']: clientAssId
          });
          opOr.push({
            ['$crm_client.association_id$']: clientAssId
          });
        }

      }
    }
    if (query.other_reason) {
      opOr.push({
        convert_fail: {
          [Op.substring]: query.other_reason
        }
      });
      opOr.push({
        mismatch: {
          [Op.substring]: query.other_reason
        }
      });
      opOr.push({
        invalid_clue: {
          [Op.substring]: query.other_reason
        }
      });
      opOr.push({
        other_reason: {
          [Op.substring]: query.other_reason
        }
      });
    }
    if (query.creator_id) {
      const creatorId = query.creator_id.split(',');
      where.creator_id = {
        [Op.in]: creatorId
      };
    }

    if (query.conversion_time) {
      const conversionTime = query.conversion_time.split(',');
      where.conversion_time = {
        [Op.between]: [moment(conversionTime[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'), moment(conversionTime[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')]
      };
    }
    if (query.ctime) {
      const ctime = query.ctime.split(',');
      where.ctime = {
        [Op.between]: [moment(ctime[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'), moment(ctime[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')]
      };
    }
    if (query.client_ctime) {
      const [startTime, endTime] = query.client_ctime.split(',');
      where['$crm_client.ctime$'] = {
        [Op.between]: [moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss'), moment(endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')]
      };
    }
    if (userISDemoAccount(this.ctx.user)) {
      // DEMO账号
      opAnd.push({
        company_name: 'DEMO_DSP'
      });
    }
    if (opAnd.length) {
      where[Op.and] = opAnd;
    }
    if (opOr.length) {
      where[Op.or] = opOr;
    }

    const params = {
      where,
      order,
      include: [
        {
          model: this.ctx.model.CustomerManage.Pool,
        }
      ]
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    return params;
  }

  /** 导出Excel  */
  async export() {
    const { ctx } = this;
    const {
      query,
      columnObjs,
      sheetName,
      PAGE_TYPE
    } = ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const params = await this.getParams({
      ...query,
      PAGE_TYPE
    });
    const result = await this.service.clueManage.pool.find({
      ...params,
      raw: true
    });
    const { bds: BD_USERS } = await this.service.authority.user.getByRoleUsers();

    const attachmentNames = new Set();
    result.forEach(it => {

      if (it.previously_bd) {
        const prevBds = it.previously_bd.split(',').filter(it => it);
        const bdNames = prevBds.map(it => {
          return BD_USERS.find(bd => bd.value === it)?.label || it;
        });
        it.previously_bd = bdNames.join(',');
      }

      if (it.attachment) {
        const attachments = it.attachment.split(',');
        attachments.forEach((url, index) => {
          attachmentNames.add(`Attachment${index + 1}`);
          it['Attachment' + (index + 1)] = url;
        });
      }

      if (!clientRelatedFieldHandler(it, this.ctx.user)) {
        it.connect_name = '';
        it.contact_type = '';
        it.position = '';
      }

      if (it['crm_client.id']) {
        it.customer_status = CUSTOMER_STATUS_MAP[it['crm_client.client_status']] || '-';
        it.client_ctime = it['crm_client.ctime'] ? moment(it['crm_client.ctime']).format('YYYY-MM-DD') : '-';
      }
      if (it.contact_type && Object.values(it.contact_type)?.length) {
        const entriesData = Object.entries(it.contact_type);
        it.contact_type = `Name: ${it.connect_name} (Title: ${it.position}))
 ${entriesData.map(([key, value]) => `${key}: ${value}`).join(`
 `)}`;
      }

      if (it['crm_client.client_name']) {
        it.client_merge_field = `Name: ${it['crm_client.client_name']}
Customer ID：${GET_COOP_BUS_STR({
          coop_bus: it['crm_client.coop_bus'],
          association_id: it['crm_client.association_id'],
          id: it['crm_client.id']
        }) || '-'}`;
      } else {
        it.client_merge_field = it.customer_name || '';
      }
      it.leads_time = `Creation Time: ${it.ctime ? moment(it.ctime).format('YYYY-MM-DD') : '-'}
Conversion Time: ${it.conversion_time ? moment(it.conversion_time).format('YYYY-MM-DD') : '-'}`;
    });
    [...attachmentNames].forEach((it: string, index: number) => {
      columnObjs[it] = {
        dataIndex: it,
        sortedIndex: 17 + index,
        title: it,
        style: {
          width: 200
        }
      };
    });

    const fileStream = await this.service.excelHandler.export(sheetName, result, columnObjs);
    this.ctx.body = fileStream;

  }

  async create({ isJumpValidate = false } = {}) {
    const { ctx } = this;
    const { body } = ctx.request;
    if (userIsBD(this.ctx.user)) {
      body.clue_status = 'contacted';
    }
    const { isSuccess, result, msg } = await this.service.clueManage.pool.createClue(body, isJumpValidate);
    if (!isSuccess) {
      return this.error({}, msg);
    }
    this.success(result, ctx.__('CreateSuccess'));
  }
  /**
   * 开放给外部调用的接口，逻辑同上面的create
   */
  async disparkCreate() {
    // 会跳过唯一校验
    return this.create({
      isJumpValidate: true
    });
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    if (!userIsADMIN(this.ctx.user)) {
      // 不是admin，只有该线索的bd，才能修改
      if (Number(updateData.bd_id) !== Number(this.ctx.user.id)) {
        return this.error({}, `A lead's information can only be modified by the assigned BD`);
      }
    }
    const isExist = await this.service.clueManage.pool.clueIsExist(updateData, id);
    if (isExist) {
      return this.error({}, 'the clue already exists');
    }
    updateData.modifier = ctx.user.pub_name;
    const result = await service.clueManage.pool.update(id, updateData);

    const clueInfo = await this.service.clueManage.pool.findOne({ where: { id, clue_identity: 1 } });
    if (clueInfo) {
      const { client_id } = clueInfo;
      await this.service.customerManage.pool.update({ id: client_id }, { company_name: updateData.company_name });
    }

    // 如果该线索存在客户
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.clueManage.pool.update(id, { clue_status: 'deleted' });
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async batchDestory() {
    const { ctx, service } = this;
    const { ids } = ctx.request.body;
    await service.clueManage.pool.update({ id: { [Op.in]: ids } }, { clue_status: 'deleted' });
    this.success({}, ctx.__('DeleteSuccess'));
  }
  /**
   * 批量分配线索
   */
  async batchLeadAssignment() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { ids, type, ...updateData } = body;
    // 由于转换失败状态需要特殊处理，不能一次update
    for (const id of ids) {
      await this.leadAssignment({
        ...updateData,
        id,
        type
      });
    }
    this.success({}, ctx.__('UpdateSuccess'));
  }
  /**
   * 单个分配线索
   */
  async leadAssignment(body) {
    const { id, type, ...updateData } = body;
    const status = 'assigned';
    const curDetail = await this.service.clueManage.pool.findOne({
      where: {
        id
      },
      attributes: ['bd_id', 'previously_bd', 'clue_status']
    });
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    const previouslyBd = curDetail.previously_bd ? curDetail.previously_bd.split(',') : [];
    if (curDetail.clue_status === 'conversion_failed' && curDetail.bd_id && Number(updateData.bd_id) !== Number(curDetail.bd_id)) {
      // 记录之前的bd
      previouslyBd.push(curDetail.bd_id);
    }

    const result = await this.service.clueManage.pool.update({
      id
    }, {
      ...updateData,
      clue_status: status,
      previously_bd: previouslyBd.join(','),
      assignment_time: now
    });

    if (type === 'allot') {
      // 节点2:商务负责人分配线索给BD 通知BD 本人
      this.service.systemNotify.notify.sendClueAssignNotify({
        relation_id: id,
        bd_id: updateData.bd_id,
        note: updateData.note
      });
    } else if (type === 'transfer') {
      // 节点3： BD之间转让线索，通知被转让的BD本人
      this.service.systemNotify.notify.sendClueTransferNotify({
        relation_id: id,
        bd_id: updateData.bd_id,
        note: updateData.note
      });
    }
    return result;
  }

  /**
   * 线索联系客户
   */
  async associatedCustomer() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_id, client_id } = body;
    const status = 'converted';
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    const result = await service.clueManage.pool.update({
      id: clue_id
    }, {
      clue_status: status, // 修改状态为转换成功
      client_id,
      clue_identity: 2,
      modifier: ctx.user.pub_name,
      conversion_time: now
    });

    this.success(result, ctx.__('UpdateSuccess'));
  }
  /**
   * 线索转换客户
   */
  async conversionCustomer() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_id, values } = body;
    values.creator = ctx.user.pub_name;

    // 先创建客户
    const { isSuccess, result, msg } = await service.customerManage.pool.clientCreate(values);
    if (!isSuccess) {
      return this.error({}, msg);
    }
    // 创建成功之后修改线索的信息
    const status = 'converted';
    const now = moment().format('YYYY-MM-DD HH:mm:ss');

    const result2 = await service.clueManage.pool.update({
      id: clue_id
    }, {
      clue_status: status, // 修改状态为转换成功
      client_id: result.id,
      clue_identity: 1, // 主线索
      modifier: ctx.user.pub_name,
      conversion_time: now,
      customer_name: values.client_name
    });
    this.success(result2, ctx.__('UpdateSuccess'));
  }
  /**
   * 修改线索失败状态
   */
  async clueFailureHandler() {
    // 修改线索状态
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_ids, type, reason } = body;
    let updateKey = '';
    if (type === 'conversion_failed') {
      updateKey = 'convert_fail';
    } else if (type === 'invalid') {
      updateKey = 'invalid_clue';
    } else if (type === 'mismatch') {
      updateKey = 'mismatch';
    } else if (type === 'other') {
      updateKey = 'other_reason';
    }

    const updateData = {
      clue_status: type,
      [updateKey]: reason
    };
    if (updateKey === 'convert_fail') {
      // 清除BD
      // updateData.bd_id = '';
      // updateData.assist_id = '';

    }
    const result = await service.clueManage.pool.update({
      id: {
        [Op.in]: clue_ids
      }
    }, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }
  async clueStatusToContacted() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_id } = body;
    const result = await service.clueManage.pool.update({
      id: clue_id
    }, {
      clue_status: 'contacted'
    });
    this.success(result, ctx.__('UpdateSuccess'));
  }
  /**
   * 修改至未分配状态
   */
  async changeToUnassigned() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_id } = body;
    const result = await service.clueManage.pool.update({
      id: clue_id
    }, {
      clue_status: 'unassigned',
      bd_id: '',
      assist_id: '',
    });
    this.success(result, ctx.__('UpdateSuccess'));
  }

  /**
   * 导入Lead数据并进行校验
   */
  async importLeadDataAndVerify(ctx: Context) {
    const stream = await ctx.getFileStream();
    // 获取额外字段
    const { active_id, clue_from } = stream.fields;

    // 校验：如果以上七项表头，不正确。阻止操作，
    const verifyData = (result: Array<Record<string, any>>) => {
      const someVerify = result.some(item => {
        return !item.hasOwnProperty(UPLOAD_TITLE.connect_name)
          || !item.hasOwnProperty(UPLOAD_TITLE.company_name);
      });
      return !someVerify;
    };
    const headersSet = new Set();
    const transformResult = (excelDatas: Array<Record<string, any>>) => {
      excelDatas.forEach(it => {
        Reflect.deleteProperty(it, 'Verify Note');
        const contactType = {};
        for (const key in it) {
          if (!Object.prototype.hasOwnProperty.call(it, key)) {
            continue;
          }
          if (key === UPLOAD_TITLE.industry) {
            if (it[key]) {
              it[key] = it[key].replace(/，/g, ',');
              if (it[key].split) {
                it[key] = it[key].split(',').map(it => it.trim()).join(',');
              }
            }
          }
          headersSet.add(key);

          if (!it[UPLOAD_TITLE.company_name]) {
            it['Verify Note'] = 'Company Name is empty';
          } else if (!it[UPLOAD_TITLE.connect_name]) {
            it['Verify Note'] = 'Contact Name is empty';
          }

          if (key.includes('(Contact)') && it[key]) {
            const newKey = key.replace('(Contact)', '').trim();
            contactType[newKey] = it[key];
          }
          if (Object.values(contactType).length) {
            it.contact_type = contactType;
          }
        }
        if (!it.contact_type) {
          it['Verify Note'] = 'Contact Infomation is empty';
        }
        return it;

      });

      return excelDatas;
    };

    try {
      // 获取原数据
      const excelDatas: any = await this.service.upload.getFileBuffers(stream);

      if (excelDatas.length > 5000) {
        return this.error({}, 'Can\'t import data over 5000 rows.');
      }

      const verifyResult = verifyData(excelDatas);
      if (!verifyResult) {
        return this.error({}, 'The header name of form is not correct, please recheck with the export template and upload again.');
      }
      // 转换出需要的数据
      const result = transformResult(excelDatas);
      // 开始入库
      const satisfyData = result.filter(it => !it['Verify Note']); // 过滤出成功的数据
      const dissatisfyData = result.filter(it => it['Verify Note']); // 过滤出不成功的数据
      const totalNum = excelDatas.length;
      let sucNum = 0;
      if (satisfyData?.length) {
        for (const detailData of satisfyData) {
          const createData: Record<string, any> = {
            company_name: detailData[UPLOAD_TITLE.company_name],
            connect_name: detailData[UPLOAD_TITLE.connect_name],
            position: detailData[UPLOAD_TITLE.position],
            industry: detailData[UPLOAD_TITLE.industry],
            clue_content: detailData[UPLOAD_TITLE.content],
            contact_type: detailData.contact_type,
            active_id,
            clue_from
          };
          if (userIsBD(this.ctx.user)) {
            createData.bd_id = this.ctx.user.id;
            createData.clue_status = 'contacted';
          }
          const { isSuccess } = await this.service.clueManage.pool.createClue(createData);
          if (!isSuccess) {
            Reflect.deleteProperty(detailData, 'bd_id');
            Reflect.deleteProperty(detailData, 'status');
            dissatisfyData.push({
              ...detailData,
              ['Verify Note']: 'The clue already exists'
            });
          } else {
            sucNum++;
          }
        }

      }

      this.success({
        total: totalNum,
        success: sucNum,
        dissatisfyData,
        headers: [...headersSet, 'Verify Note']
      }, 'upload success');

    } catch (err: any) {
      this.error({}, err.message);
    }
  }

  async runSchedule() {
    await this.ctx.app.runSchedule('working-day-notice');
    this.success({}, 'success');
  }

  async runNotifySchedule() {
    await this.ctx.app.runSchedule('message-notification-center');
    // await this.ctx.app.runSchedule('notification-of-unprocessed');
    this.success({}, 'success');
  }

  async getClueCompany() {
    const { ctx } = this;
    const result = await this.service.clueManage.pool.getClueCompany();
    this.success(result, ctx.__('FetchSuccess'));
  }
}
