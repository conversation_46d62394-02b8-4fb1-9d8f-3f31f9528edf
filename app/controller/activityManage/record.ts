
import Controller from '../base';
import sequelize from 'sequelize';
import moment from 'moment';
import { userISDemoAccount } from '../../lib/constant';
const { Op } = sequelize;
export default class CrmActiveController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.setParams(query);
    const result = await service.activityManage.record.find(params);
    const total = await service.activityManage.record.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async setParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { start_time, end_time, name, content } = query;
    const where: any = {};
    const order = [['id', 'DESC']];
    // 主表筛选条件
    if (start_time && end_time) {
      where[Op.or] = [
        {
          start_time: {
            [Op.between]: [start_time, end_time]
          }
        },
        {
          end_time: {
            [Op.between]: [start_time, end_time]
          }
        },
        {
          [Op.and]: [
            {
              start_time: {
                [Op.lte]: start_time
              }
            },
            {
              end_time: {
                [Op.gte]: end_time
              }
            }
          ]
        }
      ];
    }
    if (name) {
      where[Op.or] = [
        {
          name: {
            [Op.substring]: name
          }
        },
        {
          id: name
        }
      ];
    }
    if (content) {
      where.content = {
        [Op.substring]: content
      };
    }
    if (userISDemoAccount(this.ctx.user)) {
      where.id = 11;
    }
    const params = {
      where,
      order,
      raw: true
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    return params;
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.activityManage.record.findOne({ where: { id } });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;
    delete body.id;

    const isExist = await service.activityManage.record.findOne({ where: { name: body.name } });
    if (isExist) {
      this.error(isExist.dataValues, ctx.__('DataExists'));
      return;
    }

    const result = await service.activityManage.record.create(body);
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    const isExist = await service.activityManage.record.findOne({
      where: {
        name: updateData.name, id: {
          [Op.ne]: id
        }
      }
    });

    if (isExist) {
      this.error(isExist.dataValues, ctx.__('DataExists'));
      return;
    }
    updateData.modifier = ctx.user.pub_name;
    const result = await service.activityManage.record.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.activityManage.record.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }

  /** 导出Excel  */
  async export() {
    const { ctx } = this;
    const {
      query,
      columnObjs,
      sheetName,
    } = ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const params = await this.setParams(query);
    const result = await this.service.activityManage.record.find(params);
    result.forEach(it => {
      it.time_range = `${it.start_time} ~ ${it.end_time}`;
    });
    const fileStream = await this.service.excelHandler.export(sheetName, result, columnObjs);
    this.ctx.body = fileStream;

  }

}
