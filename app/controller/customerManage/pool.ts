
import moment from 'moment';
import Controller from '../base';
import sequelize, { QueryTypes } from 'sequelize';
import { clientRelated<PERSON><PERSON><PERSON><PERSON><PERSON>, COOP_BUS_PREFIX_TO_ORIGIN, GET_COOP_BUS_STR, OPTION_SEPARATOR, userIsBDAssistant, userISDemoAccount, userIsMarketer } from '../../lib/constant';

const { Op } = sequelize;

const statusOrder = {
  0: 1, // 未合作
  1: 2, // 已合作
};
export default class CrmClueController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.setParams(query);
    const result = await service.customerManage.pool.find(params);
    const total = await service.customerManage.pool.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async setParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { } = query;
    const where: any = {};
    const order = [
      [sequelize.literal(`CASE client_status
        ${Object.entries(statusOrder).map(([status, order]) => `WHEN '${status}' THEN ${order}`).join(' ')}
        ELSE ${Object.keys(statusOrder).length + 1} END`), 'ASC'],
      ['id', 'DESC']
    ];
    const opOr = [];
    const opAnd = [];
    const clueWhere: Record<string, any> = {
      clue_identity: 1
    };
    const userIsMKT = userIsMarketer(this.ctx.user);
    if (userIsMKT) {
      // 市场用户默认查不出数据
      where.id = -1;
    }
    // 主表筛选条件
    if (query.PAGE_TYPE === 'owned') {
      if (userIsBDAssistant(this.ctx.user)) {
        clueWhere.assist_id = this.ctx.user.id;
      } else {
        clueWhere.bd_id = this.ctx.user.id;

      }
    }

    if (query.id) {
      // 此ID不是数据ID 1. 如果是广告系统的客户，查广告系统的客户ID，否则才是数据ID
      const coopBusOrigin = COOP_BUS_PREFIX_TO_ORIGIN(query.id);
      const clientAssId = query.id.replace(/.*?(\d+)$/, '$1');
      if (!coopBusOrigin) {
        // 不是广告系统的客户
        opOr.push({
          ['id']: clientAssId
        });
        opOr.push({
          ['association_id']: clientAssId
        });
      } else {
        // 筛选的是关联客户
        opAnd.push({
          [Op.or]: coopBusOrigin.map(item => ({
            ['coop_bus']: {
              [Op.substring]: item
            }
          }))
        });
        if (clientAssId) {
          opOr.push({
            ['id']: clientAssId
          });
          opOr.push({
            ['association_id']: clientAssId
          });
        }

      }
    }
    if (query.client_name) {
      where.client_name = {
        [Op.substring]: query.client_name
      };
      if (userIsMKT) {
        // 市场角色 数据 只能通过模糊查询显示
        Reflect.deleteProperty(where, 'id');
      }
    }
    if (query.client_status) {
      where.client_status = query.client_status;
    }
    if (query.company_name) {
      where.company_name = {
        [Op.substring]: query.company_name
      };
      if (userIsMKT) {
        // 市场角色 数据 只能通过模糊查询显示
        Reflect.deleteProperty(where, 'id');
      }
    }
    if (query.bus_line) {
      where.bus_line = query.bus_line;
    }
    if (query.coop_bus) {
      const coopBus = query.coop_bus.split(OPTION_SEPARATOR);
      coopBus.forEach((item, index) => {
        opOr.push({
          coop_bus: {
            [Op.startsWith]: item
          }
        });
      });
    }
    if (query.comp_address) {
      where.comp_address = {
        [Op.substring]: query.comp_address
      };
    }
    if (query.pay_type) {
      where.pay_type = query.pay_type;
    }

    if (query.bd_id) {
      clueWhere.bd_id = query.bd_id;
    }
    if (query.assist_id) {
      clueWhere.assist_id = query.assist_id;
    }
    if (query.connect_name) {
      clueWhere.connect_name = {
        [Op.substring]: query.connect_name
      };
    }
    if (query.ctime) {
      const ctimeRange = query.ctime.split(OPTION_SEPARATOR);
      where.ctime = {
        [Op.between]: ctimeRange
      };
    }

    if (userISDemoAccount(this.ctx.user)) {
      opAnd.push({ client_name: 'DEMO_DSP' });
    }

    if (opOr.length) {
      where[Op.or] = opOr;
    }

    if (opAnd.length) {
      where[Op.and] = opAnd;
    }

    const params = {
      where,
      order,
      include: [
        {
          model: this.ctx.model.ClueManage.Pool,
          attributes: ['id',
            'bd_id', 'assist_id', 'position', 'connect_name',
            'contact_type', 'clue_identity', 'conversion_time',
            'company_name', 'client_id'
          ],
          where: clueWhere
        }
      ]
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    return params;
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.customerManage.pool.findOne({
      where: { id },
      include: [
        {
          model: this.ctx.model.ClueManage.Pool,
          attributes: ['id',
            'bd_id', 'assist_id', 'position', 'connect_name',
            'contact_type', 'clue_identity', 'conversion_time',
            'company_name', 'attachment', 'client_id', 'clue_from'
          ],
        }
      ],
      order: [[this.ctx.model.ClueManage.Pool, 'clue_identity', 'ASC']],
    });
    this.success(result, ctx.__('FetchSuccess'));
  }

  /** 导出Excel  */
  async export() {
    const { ctx } = this;
    const {
      query,
      columnObjs,
      sheetName,
      PAGE_TYPE
    } = ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const params = await this.setParams({
      ...query,
      PAGE_TYPE
    });
    let result = await this.service.customerManage.pool.find({
      ...params,
    });
    result = result.map(it => it.dataValues);
    result.forEach(it => {
      const item = it;
      item.id = GET_COOP_BUS_STR(item);
      item.coop_bus = item.coop_bus.split(',').join(' - ');

      const mainInfo = item.crm_clues.find(item => item.clue_identity === 1);
      if (mainInfo) {
        item.bd_id = mainInfo.bd_id;
        item.assist_id = mainInfo.assist_id;
        item.conversion_time = moment(mainInfo.conversion_time);
        item.connect_name = mainInfo.connect_name;
        item.position = mainInfo.position;

        if (!clientRelatedFieldHandler(mainInfo, this.ctx.user)) {
          it.connect_name = '';
          it.contact_type = '';
          it.position = '';
        }

        if (mainInfo.contact_type && Object.values(mainInfo.contact_type)?.length) {
          const entriesData = Object.entries(mainInfo.contact_type);
          it.contact_type = `Name: ${mainInfo.connect_name} (Title: ${mainInfo.position}))
   ${entriesData.map(([key, value]) => `${key}: ${value}`).join(`
   `)}`;
        }
      }
    });
    const fileStream = await this.service.excelHandler.export(sheetName, result, columnObjs);
    this.ctx.body = fileStream;

  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;
    delete body.id;
    const result = await service.customerManage.pool.create(body);
    if (!result.isSuccess) {
      return this.error(result, result.msg);
    }
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    // 公司名称 +  业务类型 唯一
    if (updateData.client_name && updateData.coop_bus) {
      const isExist = await this.service.customerManage.pool.findOne({
        where: {
          client_name: updateData.client_name,
          coop_bus: updateData.coop_bus,
          id: {
            [Op.ne]: id
          }
        },
        raw: true
      });
      if (isExist) {
        return this.error(isExist, 'The customer already exists');
      }
    }

    updateData.modifier = ctx.user.pub_name;
    const result = await service.customerManage.pool.update(id, updateData);
    if (!result.isSuccess) {
      return this.error(result, result.msg);
    }
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.customerManage.pool.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async clientUpdateMasterCueBD() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { master_cue_ids, bd_id, assist_id, remark } = body;
    const result = await this.service.clueManage.pool.update({
      id: {
        [Op.in]: master_cue_ids
      }
    }, {
      previously_bd: sequelize.literal(`
        CASE
          WHEN previously_bd IS NULL THEN bd_id
          WHEN previously_bd = '' THEN bd_id
          ELSE CONCAT(previously_bd, ",", bd_id)
        END
      `),
      bd_id,
      assist_id,
      remark,

    });

    // 遍历master_cue_ids,如果是广告系统关联客户，同步修改bd
    const bdUserInfo = await service.authority.user.findOne({
      where: {
        id: bd_id
      },
      raw: true,
      attributes: ['id', 'pub_name']
    });
    const clues = await service.clueManage.pool.find({
      where: {
        id: {
          [Op.in]: master_cue_ids
        },
        clue_identity: 1
      },
      attributes: ['id', 'client_id'],
      raw: true
    });

    const clinetIds = clues.map(it => it.client_id);
    const clients = await service.customerManage.pool.find({
      where: {
        id: {
          [Op.in]: clinetIds
        },
        association_id: {
          [Op.ne]: 0
        }
      },
      raw: true
    });

    clients.forEach((item) => {
      this.service.customerManage.pool.syncUpdateAdsClientBD(item.id, bdUserInfo.pub_name);
    });

    return this.success(result, ctx.__('UpdateSuccess'));
    // const result = await service.customerManage.pool.clientUpdateMasterCueBD(body);
  }
  async clientDeleteRelationship() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { clue_id } = body;
    // 解除线索的关联关系
    const res = await service.clueManage.pool.update({
      id: clue_id,
    }, {
      client_id: 0,
      clue_identity: 0,
      clue_status: 'assigned'
    });
    return this.success(res, ctx.__('UpdateSuccess'));
  }

  async createClueAndCustomer() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;

    if (body.lead_type === 'create_new_lead') {
      const isExist = await this.service.clueManage.pool.clueIsExist(body);
      if (isExist) {
        return this.error(isExist, 'The clue already exists');
      }
    }
    // 创建客户
    const { isSuccess, result, msg } = await service.customerManage.pool.clientCreate(body);
    if (!isSuccess) {
      return this.error(result, msg);
    }
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    if (body.lead_type === 'create_new_lead') {
      const clueData: Record<string, any> = {
        company_name: body.company_name,
        connect_name: body.connect_name,
        contact_type: body.contact_type,
        active_id: body.active_id,
        clue_from: body.clue_from,
        position: body.position,
        industry: body.industry,
        clue_content: body.clue_content,
        clue_status: 'converted',
        client_id: result.id,
        clue_identity: 1, // 主线索
        conversion_time: now,
        assignment_time: now,
        bd_id: body.bd_id,
        assist_id: body.assist_id,
        customer_name: body.client_name,
        attachment: body.attachment
      };

      const { isSuccess: isSuccessClue, result: resultClue, msg: msgClue } = await this.service.clueManage.pool.createClue(clueData, false);
      if (!isSuccessClue) {
        return this.error({}, msgClue);
      }
    } else {
      // 关联旧线索
      await this.service.clueManage.pool.update({
        id: body.lead_id
      }, {
        client_id: result.id,
        clue_identity: 1, // 关联线索
        conversion_time: now,
        clue_status: 'converted'
      });
    }

    this.success(result, ctx.__('CreateSuccess'));

  }

  async getCustomers() {
    const { ctx, service } = this;
    const { query } = ctx;
    if (!query.customer_ids) {
      return this.success([], ctx.__('FetchSuccess'));
    }
    const result = await service.customerManage.pool.find({
      where: {
        id: {
          [Op.in]: query.customer_ids.split(',')
        }
      }
    });
    this.success(result, ctx.__('FetchSuccess'));
  }
  async refreshClientCtime() {
    const { ctx, service } = this;
    const advGroupClientCrmData = await service.customerManage.pool.find({
      where: {
        coop_bus: {
          [Op.substring]: 'Affiliate,Advertiser Group'
        },
        association_id: {
          [Op.gt]: 0
        }
      },
      raw: true
    });
    const affAdvGroupAdsData = await this.app.model.query(`select * from affiliate_advertiser_group`, {
      type: QueryTypes.SELECT
    });

    for (const item of advGroupClientCrmData) {
      const findAdsGroupData = affAdvGroupAdsData.find(it => Number(it.id) === Number(item.association_id));
      if (findAdsGroupData && findAdsGroupData.ctime > 0) {
        await service.customerManage.pool.update({
          id: item.id
        }, {
          ctime: moment(findAdsGroupData.ctime * 1000).format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }

    // aff adv group 清洗完成

    // adx demand source 开始清洗
    const adxDemandSourceCrmData = await service.customerManage.pool.find({
      where: {
        coop_bus: {
          [Op.or]: [
            { [Op.substring]: 'Adx,Demand Group' },
            { [Op.substring]: 'Commercialize,Interative Demand Group' }
          ]
        },
        association_id: {
          [Op.gt]: 0
        }
      },
      raw: true
    });
    const adxDemandSourceAdsData = await this.app.model.query(`select * from demand_source`, {
      type: QueryTypes.SELECT
    });

    for (const item of adxDemandSourceCrmData) {
      const findAdsGroupData = adxDemandSourceAdsData.find(it => Number(it.id) === Number(item.association_id));
      if (findAdsGroupData && findAdsGroupData.ctime > 0) {
        await service.customerManage.pool.update({
          id: item.id
        }, {
          ctime: moment(findAdsGroupData.ctime * 1000).format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }
    // adx demand source 清洗完成

    // aff publisher group 开始清洗
    const affPublisherGroupCrmData = await service.customerManage.pool.find({
      where: {
        coop_bus: {
          [Op.substring]: 'Affiliate,Publisher Group'
        },
        association_id: {
          [Op.gt]: 0
        }
      },
      raw: true
    });

    const affPublisherGroupAdsData = await this.app.model.query(`select * from affiliate_publisher_group`, {
      type: QueryTypes.SELECT
    });

    for (const item of affPublisherGroupCrmData) {
      const findAdsGroupData = affPublisherGroupAdsData.find(it => Number(it.id) === Number(item.association_id));
      if (findAdsGroupData && findAdsGroupData.ctime > 0) {
        await service.customerManage.pool.update({
          id: item.id
        }, {
          ctime: moment(findAdsGroupData.ctime * 1000).format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }
    // aff publisher group 清洗完成

    // adx publisher group 开始清洗

    const adxPublisherGroupCrmData = await service.customerManage.pool.find({
      where: {
        coop_bus: {
          [Op.or]: [
            { [Op.substring]: 'Adx,Publisher Group' },
            { [Op.substring]: 'Commercialize,Interative Publisher Group' }
          ]
        },
        association_id: {
          [Op.gt]: 0
        }
      },
      raw: true
    });

    const adxPublisherGroupAdsData = await this.app.model.query(`select * from publisher_group`, {
      type: QueryTypes.SELECT
    });

    for (const item of adxPublisherGroupCrmData) {
      const findAdsGroupData = adxPublisherGroupAdsData.find(it => Number(it.id) === Number(item.association_id));
      if (findAdsGroupData && findAdsGroupData.ctime > 0) {
        await service.customerManage.pool.update({
          id: item.id
        }, {
          ctime: moment(findAdsGroupData.ctime * 1000).format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }
    // adx publisher group 清洗完成

    this.success({
      // affAdvGroupData,
      // advGroupClient
    }, ctx.__('UpdateSuccess'));
  }
}
