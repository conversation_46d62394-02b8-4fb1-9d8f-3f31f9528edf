import Controller from './base';
import { Context } from 'egg';
import { Bucket, SavePath, OSS_ROOT_PATH, OSS_PREFIX_URL } from '../lib/constant';
import fs from 'fs';
import { STS } from 'ali-oss';
import * as SYSTEM_CONFIG from '@flat/egg-system-config';

export default class UploadController extends Controller {
  async create(ctx: Context) {
    const { request } = ctx;
    const { service } = this;
    let domain = OSS_PREFIX_URL;
    if (request?.header?.domain) {
      domain = (request.header?.domain as string).trim();
    }
    const lastChar = domain.charAt(domain.length - 1);
    if (lastChar !== '/') {
      domain = domain + '/';
    }
    const { form, filename, filePath } = await service.upload.saveToLocalFile({
      savePath: SavePath
    });

    const filenameArr = filename.split('.');
    const suffix = filenameArr[filenameArr.length - 1];
    filenameArr.pop();
    const name = filenameArr.join('.');
    // 过滤其他字符
    let newfilename = name.replace(/[^a-z|^A-Z|^0-9|\-|_]/g, '');
    if (newfilename) {
      newfilename = newfilename + '-';
    }

    const savePath = `${OSS_ROOT_PATH}${newfilename}${Date.now()}.${suffix}`;
    const ossResult: any = await service.upload.saveToOssFile({ savePath, form });
    if (ossResult && ossResult.status === 200) {
      // 删掉本地文件
      fs.unlink(filePath, () => { });
      this.success(
        {
          path: `${domain}${savePath}`,
          filename: newfilename,
          status: ossResult.data.status
        },
        ctx.__('UploadSuccess')
      );
    } else {
      this.error({}, ctx.__('UploadFail'));
    }
  }

  async editorUploadImage(ctx: Context) {
    const { request } = ctx;
    const { service } = this;
    let domain = OSS_PREFIX_URL;
    if (request?.header?.domain) {
      domain = (request.header?.domain as string).trim();
    }
    const lastChar = domain.charAt(domain.length - 1);
    if (lastChar !== '/') {
      domain = domain + '/';
    }
    const { form, filename, filePath } = await service.upload.saveToLocalFile({
      savePath: SavePath
    });

    const filenameArr = filename.split('.');
    const suffix = filenameArr[filenameArr.length - 1];
    filenameArr.pop();
    const name = filenameArr.join('.');
    // 过滤其他字符
    let newfilename = name.replace(/[^a-z|^A-Z|^0-9|\-|_]/g, '');
    if (newfilename) {
      newfilename = newfilename + '-';
    }

    const savePath = `${OSS_ROOT_PATH}${newfilename}${Date.now()}.${suffix}`;
    const ossResult: any = await service.upload.saveToOssFile({ savePath, form });
    if (ossResult && ossResult.status === 200) {
      // 删掉本地文件
      fs.unlink(filePath, () => { });
      return this.ctx.body = {
        errno: 0,
        data: {
          url: `${domain}${savePath}`
        }
      };
    } else {
      this.error({}, ctx.__('UploadFail'));
    }
  }

  /** 上传大文件，获取临时 token */
  async getUploadKey() {
    const { ctx } = this;
    const sts = new STS({
      // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
      accessKeyId: SYSTEM_CONFIG.accessKeyId,
      accessKeySecret: SYSTEM_CONFIG.accessKeySecret
    });
    // roleArn填写角色ARN。
    // policy填写自定义权限策略。
    // expiration用于设置临时访问凭证有效时间单位为秒，最小值为900，最大值以当前角色设定的最大会话时间为准。
    // sessionName用于自定义角色会话名称，用来区分不同的令牌，例如填写为SessionTest。
    const sessionName = 'marmaxSession';
    const { res, credentials } = await sts.assumeRole('acs:ram::5022933608753771:role/ramoss', '', '3600', sessionName);
    if (res.status === 200) {
      this.success(credentials, ctx.__('FetchSuccess'));
    } else {
      this.error(
        {
          data: {}
        },
        ctx.__('FetchFail')
      );
    }
  }

}
