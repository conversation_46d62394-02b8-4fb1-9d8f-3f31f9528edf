
import moment from 'moment';
import { BOSSES_DD_USER_ID, DD_SPACE_ID } from '../../lib/globalVar';
import Controller from '../base';
import sequelize, { QueryTypes, where } from 'sequelize';
import { readFileSync } from 'fs';
import FormStream from 'formstream';
import { AUDIT_SUBMIT_TYPE, CONTRACT_DATA_TYPE, CONTRACT_TYPE_ENUM, OPTION_SEPARATOR, OSS_ROOT_PATH, userIsAssetGroup, userIsBD, userIsBDAssistant, userIsBDLeader, userIsFinance, userIsLegal, userIsTax } from '../../lib/constant';

const { Op } = sequelize;
export default class CrmContractController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.service.contractManage.index.getParams(query);
    const result = await service.contractManage.index.find(params);
    const total = await service.contractManage.index.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async getContractCount() {
    const { ctx, service } = this;
    const { query } = ctx;
    const params = await this.service.contractManage.index.getParams(query);
    const total = await service.contractManage.index.count(params);
    this.success({ total }, ctx.__('FetchSuccess'));
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.contractManage.index.findOne({ where: { id } });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const isExist = await service.contractManage.index.isExist({
      contract_code: body.contract_code
    });
    if (isExist) {
      this.error(isExist, ctx.__('ContractExists'));
      return;
    }
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;

    delete body.id;

    if (body.create_type !== void 0) {
      const approvalMemo = await this.service.contractManage.index.createTypeToApprovalMemo(body);
      body.approval_memo = approvalMemo;
      body.transmittor_info = {}; // 初始化为空数组
    }

    const result = await service.contractManage.index.create(body);
    result.addCrm_clients(body.customers);

    this.service.contractManage.index.syncClientInfo(body);

    if (body.create_type !== void 0) {
      this.service.contractManage.index.createTypeToAddComment(result.dataValues, body); // 创建到合同留言
    }

    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    const isExist = await service.contractManage.index.isExist({
      contract_code: updateData.contract_code,
      id
    });
    if (isExist) {
      this.error(isExist, ctx.__('ContractExists'));
      return;
    }

    updateData.modifier = ctx.user.pub_name;

    // 如果是创建合同，先上传文件到钉钉

    if (updateData.status === CONTRACT_TYPE_ENUM.OA_APPROVAL) {
      for (let i = 0; i < updateData.attachment.length; i++) {
        const { isSuccess, msg, dd_file_id, dd_file_type } = await this.service.contractManage.index.submitFileToDingtalk(updateData.attachment[i]);
        if (!isSuccess) {
          return this.error({}, msg);
        }
        updateData.attachment[i].dd_file_id = dd_file_id;
        updateData.attachment[i].dd_file_type = dd_file_type;
      }

      // 上传文件到钉钉成功，接下来发起钉钉的crm-合同审批

      const { isSuccess: initiateIsSuc, msg: initiateMsg, instanceId } = await this.service.ddCloud.initiateApproveClient({
        fileList: updateData.attachment.map(it => ({
          fileId: it.dd_file_id,
          fileName: it.name,
          filtType: it.dd_file_type,
          spaceId: DD_SPACE_ID,
          fileSize: it.size
        })),
        contractInfo: updateData
      });
      if (!initiateIsSuc) {
        return this.error({}, initiateMsg);
      }

      if (!instanceId) {
        return this.error({}, 'Failed to create approval instance');
      }
      updateData.instance_id = instanceId;

      // 自动同意审批任务
      this.service.ddCloud.autoConfirmProcess(id, updateData.instance_id);

    }

    if (updateData.create_type !== void 0) {
      const approvalMemo = await this.service.contractManage.index.createTypeToApprovalMemo(updateData, {
        type: 'update',
        id
      });
      updateData.approval_memo = approvalMemo;
      const transmittorInfo = await this.service.contractManage.index.handlerContractTransfer(updateData, { id });
      updateData.transmittor_info = transmittorInfo;
      // 检查是否有附件修改，有则发送通知
      this.service.contractManage.index.attachmentModifyCheck(updateData, { id });
    }

    const result = await service.contractManage.index.update(id, updateData);

    if (updateData.create_type !== void 0 && result[0]) {
      // 添加合同留言信息
      this.service.contractManage.index.createTypeToAddComment(result[1][0].dataValues, updateData);
    }

    this.service.contractManage.index.syncClientInfo(updateData); // 只有从草稿到审核，才会执行

    this.success(result, ctx.__('UpdateSuccess'));
  }

  async updateRelevanceClients() {
    const { id, customers } = this.ctx.request.body;
    const contractItem = await this.service.contractManage.index.findOne({
      where: {
        id
      }
    });
    const result = await contractItem.setCrm_clients(customers);

    this.success(result, this.ctx.__('UpdateSuccess'));

  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.contractManage.index.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async testApi() {
    const { ctx, service } = this;
    // const result = await service.ddCloud.getAuditDetail('ouqNAj1lQBybF0ugWmcVYQ09041719197517');
    const result = await this.service.ddCloud.getAuditDetail('FnicnNi0QXqoIYdzj1P2bQ09041728699143');
    this.success(result, 'success');
  }

  async contractTransfer() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const { new_approver, ids } = body;

    for (const id of ids) {
      // 前置校验
      const contractItem = await this.service.contractManage.index.findOne({
        where: { id },
        raw: true,
        attributes: ['id', 'status', 'transmittor_info']
      });
      const transmittorInfo = contractItem.transmittor_info || {};

      if (transmittorInfo[contractItem.status]) {
        // 如果已经是转交过的合同，B-A， 不能B-C
        if (Number(new_approver) !== transmittorInfo[contractItem.status].origin) {
          return this.error({}, `The contract(${id}) cannot be transferred twice, but only back to the original auditor`);
        }
      }
    }

    for (const id of ids) {
      const contractItem = await this.service.contractManage.index.findOne({
        where: { id },
        raw: true
      });
      const transmittorInfo = contractItem.transmittor_info || {};
      if (transmittorInfo[contractItem.status]) {
        // 转交回原来的审批人
        Reflect.deleteProperty(transmittorInfo, contractItem.status); // 清空转交记录
      } else {
        transmittorInfo[contractItem.status] = {
          // 第一次转交
          'origin': this.ctx.user.id,
          'status': false,
          'transmittor': Number(new_approver)
        };
      }

      await this.service.contractManage.index.update(id, {
        transmittor_info: transmittorInfo
      });
      const newApproverName = await this.service.authority.user.findOne({
        where: {
          id: new_approver
        },
        attributes: ['pub_name']
      });
      this.service.contractManage.chat.create({
        contract_id: id,
        tag: 'Transferred',
        type: 'transfer',
        message: `This approval is transferred from “${this.ctx.user.pub_name}” to “${newApproverName.pub_name}”`,
        creator: this.ctx.user.pub_name
      });
    }

    this.service.systemNotify.notify.commonNoticeToSystemAndDingtalk({
      notifyUserId: new_approver,
      title: `"${this.ctx.user.pub_name}" handed over a contract approval form to you`,
      jumpPath: `/operation/contract-manage/approval?id=${ids.join(',')}`
    });

    this.success('', 'success');
  }

  async updateSharedUser() {
    const { ctx, service } = this;
    const { shared_user_ids, contract_id } = ctx.request.body;
    await service.contractManage.index.update(contract_id, { shared_user_ids });
    this.success('', 'success');
  }
}
