
import { userIsAssetGroup, userIsFinance, userIsLegal, userIsTax } from '../../lib/constant';
import { APP_MESSAGE_BUS_EVENT_MAP } from '../../lib/systemEvent';
import Controller from '../base';
import sequelize from 'sequelize';

const { Op } = sequelize;

function extractNames(text: string) {
  const regex = /@([\p{L}\p{N}\w-]+)/gu;
  const matches = text.matchAll(regex);
  const names = [];

  for (const match of matches) {
    names.push(match[1]);
  }

  return names;
}

export default class CrmContractChatRecordController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const result = await service.contractManage.chat.find({
      where: {
        contract_id: query.contract_id,
        is_delete: 0
      },
      include: [
        {
          model: this.ctx.model.ContractManage.Chat,
          as: 'relatedChatRecord'
        }
      ],
      order: [['id', 'ASC']]
    });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;
    delete body.id;

    const message = body.message;
    const atNames = extractNames(message);
    if (atNames.length > 0) {
      const users = await this.service.authority.user.find({
        where: {
          pub_name: {
            [Op.or]: atNames
          }
        },
        include: [
          {
            model: ctx.model.Authority.UserRole,
            attributes: {
              exclude: ['user_code', 'ctime', 'utime']
            },

          }
        ],
      });
      for (const item of users) {
        const { crm_oms_user_roles, id } = item;
        const roleCodes = crm_oms_user_roles.map(item => item.dataValues.role_code);
        const userItem = { roleCode: roleCodes };
        let isAudit = false;
        if (userIsAssetGroup(userItem) || userIsFinance(userItem) || userIsLegal(userItem) || userIsTax(userItem)) {
          // 审核角色，跳转到审批页面，否则是个人合同页面
          isAudit = true;
        }
        const notifyContent = ` replied to you：${body.message} Contract id: ${body.contract_id}`;
        this.ctx.service.systemNotify.notify.create({
          relation_type: 'contract',
          relation_id: body.contract_id,
          receiver_id: id,
          notify_type: 'node8',
          other_info: {
            attachment: body.attachment,
            message: body.message,
            isAudit,
            title: notifyContent,
            initiator: { // 记录下创建人
              name: ctx.user.pub_name,
              id: ctx.user.id
            }
          },
          creator: this.ctx.user.pub_name
        });
        const ddUseProjectUrl = this.ctx.app.config.DD_USE_PROJECT_URL;
        this.service.ddCloud.sendNotificationToDo({
          executor_id: item.dataValues.d_union_id,
          title: `"${ctx.user.pub_name}" ${notifyContent}`,
          jump_url: `${ddUseProjectUrl}/operation/contract-manage/${isAudit ? 'whole' : 'owned'}?id=${body.contract_id}`
        });
        // 通知更新未读
        this.app.redis.get('pub').publish(this.app.config.sun.redisChannel, JSON.stringify({
          event: APP_MESSAGE_BUS_EVENT_MAP.CONTRACT_AUDIT,
          data: {
            targetUser: {
              id
            },
          },
        }));
      }

    }

    const result = await service.contractManage.chat.create(body);
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    updateData.modifier = ctx.user.pub_name;
    const result = await service.contractManage.chat.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.contractManage.chat.update(id, { is_delete: 1 });
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async getChatCount() {
    const { ctx, service } = this;
    const { contract_id } = ctx.query;
    const result = await service.contractManage.chat.count({ where: { contract_id, is_delete: 0 } });
    this.success(result, ctx.__('FetchSuccess'));
  }
}
