
import { userIsAdvBD, userIsAdvOP, userIsBDLeader, userIsOPLeader, userIsPubBD, userIsPubOP } from '../../lib/constant';
import Controller from '../base';
import sequelize, { QueryTypes } from 'sequelize';
import NP from 'number-precision';

const { Op } = sequelize;
export default class CommissionReportController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    // const params = await this.getParams(query);
    const { records, repSql, total } = await service.commissionReport.index.getCommissionList(query);
    this.toLocaleString({ records });
    this.success(records, ctx.__('FetchSuccess'), { total, repSql });
  }

  async export() {
    const {
      query,
      columnObjs,
      sheetName,
    } = this.ctx.request.body;
    Reflect.deleteProperty(query, 'pageIndex');
    Reflect.deleteProperty(query, 'pageSize');
    const { records } = await this.service.commissionReport.index.getCommissionList({
      ...query,
      pageIndex: 1,
      pageSize: 100000
    });

    this.toLocaleString({ records });
    records.forEach((item, index) => {
      if (index === 0) {
        return;
      }
      item.adv_group = `${item.adv_group}:${item.advertiser_group_name || ''}`;
      item.adv_owner = item.adv_owner_name;
      item.pub_group = `${item.pub_group}:${item.publisher_group_name || ''}`;
      item.pub_owner = item.pub_owner_name;
    });

    const fileStream = await this.service.excelHandler.export(sheetName, records, columnObjs);
    this.ctx.body = fileStream;
  }

  async getFilter() {
    const { ctx, service } = this;
    const { query } = ctx;
    const curUser = this.ctx.user;
    let advGroupWhere: string = `where 1 = 1 `;
    if (userIsBDLeader(curUser)) {
      const memberInfo = await this.service.role.bdMemberToInfo();

      advGroupWhere += ` and bd in (${memberInfo.map((item: any) => `'${item.pub_name}'`).join(',')})`;
    } else if (userIsAdvBD(curUser)) {
      // 如果是 bd ，只能看到bd是自己的广告主
      advGroupWhere += ` and bd = '${curUser.pub_name}'`;
    }

    const advertiserGroupList = await this.app.model.query(`
      select id, name from affiliate_advertiser_group ${advGroupWhere} order by id desc limit 10000 offset 0
    `, { type: QueryTypes.SELECT });

    const adxGroups = await this.app.model.query(`
      select id, name, bd from demand_source where 1 = 1 order by id desc
    `, { type: QueryTypes.SELECT });

    const affGroupsTransorm = advertiserGroupList.map((item: Record<string, any>) => ({
      id: item.id,
      name: item.name,
      type: 'aff',
    }));

    const adxGroupsTransform = adxGroups.map((item: Record<string, any>) => ({
      name: item.name,
      id: String(item.id),
      type: 'adx'
    }));

    let pubGroupWhere: string = 'where 1 = 1 ';

    if (userIsBDLeader(curUser)) {
      const memberInfo = await this.service.role.bdMemberToInfo();

      pubGroupWhere += ` and bd in (${memberInfo.map((item: any) => `'${item.pub_name}'`).join(',')})`;
    } else if (userIsPubBD(curUser)) {
      pubGroupWhere += ` and bd = '${curUser.pub_name}'`;
    }

    const publisherGroupList = await this.app.model.query(`
      select id, name from affiliate_publisher_group ${pubGroupWhere} order by id desc
    `, { type: QueryTypes.SELECT });

    const publisherGroupListTransform = publisherGroupList.map((item: Record<string, any>) => ({
      id: item.id,
      name: item.name,
      type: 'aff',
    }));

    const adxPubGroups = await this.app.model.query(`
      select id, name from publisher_group  order by id desc
    `, { type: QueryTypes.SELECT });
    const adxPubGroupsTransform = adxPubGroups.map((item: Record<string, any>) => ({
      id: item.id,
      name: item.name,
      type: 'adx',
    }));

    const advGroups = [...affGroupsTransorm];
    if (!userIsAdvOP(curUser) && !userIsPubOP(curUser) && !userIsOPLeader(curUser)) {
      advGroups.push(...adxGroupsTransform);
    }

    const pubGroups = [...publisherGroupListTransform, ...adxPubGroupsTransform];

    this.success({
      advertiserGroups: advGroups.map(it => ({ label: `${it.type}:${it.id}:${it.name}`, value: `${it.type}:${it.id}` })),
      publisherGroups: pubGroups.map(it => ({ label: `${it.type}:${it.id}:${it.name}`, value: `${it.type}:${it.id}` }))
    }, ctx.__('FetchSuccess'));
  }

  private toLocaleString(result: { records: Array<Record<string, any>> }) {
    const need$Localle = ['billing_revenue', 'paid_revenue', 'pub_cost', 'gross_profit', 'commissionable_amount'];
    const needPercent = ['gross_profit_margin', 'revenue_comfirm_rate', 'cost_comfirm_rate'];
    result.records.forEach(item => {

      if (item.deduction) {
        item.deduction = item.deduction.split(',').map(it => `${NP.times(it, 100)}%`).join(',');
      }

      need$Localle.forEach(key => {
        if (item[key] || item[key] === 0) {
          item[key] = `$${Number(item[key] || 0).toLocaleString('en-US', {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2
          })}`;
        }
      });
      needPercent.forEach(key => {
        if (item[key] || item[key] === 0) {
          item[key] = (Number(item[key] || 0) * 100).toFixed(2) + '%';
        }
      });
    });

  }

}
