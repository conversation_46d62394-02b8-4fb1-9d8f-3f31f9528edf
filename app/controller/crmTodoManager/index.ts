
import { OPTION_SEPARATOR } from '../../lib/constant';
import Controller from '../base';
import sequelize from 'sequelize';

const { Op } = sequelize;
export default class CrmTodoManagerController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    let pageSql = '';
    const params = await this.getParams(query);
    const result = await service.crmTodoManager.index.find({
      ...params,
      logging(sql) {
        pageSql = sql;
      }
    });
    const total = await service.crmTodoManager.index.count(params);
    this.success(result, ctx.__('FetchSuccess'), { total, sql: ctx.locals.env === 'prod' ? '' : pageSql });
  }

  async getParams(query) {
    const { Op } = sequelize;
    const pageIndex = (query.pageIndex && parseInt(query.pageIndex, 10)) || '';
    const pageSize = (query.pageSize && parseInt(query.pageSize, 10)) || '';
    const { content, type, belong, owner, status, global_user } = query;
    const where: any = {};
    const opAnd: any[] = [
      {
        status: {
          [Op.in]: [1, 3] // 不展示已完成的待办
        }
      }
    ];
    this.service.crmTodoManager.index.userRoleFilterHandler(opAnd);
    // overdue 状态置顶，再按duration 降序排
    const order = [
      [sequelize.literal('CASE WHEN status = 3 THEN 0 ELSE 1 END'), 'ASC'],
      ['ctime', 'ASC']
    ];
    // 主表筛选条件

    const params = {
      where,
      order,
      raw: true
    } as any;
    if (pageSize) {
      params.limit = pageSize;
    }
    if (pageIndex) {
      params.offset = pageSize ? (pageIndex - 1) * pageSize : 0;
    }
    if (content) {
      where.content = {
        [Op.substring]: content
      };
    }
    if (type) {
      const types = type.split(OPTION_SEPARATOR);
      where.type = {
        [Op.in]: types
      };
    }
    if (belong) {
      const belongs = belong.split(OPTION_SEPARATOR);
      where.belong = {
        [Op.in]: belongs
      };
    }
    if (owner) {
      const owners = owner.split(OPTION_SEPARATOR);
      const findInSetSql = owners.map(owner => `FIND_IN_SET(${owner}, owner)`).join(' or ');
      where.owner = sequelize.literal(`(${findInSetSql})`);
    }
    if (status) {
      const statuses = status.split(OPTION_SEPARATOR);
      where.status = {
        [Op.in]: statuses
      };
    }
    if (global_user) {
      opAnd.push({
        owner: sequelize.literal(`FIND_IN_SET(${global_user}, owner)`)
      });
    }
    if (opAnd.length > 0) {

      where[Op.and] = opAnd;
    }
    return params;
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const result = await service.crmTodoManager.index.findOne({ where: { id } });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    body.creator = ctx.user.pub_name;
    body.modifier = ctx.user.pub_name;
    delete body.id;
    const result = await service.crmTodoManager.index.create(body);
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    updateData.modifier = ctx.user.pub_name;
    const result = await service.crmTodoManager.index.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    await service.crmTodoManager.index.destroy(id);
    this.success({}, ctx.__('DeleteSuccess'));
  }

  async getOverallData() {
    const { ctx, service } = this;
    const result = await service.crmTodoManager.index.getOverallData();
    this.success(result, ctx.__('FetchSuccess'));
  }
}
