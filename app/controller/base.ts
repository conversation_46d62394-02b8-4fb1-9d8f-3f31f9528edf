'use strict';
import { Controller } from 'egg';
import { isPureObject } from '../lib/tool';
import Excel from 'xlsx';

export default class BaseController extends Controller {
  constructor(ctx) {
    super(ctx);
  }
  /**
   * 请求成功的回调
   * @param data
   */
  success(data, msg, options?) {
    msg = msg || this.ctx.__('OperateSuccess');
    if (isPureObject(msg)) {
      options = msg;
      msg = this.ctx.__('OperateSuccess');
    }
    this.ctx.body = Object.assign(
      {
        data,
        msg,
        isSuccess: true,
        code: 200
      },
      options
    );
  }
  /**
   * 请求失败的回调
   * @param data
   */
  error(data, msg, options?) {
    msg = msg || this.ctx.__('OperateSuccess');
    if (isPureObject(msg)) {
      options = msg;
      msg = this.ctx.__('OperateSuccess');
    }
    this.ctx.body = Object.assign(
      {
        data,
        msg,
        isSuccess: false,
        code: 200
      },
      options
    );
  }

  /**
   * 导出Excel
   * @param headerTitle
   * @param data 数据
   * @param name 导出表单名（选填）
   * @returns nodeExcel
   */
  excelExport(opt: { headerTitle: { [key: string]: any }, data: any, name?: string, options?: any }) {
    const { headerTitle, data, name, options } = opt;
    // 将数据转成Excel表横坐标展示的
    let ws: any = null;
    if (options) {
      ws = Excel.utils.json_to_sheet(data, options);
    } else {
      ws = Excel.utils.json_to_sheet(data);
    }
    const range = Excel.utils.decode_range(ws['!ref']);
    // 处理头
    for (let c = range.s.c; c <= range.e.c; c++) {
      const header = Excel.utils.encode_col(c) + '1';
      ws[header].v = headerTitle[ws[header].v];
    }
    // 创建表格
    const wb = Excel.utils.book_new();
    const fileName = name ? name : 'export';
    // 数据装载到工作薄上
    Excel.utils.book_append_sheet(wb, ws, fileName); // 最后那个是工作薄表的命名
    const buf = Excel.write(wb, {
      type: 'buffer',
      bookType: 'csv',
    });
    return buf;
  }

  /**
   * 获取分页数据
   */
  getPagingData = ({ pageIndex: pi, pageSize: ps }: { pageIndex: string, pageSize: string } | Record<string, any>) => {
    const pageIndex = (pi && parseInt(pi, 10)) || 1;
    const pageSize = (ps && parseInt(ps, 10)) || 20;
    const offset = (pageIndex - 1) * pageSize;
    return {
      limit: pageSize,
      offset
    };
  }

}
