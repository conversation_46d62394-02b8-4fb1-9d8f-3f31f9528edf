import { Context } from 'egg';
import Controller from '../base';
import { BaseData } from '../../lib/constant';
import fs from 'fs';
import util from 'util';

const readFile = util.promisify(fs.readFile);
export default class AdminController extends Controller {
  // 页面渲染
  public async index(ctx: Context) {
    const path = ctx.url.split('?')[0];
    const { config, app } = this;
    const extendData: any = { publicKey: '', user: {}, auth: {} };
    let isLogin = false;

    if (ctx.isAuthenticated()) {
      isLogin = true;
      const targetUser = await ctx.model.Authority.User.findOne({
        raw: true,
        where: {
          id: ctx.user.id,
          status: 1,
          role_type: 1
        }
      });

      // 用户被删除的话，则跳出登录
      if (!targetUser) {
        ctx.logout();
        ctx.redirect(`/login`);
        return;
      }

      // 去掉敏感信息
      delete targetUser.password;
      delete targetUser.ctime;
      delete targetUser.utime;
      delete targetUser.phone;

      if (targetUser.team_id) {
        const teamInfo = await ctx.service.mediabuy.agency.findOne({
          where: { id: targetUser.team_id }
        });
        if (teamInfo?.team) {
          targetUser.team = teamInfo.team;
        }
      }
      // console.log("targetUser: ", targetUser);
      extendData.user = targetUser;

      const userRole = await ctx.service.authority.userRole.find({
        where: {
          user_code: ctx.user.id
        },
        attributes: {
          exclude: ['ctime', 'utime', 'id', 'creator', 'modifier', 'is_delete'],
          include: [[app.Sequelize.col('crm_oms_role.name'), 'name'], [app.Sequelize.col('crm_oms_role.parent'), 'parent'], [app.Sequelize.col('crm_oms_role.remark'), 'remark'], 'role_code']
        },
        include: [
          {
            model: ctx.model.Authority.Role,
            attributes: []
          },
          {
            model: ctx.model.Authority.RoleAuth,
            attributes: {
              exclude: ['ctime', 'utime', 'id', 'creator', 'modifier', 'is_delete']
            },
            include: {
              model: ctx.model.Authority.Authority,
              where: {
                is_delete: 0
              },
              attributes: {
                exclude: ['ctime', 'utime', 'id', 'code', 'creator', 'modifier', 'is_delete']
              }
            }
          }
        ]
      });

      const roleAuth = [];
      const roleName = [];
      const authMap = {};
      const roleCode = [];
      const tempSet = new Set();
      // 权限数据处理
      userRole.forEach((role, index) => {
        const { role_code } = role;
        roleCode.push(role_code);
        role.crm_oms_role_auths.forEach((auth, i) => {
          const temp: any = {};
          Object.keys(auth.crm_oms_authority.dataValues).forEach(key => {
            temp[key] = auth.crm_oms_authority.dataValues[key];
          });
          temp.auth_code = auth.auth_code;
          temp.operate_auth = (auth.operate_auth || '').split(',');
          roleAuth.push(temp);
        });

        roleName.push({ name: role.dataValues.name, role_code: role.dataValues.role_code });
      });

      ctx.user.roleCode = roleCode;
      extendData.user.roleCode = roleCode;
      extendData.auth.userRole = roleName;

      // 由于角色可以多选，操作权限取交集
      const roleAuthGroup = roleAuth.reduce((acc, cur) => {
        if (!acc[cur.auth_code]) {
          acc[cur.auth_code] = {
            operate_auth: new Set()
          };
        }
        const operateAuth = cur.operate_auth || [];
        if (operateAuth.length) {
          operateAuth.forEach(item => {
            acc[cur.auth_code].operate_auth.add(item);
          });
        }
        return acc;
      }, {});

      extendData.auth.roleAuth = [
        ...roleAuth.filter((item, index) => {
          // 去重复
          if (!tempSet.has(item.auth_code)) {
            tempSet.add(item.auth_code);
            return true;
          } else {
            return false;
          }
        })
      ];
      extendData.auth.roleAuth.forEach(it => {
        it.operate_auth = [...roleAuthGroup[it.auth_code].operate_auth];
      });

      extendData.auth.operateAuths = extendData.auth.roleAuth.map(item => {
        return {
          authCode: item.auth_code,
          key: item.name,
          name: item.remark,
          operateAuth: item.operate_auth
        };
      });

      // 已登录，访问登录页面跳到首页
      if (path === '/REDIRECT_FROM') {
        const url = ctx.url;
        const searchParams = new URLSearchParams(url.split('?')[1]);
        const from = searchParams.get('from');
        if (from) {
          searchParams.delete('from');
          const urlParams = searchParams.toString();
          ctx.redirect(`${from}${urlParams}`);
          return;
        }
        ctx.redirect(`/`);
      }
      if (path === '/login') {
        ctx.redirect(`/`);
      }
    } else {
      ctx.session.returnTo = ctx.path;
      // 未登录，访问非登录页面跳到登录页面
      if (path !== '/login' && path !== '/page/ddlogin') {
        ctx.url.includes('?') ? ctx.redirect(`/login?${ctx.url.split('?')[1]}`) : ctx.redirect(`/login`);

      }
    }

    // 登录页面额外数据注入
    if (!extendData.publicKey) {
      extendData.publicKey = await readFile(`${config.baseDir}/app/data/rsa_1024_pub.pem`, 'utf-8');
    }
    // 登录页面额外数据注入
    if (path === '/login') {
      extendData.user = {};
    }

    const tableHostMap = [];

    if (ctx.locals.env === 'local') {
      JSON.parse(JSON.stringify(this.config.sequelize)).datasources.forEach(item => {
        const { database, customParmas } = item;
        if (database === 'information_schema' && customParmas.groupMap) {
          tableHostMap.push(customParmas.groupMap);
        }
      });
    }

    await ctx.renderClient('app.js', { url: ctx.url, isLogin, BaseData, ...extendData, tableHostMap });
  }

  async authCallback() {
    const { ctx } = this;
    if (ctx.isAuthenticated()) {
      this.success(ctx.user, ctx.__('LoginSuccess'));
    } else {
      // 账号或者密码不正确，请重试
      if (ctx.app.loginByPassword === true) {
        this.error({}, ctx.__('LoginFail'));

        // 重置
        delete ctx.app.loginByPassword;
      } else {
        this.error({}, ctx.__('LoginError'));
      }
    }
  }
}
