import Controller from '../base';
import { BaseData, AuthPrefix } from '../../lib/constant';
import { makeCode, formatAuth } from '../../lib/tool';

// 定义创建接口的请求参数规则
const createRule = {
  name: { type: 'string', required: true },
  type: { type: 'enum', values: BaseData.type.map(item => item.value), required: true }
};
export default class AuthorityController extends Controller {
  async index() {
    const { ctx, service } = this;
    const arr = await service.authority.authority.find({
      raw: true,
      where: {
        is_delete: 0
      }
    });
    const result = formatAuth(arr, 'code');
    delete result.pathList;
    this.success(result, ctx.__('FetchSuccess'));
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    let conditions = {};

    if (typeof id === 'number') {
      conditions = { id };
    } else {
      conditions = { code: id };
    }

    const result = await service.authority.authority.findOne({ where: conditions });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const code = AuthPrefix + makeCode();
    body.code = code;
    const result = await service.authority.authority.create(body);
    if (body.child) {
      service.authority.authority.update({ code: body.child }, { parent: code });
    }
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = ctx.request.body;
    const result = await service.authority.authority.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const result = await service.authority.authority.findByIdAndDelete(id, { is_delete: 1 });
    try {
      await service.genConfig.findByIdAndDelete(id);
    } catch (error) {
    }
    // 子级权限
    if (ctx.query.code) {
      service.authority.authority.update({ parent: ctx.query.code }, { is_delete: 1 });
    }
    this.success(result, ctx.__('DeleteSuccess'));
  }
}
