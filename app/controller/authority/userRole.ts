import Controller from '../base';
import { BaseData } from '../../lib/constant';

// 定义创建接口的请求参数规则
const createRule = {
  name: { type: 'string', required: true },
  type: { type: 'enum', values: BaseData.type.map(item => item.value), required: true }
};
export default class UserRoleController extends Controller {
  async index() {
    const { ctx, service } = this;
    const arr = await service.authority.userRole.find({
      attributes: { exclude: ['password'] },
      order: [
        ['ctime', 'DESC'],
        ['utime', 'DESC']
      ]
    });
    const result = arr.map(item => {
      return item;
    });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    let conditions = {};

    if (typeof id === 'number') {
      conditions = { id };
    } else {
      conditions = { code: id };
    }

    const result = await service.authority.userRole.findOne({ where: conditions });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body, query } = ctx.request;
    const removeResult = await service.authority.userRole.destroy({ user_code: query.code });
    const result = await service.authority.userRole.bulkCreate(body, {});
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    const result = await service.authority.userRole.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  async logout() {
    const ctx = this.ctx;
    ctx.logout();
    this.success({}, '登出成功');
    ctx.redirect(ctx.get('referer') || '/');
  }

  // DELETE	/xxxx/:id
  async destroy() {}
}
