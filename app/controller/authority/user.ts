import Controller from '../base';
import { BaseData, SavePath } from '../../lib/constant';
import MD5 from 'crypto-js/md5';
import NodeRSA from 'node-rsa';
import fs from 'fs';
import util from 'util';
import sequelize, { Op, QueryTypes } from 'sequelize';
import dayjs from 'dayjs';

const readFile = util.promisify(fs.readFile);
let privateKey = null;

// 定义创建接口的请求参数规则
const createRule = {
  name: { type: 'string', required: true },
  type: { type: 'enum', values: BaseData.type.map(item => item.value), required: true }
};

async function makePassword(password, app, isCrypted = true) {
  if (!privateKey) {
    privateKey = await readFile(`${app.config.baseDir}/app/data/rsa_1024_priv.pem`, 'utf-8');
  }

  const nodeRSA = new NodeRSA(privateKey);
  nodeRSA.setOptions({ encryptionScheme: 'pkcs1' });

  if (isCrypted) {
    password = nodeRSA.decrypt(password, 'utf8');
  }
  // AES
  const md5Str = MD5(password).toString();
  return md5Str;
}
export default class UserController extends Controller {
  async index() {
    const { ctx, service } = this;
    const { query } = ctx;
    const { name, crm_oms_user_roles, email, d_user_id } = query;
    const { Op } = sequelize;
    const where: any = { role_type: 1 };
    const { limit, offset } = this.getPagingData(query);

    if (name) {
      where.pub_name = {
        [Op.substring]: name
      };
    }
    if (email) {
      where.email = {
        [Op.substring]: email
      };
    }
    if (d_user_id) {
      if (d_user_id === '1') {
        where.d_user_id = {
          [Op.ne]: ''
        };
      } else if (d_user_id === '2') {
        where.d_user_id = '';
      }
    }

    const params = {
      where,
      attributes: { exclude: ['password'] },
      order: [
        ['id', 'DESC'],
      ],
      include: [
        {
          model: ctx.model.Authority.UserRole,
          attributes: {
            exclude: ['user_code', 'ctime', 'utime']
          },
          required: false,
          where: {},
          include: [
            {
              model: ctx.model.Authority.Role,
              as: 'crm_oms_role',
              attributes: { exclude: ['ctime', 'utime', 'id'] },
              required: false
            }
          ],

        }
      ],
      limit,
      offset
    };

    if (crm_oms_user_roles) {
      // omsRoleObj.required = true;
      // UserRoleObj.required = true;
      params.include[0].required = true;
      params.include[0].where = {
        role_code: crm_oms_user_roles
      };
    }
    const result = await service.authority.user.find(params);
    const total = await service.authority.user.count(params);

    this.success(result, ctx.__('FetchSuccess'), { total });
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    const conditions: Record<string, any> = {
      role_type: 1
    };

    if (typeof id === 'number') {
      conditions.id = id;
    } else {
      conditions.code = id;
    }

    const result = await service.authority.user.findOne({ where: conditions });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const pubName = body.pub_name;

    if (body.option === 'create') {
      body.password = await makePassword(body.password, ctx.app);
      delete body.option;
    }
    let isExist = await service.authority.user.findOne({ where: { pub_name: pubName } });
    if (isExist) {
      this.error(isExist, 'The username already exists');
      return;
    }
    isExist = await service.authority.user.findOne({ where: { email: body.email } });
    if (isExist) {
      this.error(isExist, 'The email already exists');
      return;
    }
    const now = dayjs().unix();
    /** 这里还差对密码进行md5操作 */
    const result = await service.authority.user.create({ ...body, role_type: 1, ctime: now, utime: now });
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    let isExist = await service.authority.user.findOne({
      where: {
        pub_name: updateData.pub_name, id: {
          [Op.ne]: id
        }
      },
      raw: true
    });
    if (isExist) {
      this.error(isExist, 'The user already exists');
      return;
    }
    isExist = await service.authority.user.findOne({
      where: {
        email: updateData.email, id: {
          [Op.ne]: id
        }
      },
      raw: true
    });
    if (isExist) {
      this.error(isExist, 'The email already exists');
      return;
    }

    const now = dayjs().unix();
    updateData.utime = now;
    const { password } = updateData;

    if (password) { updateData.password = await makePassword(password, ctx.app); }
    const result = await service.authority.user.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  async logout() {
    const ctx = this.ctx;
    ctx.logout();
    this.success({}, ctx.__('OperateSuccess'));
    ctx.redirect(`/login`);
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    // 之前版本是软删除
    const item = await service.authority.user.findOne({ where: { id }, raw: true });
    // 删除是硬删除，删除该用户
    const result = await service.authority.user.destroy({ id });
    // 删除用户分配的角色
    if (item.id) {
      await service.authority.userRole.destroy({ user_code: item.id });
    }
    this.success(result, ctx.__('DeleteSuccess'));
  }

  /**
   * 获取指定角色的用户
   */
  async getByRoleUsers() {
    const result = await this.service.authority.user.getByRoleUsers();

    this.success(result, this.ctx.__('FetchSuccess'));
  }

  async getAdsUser() {
    const adsUserRoleSql = `
    select t1.*, IF(
      COUNT(ads_user_role.role_code) = 0,
      JSON_ARRAY(),
      JSON_ARRAYAGG(ads_user_role.role_code)
    ) AS role_codes
    from (
      select id, email, lang, name, nickname from cms_auth where role in ('root', 'master', 'manager', 'user')  order by id desc
    ) as t1
    left join ads_user_role on t1.id = ads_user_role.user_code  and ads_user_role.system = 'ads'
    group by t1.id
    order by t1.id desc
  `;
    const adsUserList = await this.app.model.query(adsUserRoleSql, { type: QueryTypes.SELECT });

    const result = adsUserList.map(it => ({ label: it.name, value: it.id }));
    this.success(result, this.ctx.__('FetchSuccess'));
  }
}
