import Controller from '../base';
import { BaseData, RolePrefix } from '../../lib/constant';
import { makeCode } from '../../lib/tool';

// 定义创建接口的请求参数规则
const createRule = {
  name: { type: 'string', required: true },
  type: { type: 'enum', values: BaseData.type.map(item => item.value), required: true }
};
export default class RoleController extends Controller {
  async index() {
    const { ctx, service } = this;
    const arr = await service.authority.role.find({
      where: {
        is_delete: 0
      },
      include: [
        {
          model: ctx.model.Authority.RoleAuth,
          attributes: {
            exclude: ['ctime', 'utime']
          }
        }
      ]
    });
    const result = arr.map(item => {
      return item;
    });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    let conditions = {};

    if (typeof id === 'number') {
      conditions = { id };
    } else {
      conditions = { code: id };
    }

    const result = await service.authority.role.findOne({ where: conditions });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body } = ctx.request;
    const code = RolePrefix + makeCode();
    body.code = code;
    const result = await service.authority.role.create(body);
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    const result = await service.authority.role.update(id, updateData);
    this.success(result, ctx.__('UpdateSuccess'));
  }
  /** 获取角色名列表 */
  async roleList() {
    const { ctx, service } = this;
    const list = await service.authority.role.find({
      attributes: ['code', 'name']
    });
    this.success(list, ctx.__('FetchSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { is_delete: 1 };
    const { code } = ctx.request.query;
    const [roleResult, userRoleResult] = await ctx.model.transaction(t1 => {
      // 多个并发操作
      return Promise.all([
        service.authority.role.update(id, updateData, t1),
        service.authority.userRole.update(
          {
            role_code: code
          },
          { is_delete: 1 },
          t1
        )
      ]);
    });
    // 因为跑用例不多，所以下面判断不一定准确，可后期根据实际情况调整
    if (roleResult && userRoleResult) {
      this.success({}, ctx.__('DeleteSuccess'));
    } else {
      this.error({}, ctx.__('ServerError'));
    }
  }
}
