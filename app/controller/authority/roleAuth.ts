import Controller from '../base';
import { BaseData, OperateStatus, RolePrefix } from '../../lib/constant';
import { makeCode } from '../../lib/tool';

// 定义创建接口的请求参数规则
const createRule = {
  name: { type: 'string', required: true },
  type: { type: 'enum', values: BaseData.type.map(item => item.value), required: true }
};
export default class RoleAuthController extends Controller {
  async index() {
    const { ctx, service } = this;
    const arr = await service.authority.roleAuth.find({});
    const result = arr.map(item => {
      return item;
    });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async show() {
    const { ctx, service } = this;
    const { params } = ctx;
    const { id } = params;
    let conditions = {};

    if (typeof id === 'number') {
      conditions = { id };
    } else {
      conditions = { code: id };
    }

    const result = await service.authority.roleAuth.findOne({ where: conditions });
    this.success(result, ctx.__('FetchSuccess'));
  }

  async create() {
    const { ctx, service } = this;
    const { body, query } = ctx.request;
    const oldData = await service.authority.roleAuth.find({
      where: { role_code: query.code },
      raw: true
    });
    const payload = body.map(item => {
      const oldItem = oldData.find(i => i.auth_code === item.auth_code);
      if (oldItem) {
        return {
          ...item,
          operate_auth: oldItem.operate_auth
        };
      } else {
        return {
          ...item,
          operate_auth: ''
        };
      }
    });
    // 存在则更新，不插入
    const removeResult = await service.authority.roleAuth.destroy({ role_code: query.code });
    const result = await service.authority.roleAuth.bulkCreate(payload, { validate: true, updateOnDuplicate: ['auth_code', 'role_code'] });
    this.success(result, ctx.__('CreateSuccess'));
  }

  async update() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const updateData = { ...ctx.request.body };
    const result = await service.authority.roleAuth.update(id, updateData);
    // console.log("result:", result);
    this.success(result, ctx.__('UpdateSuccess'));
  }

  // DELETE	/xxxx/:id
  async destroy() {
    const { ctx, service } = this;
    const { id } = ctx.params;
    const result = await service.template.findByIdAndDelete(id, { is_delete: 1 });
    this.success(result, ctx.__('DeleteSuccess'));
  }

  async bulkUpdate() {
    const { ctx, service, app } = this;
    try {
      const { data, updateOnDuplicate } = ctx.request.body;
      const result = await service.authority.roleAuth.bulkUpdate(data, {
        updateOnDuplicate
      });
      if (result.status === OperateStatus.Success) {
        this.success(result.data, 'Update Successful');
      } else {
        this.error({}, 'Update Failed');
      }
    } catch (e) {
      app.coreLogger.error('[RoleAuthController => bulkUpdate]', e);
      this.error({}, e.toString());
    }
  }
}
