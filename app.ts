import assert from 'assert';
import MD5 from 'crypto-js/md5';
import NodeRS<PERSON> from 'node-rsa';
import fs from 'fs';
import util from 'util';
import path from 'path';
import { registerAppMessageBusService } from './app/modules/registerAppMessageBusService';

const readFile = util.promisify(fs.readFile);
let privateKey = null;

/**
 * 产出密码的方法
 * @param password 密码
 * @param app egg实例
 * @param isCrypted 是否是加密的密码
 */
async function makePassword(password, app, isCrypted = true) {
  if (!privateKey) {
    privateKey = await readFile(`${app.config.baseDir}/app/data/rsa_1024_priv.pem`, 'utf-8');
  }

  const nodeRSA = new NodeRSA(privateKey);
  nodeRSA.setOptions({ encryptionScheme: 'pkcs1' });

  if (isCrypted) {
    password = nodeRSA.decrypt(password, 'utf8');
  }
  // AES
  const md5Str = MD5(password).toString();
  return md5Str;
}

function formatLocale(locale) {
  // support zh_CN, en_US => zh-CN, en-US
  return locale.replace('_', '-').toLowerCase();
}

module.exports = app => {
  // 注册系统消息总线服务端部分
  registerAppMessageBusService(app);
  // multi-language cache start
  const { dirs } = app.config.i18n;
  const resources = {};
  for (let i = 0; i < dirs.length; i++) {
    try {
      if (!fs.existsSync(dirs[i])) {
        continue;
      }
      const names = fs.readdirSync(dirs[i]);
      for (let j = 0; j < names.length; j++) {
        const name = names[j];
        const filepath = path.join(dirs[i], name);
        const locale = formatLocale(name.split('.')[0]);
        resources[locale] = resources[locale] || {};
        let resource = {};
        if (name.endsWith('.json')) {
          resource = require(filepath);
          Object.assign(resources[locale], resource);
        }
      }
    } catch (error) {
      app.logger.warn('i18n config error: ', error);
      continue;
    }
  }
  app.localesCache = resources;
  // multi-language cache end

  app.passport.verify(async (ctx, user) => {
    // 创建用户信息的时需要
    const userInfo = { pub_name: '', email: '' };

    // check provider
    assert(user.provider, 'user.provider should exists');
    delete user.provider;
    // 兼容钉钉登录
    const loginType = user.username;

    user.email = user.username;
    delete user.username;

    if (loginType === 'dingtalk') {
      const { service } = ctx;

      const { appId, appSecret } = app.config[ctx.request?.body?.corp] || app.config.dingtalk;
      const { accessToken } = await service.proxy.request(
        `https://api.dingtalk.com/v1.0/oauth2/userAccessToken`,
        {
          body: JSON.stringify({
            clientId: appId,
            clientSecret: appSecret,
            code: user?.password,
            refreshToken: user?.password,
            grantType: 'authorization_code'
          })
        },
        'POST'
      );

      const dingTalkUserInfo = await service.proxy.request(`https://api.dingtalk.com/v1.0/contact/users/me`, {}, 'GET', 'application/json', {
        'x-acs-dingtalk-access-token': accessToken
      });

      const userInfoUrl = `http://${app.config.env === 'local' ? '47.88.224.134:7000' : '172.21.114.192:7000'}/team_wallet/user_info`;
      const { data, status } = await service.proxy.request(userInfoUrl, {
        unionid: dingTalkUserInfo.unionId,
        email: dingTalkUserInfo.email
      });

      // console.log(3333, accessToken, dingTalkUserInfo, data, status);

      // 离职或者非公司员工等非法用户，则跳出登录环节
      if (status !== 1) {
        return undefined;
      }

      user = {
        uid: dingTalkUserInfo.unionId
      };

      userInfo.pub_name = dingTalkUserInfo.nick;
      userInfo.email = data.email;
      // 预留 jwt 校验值
      ctx.session.jwt = data.jwt;
    } else {
      // 尝试用钉钉伪造登录，直接跳过
      // 钉钉unionid的特征
      const dingFeature = 'iEiE';
      if (loginType.length > 10 && loginType.endsWith(dingFeature)) {
        return undefined;
      }

      user.password = await makePassword(user.password, app);
    }

    let targetUser = await ctx.model.Authority.User.findOne({ where: user });
    if (!targetUser) {
      if (loginType === 'dingtalk') {
        // 调用 service 注册新用户
        user.pub_name = userInfo.email ? userInfo.email.split('@')[0] : user.uid;

        Object.assign(user, userInfo);

        // targetUser此时utime为A
        targetUser = await ctx.service.authority.user.create(user);
        // newUser此时utime为B，居然 A != B, 导致 @flat-design/egg-login-verify的时间上匹配上不
        // 解决第一次扫码登录时，登录不了的问题
        const newUser = await ctx.model.Authority.User.findOne({ where: { id: targetUser.id, role_type: 1 } });
        return newUser;
      } else {
        app.loginByPassword = true;
      }
      return undefined;
    } else if (targetUser.is_delete === 1) {
      return undefined;
    } else if (targetUser && !targetUser.email) {
      // 之前没有存 email，现在补上
      await ctx.model.Authority.User.update({ email: userInfo.email }, { where: { id: targetUser.id } });
      targetUser = await ctx.model.Authority.User.findOne({ where: user });
    }

    return targetUser;
  });

  // 将用户信息序列化后存进 session 里面，一般需要精简，只保存个别字段
  app.passport.serializeUser(async (ctx, { dataValues: user }) => {
    delete user.password;
    delete user.phone;

    delete user.custom_config;
    delete user.pub_tag;
    delete user.ctime;
    delete user.utime;
    delete user.postback_url;
    delete user.api_key;
    delete user.redirect;
    delete user.inner_pub_name;
    delete user.pub_marker;
    return user;
  });

  // 反序列化后把用户信息从 session 中取出来，反查数据库拿到完整信息
  app.passport.deserializeUser(async (ctx, user) => {
    return user;
  });
};
