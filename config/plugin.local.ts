export default {
  // 渲染插件
  reactssr: {
    package: 'egg-view-react-ssr'
  },
  // 开启mysql
  mysql: {
    enable: false,
    package: 'egg-mysql'
  },
  sequelize: {
    enable: true,
    package: 'egg-sequelize'
  },

  validate: {
    enable: true,
    package: 'egg-validate'
  },
  passport: {
    enable: true,
    package: 'egg-passport'
  },
  passportLocal: {
    enable: true,
    package: 'egg-passport-local'
  },
  postgres: {
    enable: true,
    package: 'egg-postgres'
  },
  cors: {
    package: 'egg-cors'
  },
  webpack: {
    package: 'egg-webpack'
  },
  webpackreact: {
    package: 'egg-webpack-react'
  },
  xtransit: {
    enable: false,
    package: 'egg-xtransit'
  }
};
