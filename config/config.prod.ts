/**
 * 生产环境配置
 *
 * 最终生效的配置为 prod + default（前者覆盖后者）
 */

import Memcached from 'memcached';
import elasticsearch from 'elasticsearch';
import { define } from './config.default';
import specSequelize from 'sequelize';
import clsHooked from 'cls-hooked';
import SYSTEM_CONFIG from '@flat/egg-system-config';

// 初始化命名空间
const sequelizeCLSNamespace = clsHooked.createNamespace('cls-hooked-namespace');
// @ts-ignore 使用cls命名空间
specSequelize.useCLS(sequelizeCLSNamespace);
// 更多阅读 egg-mysql和sequelize使用事务&升级指南：https://nemo.yuque.com/fe-doc/pg5rqn/hh939y

const mc = new Memcached('**********:16006', { maxKeySize: 60000, maxValue: 60000, retries: 1, failures: 2, poolSize: 15 });
const es = new elasticsearch.Client({ host: `http://************:9200/` });

export default function (app) {
  const config: any = {};
  config.logger = {
    dir: '/home/<USER>/logs/logger/ad-crm-oms'
  };
  config.customLogger = {
    scheduleLogger: {
      file: '/home/<USER>/logs/logger/ad-crm-oms/egg-schedule.log'
    }
  };

  // sequelize配置
  // config.sequelize = {
  //   dialect: 'mysql',
  //   dialectOptions: {
  //     charset: 'utf8mb4',
  //     supportBigNumbers: true,
  //     bigNumberStrings: true
  //   },
  //   host: '',
  //   port: '',
  //   username: '',
  //   password: '',
  //   database: ''
  // };

  // sequelize配置(多个数据库连接)
  config.sequelize = {
    Sequelize: specSequelize,
    datasources: [
      {
        delegate: 'model',
        baseDir: 'model',
        dialect: 'mysql',
        dialectOptions: {
          charset: 'utf8mb4',
          supportBigNumbers: true,
          bigNumberStrings: true
        },
        host: 'gray-ad-adx-center.mysql.singapore.rds.aliyuncs.com',
        port: '3306',
        username: SYSTEM_CONFIG.db_username,
        password: SYSTEM_CONFIG.db_password,
        database: 'nemo',
        timezone: '+08:00', // 设置 Sequelize 的时区为东八区
        define // 开启hooks记录log
      }
    ]
  };

  config.uploadUrl = {
    oss: 'http://***********:5003/mw/public_upload?sign=n_e_m_o'
  };

  // 代理 服务端接口地址(内网IP)
  config.all_host = {
    server: `http://************:8729`,
    ai_public_server: 'http://**************:6583'
  };

  config.dataHost = { mc, es };
  config.pbApiHost = 'http://*************:9910';

  config.redis = {
    clients: {
      pub: {
        port: 10501, // Redis port
        host: '************', // Redis host
        password: '',
        db: 0
      },
      sub: {
        port: 10501, // Redis port
        host: '************', // Redis host
        password: '',
        db: 1
      },
      common: {
        port: 10501, // Redis port
        host: '************', // Redis host
        password: '',
        db: 2
      }
    }
  };

  // 给钉钉操作使用的项目地址
  config.DD_USE_PROJECT_URL = 'https://crm.mobshark.net';
  return config;
}
