import path from 'path';
import fs from 'fs';

/**
 * 从私有npm仓库加载的egg插件
 */
const omsPluginConfig: any = {
  eggPlugin: {
    enable: true,
    package: '@flat/egg-plugin' // 引用npm包插件
    // path: `${sourcePluginDir}/plugin`, // 引用本地插件
  }
};

export default {
  ...omsPluginConfig,
  flatLoginVerify: {
    enable: false,
    package: '@flat-design/egg-login-verify'
  },
  i18n: {
    enable: true,
    package: 'egg-i18n',
  },
  eggSSE: {
    enable: true,
    package: '@fe-design/egg-sse'
  },
  eggRedis: {
    enable: true,
    package: 'egg-redis'
  },
  eggSystemUpdateNotification: {
    enable: true,
    // 未知问题，egg竟无法自动识别，只好手动引入
    path: path.join(process.cwd(), 'node_modules', '@fe-design/egg-system-update-notification')
  },
};
