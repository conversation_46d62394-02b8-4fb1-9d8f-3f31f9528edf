import { EggAppConfig } from 'egg';
import path from 'path';
import Memcached from 'memcached';
import elasticsearch from 'elasticsearch';
import { define } from './config.default';
import specSequelize from 'sequelize';
import clsHooked from 'cls-hooked';
import SYSTEM_CONFIG from '@flat/egg-system-config';
const os = require('os');

// 初始化命名空间
const sequelizeCLSNamespace = clsHooked.createNamespace('cls-hooked-namespace');
// @ts-ignore 使用cls命名空间
specSequelize.useCLS(sequelizeCLSNamespace);
// 更多阅读 egg-mysql和sequelize使用事务&升级指南：https://nemo.yuque.com/fe-doc/pg5rqn/hh939y

const mc = new Memcached('*************:16006', { maxKeySize: 60000, maxValue: 60000, retries: 1, failures: 2, poolSize: 15 });
const es = new elasticsearch.Client({ host: `http://************:8200/` });

function getLocalIP() {
  const networkInterfaces = os.networkInterfaces();
  for (const name of Object.keys(networkInterfaces)) {
    for (const inter of networkInterfaces[name]) {
      const { address, family, internal } = inter;
      if (family === 'IPv4' && !internal) {
        return address;
      }
    }
  }
  return 'localhost';
}

export default (app: EggAppConfig) => {
  const config: any = {};

  config.view = {
    cache: 0
  };

  config.static = {
    maxAge: 0 // maxAge 缓存，默认 1 年
  };

  config.development = {
    overrideDefault: true,
    watchDirs: ['app', 'plugin', 'app.ts', 'agent.ts', 'config/locale'], // 指定监视的目录（包括子目录），当目录下的文件变化的时候自动重载应用，路径从项目根目录开始写
    ignoreDirs: ['app/web', 'public', 'config/manifest.json'] // 指定过滤的目录（包括子目录）
  };

  config.logview = {
    dir: path.join(app.baseDir, 'logs')
  };

  // sequelize配置(多个数据库连接)
  config.sequelize = {
    Sequelize: specSequelize,
    datasources: [
      {
        delegate: 'model',
        baseDir: 'model',
        dialect: 'mysql',
        dialectOptions: {
          charset: 'utf8mb4',
          supportBigNumbers: true,
          bigNumberStrings: true
        },
        host: 'rm-gs509ga2ae1l0yx3ydo.mysql.singapore.rds.aliyuncs.com', // 公网域名, 通测试环境： adx-center.mysql.singapore.rds.aliyuncs.com
        port: 3306,
        username: SYSTEM_CONFIG.db_username,
        password: SYSTEM_CONFIG.db_password,
        database: 'nemo',
        timezone: '+08:00', // 设置 Sequelize 的时区为东八区
        define // 开启hooks记录log
      }

    ]
  };

  config.uploadUrl = {
    oss: 'http://*************:5003/mw/public_upload?sign=n_e_m_o'
  };

  config.dataHost = { mc, es };
  config.pbApiHost = 'http://dev.socialdev.cc';

  config.i18n = {
    defaultLocale: 'en',
    queryField: 'lang',
    cookieField: 'lang',
    cookieMaxAge: '604800000' // 7d
  };

  config.all_host = {
    server: `http://*************:8729`,
    ai_public_server: 'http://************:6583'
  };

  config.webpack = {
    port: 9100
  };

  const localIP = getLocalIP();
  config.DD_USE_PROJECT_URL = `http://${localIP}:6600`;

  return config;
};
