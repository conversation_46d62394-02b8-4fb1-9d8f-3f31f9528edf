import path from 'path';
import fs from 'fs';
import { EggAppConfig } from 'egg';

export default function (app: EggAppConfig) {
  const exports: any = {};

  exports.siteFile = {
    '/favicon.ico': fs.readFileSync(path.join(app.baseDir, 'app/web/asset/images/favicon.ico')),
    '/logo.png': fs.readFileSync(path.join(app.baseDir, 'app/web/asset/images/logo.png')),
    '/static-demo.txt': fs.readFileSync(path.join(app.baseDir, 'app/web/asset/static/static-demo.txt'))
  };

  exports.logger = {
    consoleLevel: 'DEBUG',
    dir: path.join(app.baseDir, 'logs')
  };

  exports.static = {
    prefix: '/public/',
    dir: path.join(app.baseDir, 'public')
  };

  exports.keys = '123456';

  exports.middleware = ['authorize', 'locals', 'access', 'errorCatch'];

  exports.notfound = {
    pageUrl: '/'
  };

  exports.security = {
    csrf: {
      ignore: '/api/clue/disparkCreate',
      headerName: 'X-XSRF-TOKEN' // 通过 header 传递 CSRF token 的默认字段为 x-csrf-token
    }
  };

  exports.reactssr = {
    layout: path.join(app.baseDir, 'app/web/view/layout.html')
  };

  // if any files need rotate by file size, config here
  exports.logrotator = {
    maxDays: 3 // keep max days log files, default is `31`. Set `0` to keep all logs
  };

  exports.passportLocal = {};

  exports.onerror = {
    all(err, ctx) {
      // 在此处定义针对所有响应类型的错误处理方法
      // 注意，定义了 config.all 之后，其他错误处理方法不会再生效
      ctx.service.onerror.index(err);
    }
  };

  exports.flatLoginVerify = {};

  exports.session = {
    key: 'EGG_SESS',
    maxAge: 24 * 3600 * 1000 * 7, // 7 天
    httpOnly: true,
    encrypt: true,
    renew: true // 发现当用户 Session 的有效期仅剩下最大有效期一半的时候，重置 Session 的有效期
  };

  exports.dingtalk = {
    appId: 'dingvbskkfhs3b8mocqz',
    appSecret: 'Fzu4sygnZc0ZYtXgJt3hr3nY-13ju-M4rtk7VPFpUsusFbMNgGe6BCc5-w3C6CRa',
    url: '', // 常规操作提示 'https://oapi.dingtalk.com/robot/send?access_token=993b4acc2b000e6bf94021658c43571dce24d9510a982597548a43fdd1628a97',
    env_test_url: 'https://oapi.dingtalk.com/robot/send?access_token=118b5e361afe19738693385846b0f0a4c4c05e7bceb77c0f2529d2120a6ac811',
    env_prod_url: 'https://oapi.dingtalk.com/robot/send?access_token=2af446755561c7dff18ecd0740d7065b86b2b449b739336cc7d21c82a2637ab1'
  };
  // 海外人员公司组织架构钉钉配置
  exports.dingtalkFlat1Quator = {
    appId: 'dingkquxm6h7mvdlcmuy',
    appSecret: 'iRVfC7C_9umkaXDR3ts3V9w5NGj1rwZaoAlKgnYZeHGBEYCGi_Ro8-Rq20-SqdJn'
  };

  exports.flatPermissionMonitor = {
    userRoleRouter: '/api/userRole', // 给用户添加角色权限的接口
    roleRouter: '/api/roleAuth', // 修改角色权限点的接口
    dingtalkUrl: '', // 钉钉机器人地址
    verifyUser: true, // 是否开启用户离职自动删除定时任务
    isCamelCase: false, // 表名的字段是否用驼峰命名,新的模板默认是下划线命名eg:oms_user_role表中 user_code，旧的OMS默认是驼峰命名oms_user_role 表中是userCode
    projectName: 'OMS' // 项目名字，默认OMS
  };

  exports.multipart = {
    fileExtensions: [
      '.apk',
      '.txt',
      '.ttc',
      '.woff',
      '.woff2',
      '.ico',
      '.bundle',
      '.xlsx',
      '.xls',
      '.doc',
      '.docx',
      '.csv',
      '.pdf',
      '.rar'
    ], // 增加对 其他的扩展名 扩展名的文件支持
    // 单个文件大小
    fileSize: '50mb',
    // 允许上传的最大文件数
    files: 20
  };

  // config/config.default.js
  exports.i18n = {
    defaultLocale: 'en',
    queryField: 'lang',
    cookieField: 'lang',
    cookieMaxAge: '604800000' // 7d
  };

  exports.webpack = {
    browser: 'http://127.0.0.1:6600'
  };

  // @flat/egg-redis
  exports.redis = {
    enable: true,
    clients: {
      pub: {
        port: 6379, // Redis port
        host: '127.0.0.1', // Redis host
        password: '',
        db: 0
      },
      sub: {
        port: 6379, // Redis port
        host: '127.0.0.1', // Redis host
        password: '',
        db: 1
      },
      common: {
        port: 6379, // Redis port
        host: '127.0.0.1', // Redis host
        password: '',
        db: 2
      }
    }
  };

  // @fe-design/egg-sse
  exports.sse = {
    enable: true,
    paths: ['/api/system-update-notification/connect']
  };

  // @fe-design/egg-system-update-notification
  exports.sun = {
    enable: true,
    redisChannel: 'flat-ads-crm-oms-system-update'
  };

  /** Google翻译api */
  exports.translateApi = 'http://api.apk.vidmate.net/api/prism/google/translate';

  // exports.xtransit = {
  //   server: 'ws://************:9090',
  //   appId: 'xxxxxxxxxxx',
  //   appSecret: 'xxxxxxxxxxxxxxxxxxx'
  // };

  exports.DD_WARNING_ROBOT = 'https://oapi.dingtalk.com/robot/send?access_token=cf1fd993878f8c2f30e51ddcf137b2282c2ae464f9405cff786dce19cec85e76';
  exports.DD_WARNING_ROBOT_SECRET = 'SECa347e30b8fb3b8c93b91ab689681bca43da6f7844b3fb264b616c141f93708d0';
  return exports;
}

export const define = {
  // 开启hooks记录log
  hooks: {
    afterBulkCreate: (model, options) => {
      record('bulk-create', model, options);
    },
    afterCreate: (model, options) => {
      record('create', model, options);
    },
    afterUpdate: async (model, options) => {
      record('update', model, options);
    },
    afterDestroy: async (model, options) => {
      record('destroy', model, options);
    }
  }
};

export function record(action, model, options) {
  // 是否批量
  const isBulk = Array.isArray(model);
  const singleModel = isBulk ? model[0] : model;
  const tableName = model.constructor.name;
  const { egg } = options;
  if (egg && tableName !== 'crm_oms_operate_log') {
    const { service, ctx } = egg;
    const ip = ctx.get('X-Real-IP') || ctx.ip;
    const pathname = ctx.headers.pathname;
    let url = ctx.url;
    let { dataValues: afterData, _previousDataValues: beforeData } = singleModel;
    const { newUrl } = singleModel;
    const { pub_name } = ctx.user || {};
    const operator = pub_name;

    if (newUrl) {
      url = newUrl;
    }
    if (isBulk) {
      afterData = model.map(item => item.dataValues);
      beforeData = model.map(item => item._previousDataValues);
    }

    // 构造数据
    const data = {
      // 因为bulkCreate 是批量的 id, 所以这里填充一个0值
      item_id: afterData.id || 0,
      action,
      record: JSON.stringify(afterData),
      old_record: JSON.stringify(beforeData),
      table_name: tableName,
      operator,
      uid: pub_name,
      url,
      pathname,
      ip,
      ctime: new Date()
    };
    // console.log('#######:', data);
    service.operateLog.create(data);
  }
}
