"use strict";
// https://www.yuque.com/easy-team/egg-react/config
const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const resolve = filepath => path.resolve(__dirname, filepath);
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin; // 查看编译后文件大小
const commonScssFile = [resolve("app/web/asset/style/common/variables.scss"), resolve("app/web/asset/style/common/mixins.scss")];
const SystemVersionLoggerPlugin = require("@fe-design/egg-system-update-notification/webpack-plugins/system-version-logger");

const entry = {
  app: "app/web/page/app.tsx",
};

module.exports = {
  target: "web",
  entry,
  port: 9100,
  module: {
    rules: [
      {
        less: {
          include: [resolve("app/web"), resolve("node_modules"), resolve("plugin")],
          options: {
            javascriptEnabled: true,
            modifyVars: {
              "primary-color": "#2d83fd",
              "link-color": "#2d83fd",
              "border-radius-base": "4px",
            },
          },
        },
      },
      {
        typescript: true,
      },
      {
        scss: {
          enable: true,
          test: /\.(css|scss)$/,
          include: [path.resolve(__dirname, "app/web"), path.resolve(__dirname, "plugin"), path.resolve(__dirname, "node_modules/@flat")],
          use: [
            "css-loader",
            "sass-loader",
            {
              loader: "sass-resources-loader",
              options: {
                resources: commonScssFile,
              },
            },
          ],
        },
      },
      {
        test: /\.jsx?$/,
        include: [path.resolve(__dirname, "node_modules/@flat")],
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-react"],
          },
        },
      },
    ],
  },
  plugins: [
    { clean: false },
    { imagemini: false },
    {
      copy: [{ from: "app/web/asset/lib", to: "lib" }], // 静态文件
    },
    new SystemVersionLoggerPlugin(),
    // 查看文件体积
    // new BundleAnalyzerPlugin(),
  ],
  optimization: {
    splitChunks: {
      cacheGroups: {
        // antIcons: {
        //   name: 'ant-icons',
        //   test: /[\\/]node_modules[\\/]@ant-design[\\/]icons[\\/]/,
        //   priority: -10,
        //   reuseExistingChunk: true
        // },
        iconsSvg: {
          name: "icons-svg",
          test: /[\\/]node_modules[\\/]@ant-design[\\/]icons-svg[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -20,
          reuseExistingChunk: true,
        },
        styles: {
          minChunks: 4,
        },
      },
    },
    minimize: true,
    minimizer: [
      new TerserPlugin({
        cache: true,
        parallel: true,
        sourceMap: true, // 如果在生产环境中使用 source-maps，必须设置为 true
        terserOptions: {
          // https://github.com/webpack-contrib/terser-webpack-plugin#terseroptions
        },
      }),
    ],
  },
  externals: {
    react: "React",
    "react-dom": "ReactDOM",
    axios: "axios",
    // antd: 'antd',
    moment: "moment",
    lodash: "_",
  },
  resolve: {
    alias: {
      "@": resolve("app/web"),
      request: resolve("app/web/modules/request.ts"),
      "@asset": resolve("app/web/asset"),
      "@@": resolve("app"),
      "@@@": resolve(""),
    },
  },
};
